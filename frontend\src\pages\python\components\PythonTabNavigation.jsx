import React from "react";
import { motion } from "framer-motion";

const PythonTabNavigation = ({ mainContent, currentContent }) => {
  const navItems = [
    { id: "Introduction", icon: "fas fa-book-open", label: "Introduction" },
    { id: "Chapter", icon: "fas fa-book", label: "Chapters" },
    { id: "CaseStudy", icon: "fas fa-laptop-code", label: "Case Studies" },
    { id: "FAQS", icon: "fas fa-question-circle", label: "FAQs" }
  ];

  return (
    <div className="bg-gradient-to-r from-gray-50 to-blue-50 shadow-lg rounded-xl mb-8 py-4 px-6 border border-blue-100 mt-24">
      <div className="flex justify-center flex-wrap">
        {navItems.map((item) => (
          <motion.button
            key={item.id}
            whileHover={{ scale: 1.05, y: -3 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => mainContent(item.id)}
            className={`flex items-center px-6 py-3 m-2 rounded-lg shadow-md ${
              currentContent === item.id
                ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium"
                : "bg-white text-gray-700 hover:bg-gray-100 border border-gray-200"
            } transition-all duration-300 ease-in-out`}
          >
            <i className={`${item.icon} mr-3 text-xl ${currentContent === item.id ? "text-white" : "text-blue-500"}`}></i>
            <span>{item.label}</span>
          </motion.button>
        ))}
      </div>
    </div>
  );
};

export default PythonTabNavigation;
