import { motion } from "framer-motion";
import { Link } from "react-router-dom";

const SystemDesignHeroNew = ({ showPremiumOverlay }) => {
  return (
    <>
      {/* Complex animated background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-10 blur-3xl"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 80, 0],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-10 blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.sin(i) * 20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              delay: i * 0.5,
            }}
            className={`absolute w-2 h-2 bg-blue-300 rounded-full`}
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + i * 10}%`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto pt-20 pb-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="text-center lg:text-left">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 bg-blue-500/20 border border-blue-400/30 rounded-full text-blue-300 text-sm font-medium mb-6 backdrop-blur-sm"
            >
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              🏗️ Master System Design Interviews
            </motion.div>

            <motion.h1
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight"
            >
              Design{" "}
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Scalable
              </span>
              <br />
              Build{" "}
              <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                Systems
              </span>
            </motion.h1>

            <motion.p
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="text-xl md:text-2xl mb-8 text-white max-w-2xl mx-auto lg:mx-0 leading-relaxed"
            >
              Master system design interviews with comprehensive guides, real-world examples, and expert-level problem-solving approaches from industry veterans.
            </motion.p>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center mb-12"
            >
              <motion.div
                whileHover={{
                  scale: 1.05,
                }}
                whileTap={{ scale: 0.95 }}
              >
                <button
                  onClick={showPremiumOverlay}
                  className="inline-flex items-center px-8 py-4 bg-[#303246]/60 backdrop-blur-lg text-white font-semibold rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/10"
                >
                  <span className="relative z-10">Start Practicing</span>
                  <motion.span
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="ml-2 relative z-10"
                  >
                    →
                  </motion.span>
                </button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="/GG_75"
                  className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-xl hover:bg-white/10 hover:border-white/50 transition-all duration-300 backdrop-blur-sm"
                >
                  <span className="mr-2">▶</span>
                  View Examples
                </Link>
              </motion.div>
            </motion.div>

            {/* Trust indicators */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.9 }}
              className="flex flex-wrap justify-center lg:justify-start items-center gap-6 text-sm text-gray-400"
            >
              <div className="flex items-center gap-2">
                <span className="text-yellow-400">⭐⭐⭐⭐⭐</span>
                <span className="text-white">Expert Level</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span className="text-white">Real Interview Questions</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-400">🏗️</span>
                <span className="text-white">Architecture Patterns</span>
              </div>
            </motion.div>
          </div>

          {/* Right Column - Interactive System Design Diagram */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* System Design Diagram Mockup */}
            <div className="bg-[#1e293b]/30 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden shadow-2xl">
              <div className="flex items-center gap-2 px-4 py-3 bg-[#1e293b]/50 border-b border-white/10">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="ml-2 text-xs text-gray-400">
                  system-design.arch
                </span>
              </div>
              <div className="p-6">
                {/* System Design Visual */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2 }}
                  className="space-y-4"
                >
                  {/* Load Balancer */}
                  <div className="flex items-center justify-center">
                    <div className="bg-blue-500/20 border border-blue-400/30 rounded-lg px-4 py-2 text-blue-300 text-sm">
                      Load Balancer
                    </div>
                  </div>
                  
                  {/* Arrow */}
                  <div className="flex justify-center">
                    <div className="text-gray-400">↓</div>
                  </div>
                  
                  {/* Application Servers */}
                  <div className="flex justify-center gap-4">
                    {[1, 2, 3].map((i) => (
                      <motion.div
                        key={i}
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 2, delay: i * 0.5, repeat: Infinity }}
                        className="bg-green-500/20 border border-green-400/30 rounded-lg px-3 py-2 text-green-300 text-xs"
                      >
                        App {i}
                      </motion.div>
                    ))}
                  </div>
                  
                  {/* Arrow */}
                  <div className="flex justify-center">
                    <div className="text-gray-400">↓</div>
                  </div>
                  
                  {/* Database Cluster */}
                  <div className="flex justify-center gap-4">
                    <div className="bg-purple-500/20 border border-purple-400/30 rounded-lg px-3 py-2 text-purple-300 text-xs">
                      Primary DB
                    </div>
                    <div className="bg-purple-500/10 border border-purple-400/20 rounded-lg px-3 py-2 text-purple-300 text-xs">
                      Replica DB
                    </div>
                  </div>
                  
                  {/* Cache Layer */}
                  <div className="flex justify-end">
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 3, repeat: Infinity }}
                      className="bg-orange-500/20 border border-orange-400/30 rounded-lg px-3 py-2 text-orange-300 text-xs"
                    >
                      Redis Cache
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Floating System Design Image */}
            <motion.div
              animate={{
                y: [0, -10, 0],
                rotate: [0, 2, -2, 0],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="absolute -top-6 -right-6 w-24 h-24 bg-[#1e293b]/30 backdrop-blur-lg rounded-xl border border-white/10 flex items-center justify-center"
            >
              <img
                src="/images/systemdesignlogo-.png"
                alt="System Design"
                className="w-16 h-16 object-contain rounded-lg"
              />
            </motion.div>
          </motion.div>
        </div>

        {/* Enhanced Stats Section */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.0 }}
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            {
              number: "15+",
              label: "System Designs",
              icon: "🏗️",
              color: "from-blue-400 to-purple-500",
            },
            {
              number: "100+",
              label: "Interview Questions",
              icon: "⚡",
              color: "from-green-400 to-blue-500",
            },
            {
              number: "5K+",
              label: "Engineers Helped",
              icon: "👨‍💻",
              color: "from-purple-400 to-pink-500",
            },
            {
              number: "98%",
              label: "Success Rate",
              icon: "🎯",
              color: "from-yellow-400 to-red-500",
            },
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 1.2 + index * 0.1, duration: 0.5 }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="bg-[#1e293b]/20 backdrop-blur-lg border border-white/10 rounded-xl p-6 text-center shadow-lg"
            >
              <div className="text-3xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-bold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                {stat.number}
              </div>
              <div className="text-sm text-blue-100/80">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </>
  );
};

export default SystemDesignHeroNew;