import { Link } from "react-router-dom";

const NavigationButtons = () => {
  const buttons = [
    {
      title: "Data Structures & Algorithms",
      description: "Master essential DSA concepts and problem-solving techniques",
      icon: "fas fa-code",
      emoji: "👨‍💻",
      svgIcon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="white" className="w-8 h-8">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      color: "bg-blue-600",
      link: "/data_strut",
    },
    {
      title: "System Design",
      description: "Learn to design scalable, reliable and maintainable systems",
      icon: "fas fa-sitemap",
      emoji: "🏗️",
      svgIcon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="white" className="w-8 h-8">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      color: "bg-purple-600",
      link: "/sys_des_for_int",
    },
    {
      title: "SQL Practice",
      description: "Sharpen your database querying and optimization skills",
      icon: "fas fa-database",
      emoji: "🗄️",
      svgIcon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="white" className="w-8 h-8">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
        </svg>
      ),
      color: "bg-green-600",
      link: "/sql_50",
    },
    {
      title: "JavaScript Challenge",
      description: "30 days of JavaScript coding challenges to boost your skills",
      icon: "fab fa-js",
      emoji: "🟨",
      svgIcon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24" className="w-8 h-8">
          <path d="M3 3h18v18H3V3zm16.525 13.707c-.131-.821-.666-1.511-2.252-2.155-.552-.259-1.165-.438-1.349-.854-.068-.248-.078-.382-.034-.529.113-.484.687-.629 1.137-.495.293.09.563.315.732.676.775-.507.775-.507 1.316-.844-.203-.314-.304-.451-.439-.586-.473-.528-1.103-.798-2.126-.775l-.528.067c-.507.124-.991.395-1.283.754-.855.968-.608 2.655.427 3.354 1.023.765 2.521.933 2.712 1.653.18.878-.652 1.159-1.475 1.058-.607-.136-.945-.439-1.316-1.002l-1.372.788c.157.359.337.517.607.832 1.305 1.316 4.568 1.249 5.153-.754.021-.067.18-.528.056-1.237l.034.049zm-6.737-5.434h-1.686c0 1.453-.007 2.898-.007 4.354 0 .924.047 1.772-.104 2.033-.247.517-.886.451-1.175.359-.297-.146-.448-.349-.623-.641-.047-.078-.082-.146-.095-.146l-1.368.844c.229.473.563.879.994 1.137.641.383 1.502.507 2.404.305.588-.17 1.095-.519 1.358-1.059.384-.697.302-1.553.299-2.509.008-1.541 0-3.083 0-4.635l.003-.042z"/>
        </svg>
      ),
      color: "bg-yellow-500",
      link: "/30_days_js",
    },
  ];

  return (
    <section className="py-10">
      {/* Navigation heading */}
      
      <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">
        Explore Our Learning Paths
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {buttons.map((button, index) => (
          <Link key={index} to={button.link}>
            <div
              className={`${button.color} h-full rounded-xl p-6 text-white shadow-lg hover:shadow-2xl transition-all duration-300 flex flex-col hover:-translate-y-1`}
            >
              <div className="bg-white bg-opacity-20 rounded-full w-14 h-14 flex items-center justify-center mb-4">
                {/* Direct SVG icons with improved visibility */}
                {button.svgIcon}
              </div>
              <h3 className="text-xl font-semibold mb-2">{button.title}</h3>
              <p className="text-white text-opacity-80 mb-6 flex-grow">
                {button.description}
              </p>
              <div className="flex justify-end mt-auto">
                <span className="flex items-center gap-1 font-medium">
                  Explore
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" className="ml-1">
                    <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
                  </svg>
                </span>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </section>
  );
};

export default NavigationButtons;
