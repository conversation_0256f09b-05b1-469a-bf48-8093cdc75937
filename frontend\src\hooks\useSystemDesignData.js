const useSystemDesignData = () => {
  const systemDesignQuestions = [
    {
      level: "Beginner Level",
      icon: "fas fa-leaf",
      badgeClass: "difficulty-easy",
      badgeText: "Easy",
      questions: [
        {
          title: "Design a URL Shortening Service (like TinyURL)",
          tags: ["Hash Functions", "Database", "Rate Limiting"],
          keyPoints: [
            "URL shortening and redirection",
            "Handling collisions in hash generation",
            "Analytics and tracking",
          ],
        },
        {
          title: "Design a Parking Lot System",
          tags: ["OOP Design", "Database Schema", "Concurrency"],
          keyPoints: [
            "Multiple parking spot types",
            "Payment processing",
            "Spot allocation strategy",
          ],
        },
      ],
    },
    {
      level: "Intermediate Level",
      icon: "fas fa-fire",
      badgeClass: "difficulty-medium",
      badgeText: "Medium",
      questions: [
        {
          title: "Design Instagram",
          tags: ["CDN", "Media Storage", "Feed Generation"],
          keyPoints: [
            "Photo storage and distribution",
            "News feed implementation",
            "Social graph data model",
          ],
        },
        {
          title: "Design Twitter",
          tags: ["Real-time", "Feed Generation", "Social Graph"],
          keyPoints: ["Timeline generation", "Tweet fanout", "Trending topics"],
        },
      ],
    },
    {
      level: "Advanced Level",
      icon: "fas fa-bolt",
      badgeClass: "difficulty-hard",
      badgeText: "Hard",
      questions: [
        {
          title: "Design Google Drive",
          tags: ["File Storage", "Sync", "Collaboration"],
          keyPoints: [
            "File storage and synchronization",
            "Conflict resolution",
            "Permission management",
          ],
        },
        {
          title: "Design a Global Video Streaming Platform",
          tags: ["Video Processing", "CDN", "Global Scale"],
          keyPoints: [
            "Video transcoding and storage",
            "Global content delivery",
            "Adaptive bitrate streaming",
          ],
        },
      ],
    },
  ];

  const interviewChecklist = [
    {
      question: "Design a URL shortener",
      answer:
        "Use a key-value database like Redis to store shortened URL keys and map them to original URLs.",
    },
    {
      question: "Design a cache system",
      answer:
        "Use a caching layer like Redis or Memcached to store frequently accessed data for faster access.",
    },
    {
      question: "Design a load balancer",
      answer:
        "Distribute incoming network traffic across multiple servers to ensure availability and reliability of applications.",
    },
    {
      question: "Design a message queue system",
      answer:
        "Use a system like RabbitMQ or Kafka to manage and store messages between services, ensuring data is transmitted efficiently.",
    },
    {
      question: "Design a distributed file storage system",
      answer:
        "Use systems like HDFS or Amazon S3 to store data across multiple servers, ensuring durability and accessibility.",
    },
    {
      question: "Design a rate limiter",
      answer:
        "Implement a system to limit the number of requests a user can make in a specific time period to protect services from overload.",
    },
    {
      question: "Design an API rate limiting mechanism",
      answer:
        "Use token buckets or leaky bucket algorithms to control API call limits and maintain performance.",
    },
    {
      question: "Design a notification system",
      answer:
        "Use message queues and services like Firebase or Twilio to send notifications to users via various channels.",
    },
    {
      question: "Design a social media feed",
      answer:
        "Create a system that ranks, filters, and displays user-generated content using caching, ranking algorithms, and pagination.",
    },
    {
      question: "Design an online code editor",
      answer:
        "Build a system that compiles and executes code in a sandboxed environment, ensuring security and performance.",
    },
    {
      question: "Design a search engine",
      answer:
        "Use indexing, ranking algorithms, and crawlers to efficiently retrieve and display relevant search results.",
    },
    {
      question: "Design a video streaming platform",
      answer:
        "Utilize CDN, adaptive streaming, and buffering techniques to deliver video content with low latency and high availability.",
    },
    {
      question: "Design a payment gateway system",
      answer:
        "Use a secure system with payment processors, fraud detection, and proper handling of sensitive user information.",
    },
    {
      question: "Design a database sharding mechanism",
      answer:
        "Distribute large databases across multiple servers based on keys to improve performance and scalability.",
    },
    {
      question: "Design a real-time chat application",
      answer:
        "Use WebSockets for instant communication, message queues for load management, and databases to store message history.",
    },
  ];

  return {
    systemDesignQuestions,
    interviewChecklist
  };
};

export default useSystemDesignData;
