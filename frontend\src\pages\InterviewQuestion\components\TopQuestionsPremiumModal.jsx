import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const TopQuestionsPremiumModal = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 100 },
    visible: { opacity: 1, scale: 1, y: 0, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, scale: 0.8, y: 100, transition: { duration: 0.2 } }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  return (
    <AnimatePresence>
      <motion.div
        variants={backdropVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="bg-gradient-to-br from-[#1a3c50] to-[#010509] border border-white/20 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 bg-gradient-to-r from-[#1a3c50] to-[#010509] border-b border-white/10 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold text-white mb-2">🚀 Upgrade to Interview Premium</h2>
                <p className="text-white/70">Ace your technical interviews with premium features</p>
              </div>
              <button
                onClick={onClose}
                className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white text-xl transition-all duration-200"
              >
                ×
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* Premium Features */}
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                <div className="text-3xl mb-4">🎯</div>
                <h4 className="text-xl font-bold text-white mb-3">Company-Specific Questions</h4>
                <p className="text-white/70 leading-relaxed">Access exclusive questions from Google, Amazon, Microsoft, Meta, and 50+ top companies.</p>
              </div>
              
              <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                <div className="text-3xl mb-4">🎭</div>
                <h4 className="text-xl font-bold text-white mb-3">Real Mock Interviews</h4>
                <p className="text-white/70 leading-relaxed">Practice with live mock interviews conducted by engineers from top tech companies.</p>
              </div>
              
              <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                <div className="text-3xl mb-4">📊</div>
                <h4 className="text-xl font-bold text-white mb-3">Performance Analytics</h4>
                <p className="text-white/70 leading-relaxed">Track your progress with detailed analytics and personalized improvement recommendations.</p>
              </div>
              
              <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                <div className="text-3xl mb-4">👨‍💼</div>
                <h4 className="text-xl font-bold text-white mb-3">Career Coaching</h4>
                <p className="text-white/70 leading-relaxed">Get one-on-one career coaching and interview strategy sessions with industry experts.</p>
              </div>
            </div>

            {/* Pricing */}
            <div className="bg-gradient-to-r from-orange-500/20 to-red-500/20 backdrop-blur-lg border border-white/10 rounded-xl p-8 text-center">
              <h3 className="text-2xl font-bold text-white mb-4">Choose Your Plan</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white/5 border border-white/10 rounded-xl p-6">
                  <h4 className="text-xl font-bold text-white mb-2">Interview Pro</h4>
                  <div className="text-3xl font-bold text-orange-400 mb-4">₹149/month</div>
                  <ul className="text-left space-y-2 text-white/80 mb-6">
                    <li>✓ All company questions</li>
                    <li>✓ Video solutions</li>
                    <li>✓ Progress tracking</li>
                    <li>✓ Interview simulator</li>
                  </ul>
                  <button className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 rounded-lg font-bold hover:from-orange-600 hover:to-orange-700 transition-all duration-300">
                    Get Pro
                  </button>
                </div>
                
                <div className="bg-white/5 border-2 border-red-400 rounded-xl p-6 relative">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                  <h4 className="text-xl font-bold text-white mb-2">Interview Expert</h4>
                  <div className="text-3xl font-bold text-red-400 mb-4">₹299/month</div>
                  <ul className="text-left space-y-2 text-white/80 mb-6">
                    <li>✓ Everything in Pro</li>
                    <li>✓ Live mock interviews</li>
                    <li>✓ Career coaching</li>
                    <li>✓ Interview feedback</li>
                  </ul>
                  <button className="w-full bg-gradient-to-r from-red-500 to-pink-500 text-white py-3 rounded-lg font-bold hover:from-red-600 hover:to-pink-600 transition-all duration-300">
                    Get Expert
                  </button>
                </div>
              </div>
              
              <p className="text-white/70 mt-6">🔒 30-day money-back guarantee • Cancel anytime</p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TopQuestionsPremiumModal;
