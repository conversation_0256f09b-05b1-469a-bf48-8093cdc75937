import { motion } from "framer-motion";

const InterviewChecklistNew = ({ interviewChecklist, showPremiumOverlay }) => {
  return (
    <section className="py-10">
      <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-lg border border-white/10 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-[#303246]/60 backdrop-blur-lg text-green-400 p-3 rounded-lg border border-white/10">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-white">
            {interviewChecklist.title}
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
          {interviewChecklist.items.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors"
            >
              <div className={`mt-0.5 flex-shrink-0 w-5 h-5 rounded-full border-2 flex items-center justify-center ${item.checked ? 'bg-green-500 border-green-500' : 'border-gray-400'}`}>
                {item.checked && (
                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                  </svg>
                )}
              </div>
              <span className={`text-gray-200 ${item.checked ? 'font-medium' : ''}`}>{item.text}</span>
            </motion.div>
          ))}
        </div>

        {/* Premium CTA */}
        <div className="mt-8 p-6 bg-[#303246]/30 backdrop-blur-lg rounded-xl border border-white/10 text-center">
          <h3 className="text-xl font-bold text-white mb-2">Want a complete interview preparation checklist?</h3>
          <p className="text-gray-300 mb-4">Upgrade to premium for a comprehensive interview preparation guide and personalized checklist.</p>
          <button 
            onClick={showPremiumOverlay}
            className="bg-[#303246]/60 backdrop-blur-lg text-white px-6 py-3 rounded-xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-white/10 inline-flex items-center"
          >
            <span className="relative z-10">Get Premium Access</span>
            <span className="ml-2 relative z-10">🚀</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default InterviewChecklistNew;