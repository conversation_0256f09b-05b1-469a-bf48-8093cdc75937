const roboticsCaseStudies = [
  {
    title: "Robot Motion Planning",
    objective: "Learn basic motion planning algorithms",
    scenario: "Plan a path for a robot arm",
    keyConcepts: ["Configuration Space", "Path Planning", "Collision Detection", "Inverse Kinematics"],
    solution: `# Robot Motion Planning Example
import numpy as np
from scipy.spatial import KDTree

class MotionPlanner:
    def __init__(self, workspace_bounds, obstacles):
        self.bounds = workspace_bounds
        self.obstacles = obstacles
        self.obstacle_tree = KDTree(obstacles)
    
    def check_collision(self, point):
        dist, _ = self.obstacle_tree.query(point)
        return dist < self.safety_margin
    
    def plan_path(self, start, goal):
        # RRT implementation
        nodes = [start]
        parents = {tuple(start): None}
        
        for _ in range(1000):
            # Sample random point
            point = np.random.uniform(self.bounds[:, 0], 
                                    self.bounds[:, 1])
            
            # Find nearest node
            distances = np.linalg.norm(nodes - point, axis=1)
            nearest_idx = np.argmin(distances)
            
            # Check if path is collision-free
            if not self.check_collision(point):
                nodes.append(point)
                parents[tuple(point)] = nearest_idx
                
                if np.linalg.norm(point - goal) < 0.1:
                    return self._extract_path(nodes, parents, -1)
        
        return None
`
  }
];

export default roboticsCaseStudies;
