import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const AIHero = ({ showPremiumOverlay }) => {
  return (
    <motion.section
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      className="relative overflow-hidden bg-gradient-to-br from-[#2a2756] to-[#01050b] text-white w-full min-h-screen flex items-center justify-center"
    >
      {/* Complex animated background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-indigo-400 to-purple-600 rounded-full opacity-10 blur-3xl"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 80, 0],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-10 blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.sin(i) * 20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className={`absolute w-2 h-2 bg-white rounded-full opacity-30`}
            style={{
              top: `${20 + i * 10}%`,
              left: `${10 + i * 12}%`,
            }}
          />
        ))}
        
        {/* AI-themed animated elements */}
        <div className="absolute inset-0 flex items-center justify-center opacity-10">
          <div className="grid grid-cols-10 grid-rows-10 gap-6 w-full h-full max-w-6xl mx-auto">
            {[...Array(50)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0.1 }}
                animate={{ 
                  opacity: [0.1, 0.3, 0.1],
                  scale: [1, 1.2, 1]
                }}
                transition={{ 
                  duration: 2 + (i % 5), 
                  repeat: Infinity,
                  delay: i * 0.1,
                  ease: "easeInOut"
                }}
                className="w-full h-full bg-white/5 rounded-md backdrop-blur-sm"
              />
            ))}
          </div>
        </div>
      </div>

      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 md:px-6 py-20 pt-36">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center lg:text-left"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="inline-block px-6 py-3 bg-white/10 border border-white/20 rounded-full text-white/90 text-sm font-medium mb-8 backdrop-blur-sm"
            >
              <span className="mr-2">🤖</span> Cutting-edge AI Learning Platform
            </motion.div>
            
            <motion.h1
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-200 to-purple-200"
            >
              Master Artificial Intelligence
            </motion.h1>
            
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="text-lg md:text-xl text-gray-300 mb-8 max-w-lg mx-auto lg:mx-0"
            >
              From foundational concepts to advanced applications, our comprehensive AI curriculum prepares you for the future of technology and innovation.
            </motion.p>
            
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
            >
              <button
                onClick={showPremiumOverlay}
                className="px-8 py-4 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white rounded-xl font-medium shadow-lg shadow-purple-500/25 transition-all duration-300 transform hover:scale-105"
              >
                Get Premium Access
              </button>
              
              <Link to="/pythoncourse" className="px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105">
                Explore Courses
              </Link>
            </motion.div>
            
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.6 }}
              className="mt-8 text-sm text-gray-400 flex items-center justify-center lg:justify-start"
            >
              <div className="flex -space-x-2 mr-3">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className={`w-8 h-8 rounded-full border-2 border-[#050816] bg-gradient-to-r ${
                    i === 1 ? 'from-blue-400 to-blue-600' :
                    i === 2 ? 'from-purple-400 to-purple-600' :
                    i === 3 ? 'from-pink-400 to-pink-600' :
                    'from-indigo-400 to-indigo-600'
                  }`}></div>
                ))}
              </div>
              <span>Joined by 10,000+ learners worldwide</span>
            </motion.div>
          </motion.div>
          
          {/* Right Content - 3D AI brain or code visualization */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative z-10 lg:mt-0 flex justify-center"
          >
            <div className="relative w-full max-w-md">
              <div className="aspect-square rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 backdrop-blur-md p-1.5">
                <div className="w-full h-full rounded-full bg-gradient-to-r from-[#050816]/80 to-[#050816]/90 backdrop-blur-md p-6 overflow-hidden flex items-center justify-center relative">
                  <img
                    src="/public/images/aiposter.jpg"
                    alt="AI Course Visualization"
                    className="w-full h-full object-cover rounded-full shadow-2xl"
                  />
                  
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/10 via-transparent to-indigo-500/10 opacity-70"></div>
                  
                  {/* Orbiting elements */}
                  {[1, 2, 3].map((i) => (
                    <motion.div
                      key={i}
                      animate={{
                        rotate: [0, 360],
                      }}
                      transition={{
                        duration: 10 + i * 5,
                        repeat: Infinity,
                        ease: "linear",
                      }}
                      className={`absolute inset-0 rounded-full border-2 border-dashed ${
                        i === 1 ? 'border-purple-500/20' :
                        i === 2 ? 'border-indigo-500/20' :
                        'border-blue-500/20'
                      } opacity-80`}
                      style={{ padding: `${i * 12}px` }}
                    />
                  ))}
                </div>
              </div>
              
              {/* Floating badges */}
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.7, duration: 0.6 }}
                className="absolute -top-5 -left-5 px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-700 rounded-full text-white text-sm font-medium shadow-lg"
              >
                Machine Learning
              </motion.div>
              
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.6 }}
                className="absolute top-1/4 -right-5 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full text-white text-sm font-medium shadow-lg"
              >
                Deep Learning
              </motion.div>
              
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9, duration: 0.6 }}
                className="absolute bottom-1/4 -left-5 px-4 py-2 bg-gradient-to-r from-indigo-500 to-indigo-700 rounded-full text-white text-sm font-medium shadow-lg"
              >
                Neural Networks
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
};

export default AIHero;
