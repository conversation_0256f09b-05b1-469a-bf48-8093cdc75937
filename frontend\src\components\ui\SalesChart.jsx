import { Link } from "react-router-dom";

const SalesChart = () => {
  return (
    <div className="container mx-auto px-6 py-16">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Enhanced Coding Interview Questions Section */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
              <span className="text-3xl">🎯</span>
              Master Coding Interviews
            </h3>
            <Link 
              to="/GG_75" 
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors duration-300"
            >
              Start Practicing
            </Link>
          </div>
          
          <p className="text-gray-600 mb-6 leading-relaxed">
            Prepare for coding interviews with our curated collection of top algorithmic and data structure problems. 
            Build confidence, enhance problem-solving skills, and ace technical interviews at leading tech companies!
          </p>

          {/* Key Features */}
          <div className="mb-6">
            <h4 className="font-semibold text-gray-800 mb-3">What You'll Get:</h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm text-gray-700">500+ Problems</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Step-by-step Solutions</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Company-specific Questions</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                <span className="text-sm text-gray-700">Live Code Editor</span>
              </div>
            </div>
          </div>

          {/* Company Logos */}
          <div className="mb-6">
            <p className="text-sm font-semibold text-gray-700 mb-3">Questions from Top Companies:</p>
            <div className="flex justify-center items-center flex-wrap gap-4">
              <div className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <img 
                  src="images/personal-interview-survey-color-icon-vector-removebg-preview.png"
                  alt="Interview Icon" 
                  className="w-12 h-12 object-contain"
                />
              </div>
              <div className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <img 
                  src="images/3454898-removebg-preview.png" 
                  alt="Tech Companies" 
                  className="w-12 h-12 object-contain"
                />
              </div>
              <div className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">G</span>
                </div>
              </div>
              <div className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">A</span>
                </div>
              </div>
            </div>
          </div>

          {/* Popular Problem Categories */}
          <div className="mb-6">
            <p className="text-sm font-semibold text-gray-700 mb-3">Popular Categories:</p>
            <div className="flex flex-wrap gap-2">
              {['Arrays', 'Linked Lists', 'Trees', 'Dynamic Programming', 'Graphs', 'Strings'].map((category) => (
                <span 
                  key={category}
                  className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors cursor-pointer"
                >
                  {category}
                </span>
              ))}
            </div>
          </div>

          {/* Success Stats */}
          <div className="bg-white rounded-xl p-4 border border-blue-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">95%</div>
                <div className="text-xs text-gray-600">Interview Success</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">10K+</div>
                <div className="text-xs text-gray-600">Students Placed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">500+</div>
                <div className="text-xs text-gray-600">Companies</div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Tech Skills Development Section */}
        <div className="bg-gradient-to-br from-green-50 to-teal-100 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
              <span className="text-3xl">🚀</span>
              Tech Skills Mastery
            </h3>
            <Link 
              to="/data_strut" 
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors duration-300"
            >
              Explore Paths
            </Link>
          </div>
          
          <p className="text-gray-600 mb-6 leading-relaxed">
            Master in-demand technical skills with our comprehensive learning paths. From data structures to system design, 
            build expertise that tech companies value most.
          </p>

          {/* Learning Paths */}
          <div className="space-y-4 mb-6">
            <Link to="/data_strut" className="block bg-white rounded-lg p-4 hover:bg-gray-50 transition-colors border border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 font-bold">DS</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Data Structures & Algorithms</h4>
                  <p className="text-sm text-gray-600">Master fundamental CS concepts</p>
                </div>
              </div>
            </Link>
            
            <Link to="/sys_des_for_int" className="block bg-white rounded-lg p-4 hover:bg-gray-50 transition-colors border border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-purple-600 font-bold">SD</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">System Design</h4>
                  <p className="text-sm text-gray-600">Design scalable distributed systems</p>
                </div>
              </div>
            </Link>
            
            <Link to="/sql_50" className="block bg-white rounded-lg p-4 hover:bg-gray-50 transition-colors border border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-600 font-bold">SQL</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Database & SQL</h4>
                  <p className="text-sm text-gray-600">Master database design and queries</p>
                </div>
              </div>
            </Link>
          </div>

          {/* Skill Level Indicator */}
          <div className="bg-white rounded-xl p-4 border border-green-200">
            <h4 className="font-semibold text-gray-800 mb-3">Your Progress</h4>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Problem Solving</span>
                  <span className="text-green-600 font-semibold">75%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{width: '75%'}}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">System Design</span>
                  <span className="text-blue-600 font-semibold">60%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{width: '60%'}}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Coding Skills</span>
                  <span className="text-purple-600 font-semibold">85%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{width: '85%'}}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesChart;
