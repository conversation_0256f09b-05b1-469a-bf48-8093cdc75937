import FAQ from "./models/FAQ.js";
import { connectDB } from "./utils/db.js";
import dotenv from "dotenv";

dotenv.config();

const seedFAQs = async () => {
  try {
    await connectDB();
    
    // Clear existing FAQs
    await FAQ.deleteMany({});
    
    const faqData = [
      // Python FAQs
      {
        question: "What prerequisites do I need for this Python course?",
        answer: "Basic computer literacy is all you need! This course starts from the fundamentals and gradually builds up to advanced concepts. No prior programming experience required.",
        category: "python",
        order: 1
      },
      {
        question: "How long does it take to complete the Python course?",
        answer: "The course is self-paced and typically takes 8-12 weeks to complete if you dedicate 2-3 hours per week. You can go faster or slower based on your schedule and learning pace.",
        category: "python",
        order: 2
      },
      {
        question: "Do I get a certificate upon completion of Python course?",
        answer: "Yes! Upon successfully completing all modules and assignments, you'll receive a certificate of completion that you can add to your LinkedIn profile and resume.",
        category: "python",
        order: 3
      },
      {
        question: "What kind of support is available during the Python course?",
        answer: "We provide comprehensive support including discussion forums, live Q&A sessions, code reviews, and direct access to instructors for personalized guidance.",
        category: "python",
        order: 4
      },
      {
        question: "Can I access the Python course materials after completion?",
        answer: "Absolutely! You get lifetime access to all course materials, including future updates and new content additions. Your investment keeps growing over time.",
        category: "python",
        order: 5
      },
      {
        question: "What makes this Python course different from others?",
        answer: "Our course focuses on practical, real-world applications with hands-on projects, interview preparation, and industry best practices. We emphasize building actual skills, not just theoretical knowledge.",
        category: "python",
        order: 6
      },
      {
        question: "Is there a money-back guarantee for Python course?",
        answer: "Yes, we offer a 30-day money-back guarantee. If you're not satisfied with the course content within the first 30 days, we'll provide a full refund, no questions asked.",
        category: "python",
        order: 7
      },
      {
        question: "What tools and software do I need for Python course?",
        answer: "All you need is a computer with Python installed (we provide installation guides) and a text editor. We recommend VS Code, which is free and works on all operating systems.",
        category: "python",
        order: 8
      },

      // ML FAQs
      {
        question: "What background do I need for Machine Learning?",
        answer: "Basic knowledge of programming (preferably Python) and high school level mathematics. We cover the mathematical concepts as we go, making ML accessible to everyone.",
        category: "ml",
        order: 1
      },
      {
        question: "How hands-on is the Machine Learning course?",
        answer: "Very hands-on! You'll work on real datasets, build actual ML models, and deploy them. Each concept is reinforced with practical exercises and projects.",
        category: "ml",
        order: 2
      },
      {
        question: "What ML libraries and tools will I learn?",
        answer: "You'll master industry-standard tools including scikit-learn, TensorFlow, Keras, pandas, NumPy, and matplotlib. We also cover MLOps tools for deployment.",
        category: "ml",
        order: 3
      },

      // AI FAQs
      {
        question: "Is this AI course suitable for beginners?",
        answer: "Yes! We start with AI fundamentals and gradually progress to advanced topics. The course is designed to take you from beginner to AI practitioner.",
        category: "ai",
        order: 1
      },
      {
        question: "What AI domains does the course cover?",
        answer: "We cover computer vision, natural language processing, deep learning, reinforcement learning, and generative AI. You'll get a comprehensive understanding of AI applications.",
        category: "ai",
        order: 2
      },

      // Full Stack FAQs
      {
        question: "What technologies are covered in the Full Stack course?",
        answer: "We cover the complete MERN stack (MongoDB, Express.js, React, Node.js), along with TypeScript, REST APIs, GraphQL, and modern deployment practices.",
        category: "fullstack",
        order: 1
      },
      {
        question: "Will I build real projects in the Full Stack course?",
        answer: "Absolutely! You'll build multiple full-stack applications including an e-commerce platform, social media app, and a project management tool.",
        category: "fullstack",
        order: 2
      },

      // Data Science FAQs
      {
        question: "What makes your Data Science course unique?",
        answer: "Our course combines statistical theory with practical implementation. You'll work with real datasets from various industries and learn end-to-end data science workflows.",
        category: "datascience",
        order: 1
      },
      {
        question: "Do I need a statistics background for Data Science?",
        answer: "While helpful, it's not required. We cover essential statistics and probability concepts as part of the curriculum, making data science accessible to all backgrounds.",
        category: "datascience",
        order: 2
      },

      // General FAQs
      {
        question: "How does the pricing work?",
        answer: "We offer flexible pricing options including monthly subscriptions, annual plans with discounts, and lifetime access options. Student discounts are also available.",
        category: "general",
        order: 1
      },
      {
        question: "Can I switch between courses?",
        answer: "Yes! With our premium subscription, you get access to all courses and can switch between them freely. This allows you to explore different tech domains.",
        category: "general",
        order: 2
      },
      {
        question: "Is there a mobile app available?",
        answer: "Currently, our platform is web-based and fully responsive. A dedicated mobile app is in development and will be launched soon.",
        category: "general",
        order: 3
      },
      {
        question: "How often is the content updated?",
        answer: "We update our content regularly to keep up with industry trends. New modules, projects, and technologies are added quarterly based on market demand.",
        category: "general",
        order: 4
      }
    ];

    await FAQ.insertMany(faqData);
    console.log("✅ FAQ data seeded successfully!");
    process.exit(0);
    
  } catch (error) {
    console.error("❌ Error seeding FAQ data:", error);
    process.exit(1);
  }
};

seedFAQs();
