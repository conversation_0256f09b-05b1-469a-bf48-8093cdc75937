import React from "react";
import { motion } from "framer-motion";

const JavaFullStackLiveClasses = ({ onBackToCourse, showPremiumOverlay }) => {
  const liveProjects = [
    {
      title: "Enterprise CRM System",
      description: "Build a customer relationship management system with Spring Boot",
      date: "Next Session: Tomorrow, 7:00 PM",
      icon: "🏢"
    },
    {
      title: "Task Management System",
      description: "Create a Jira-inspired app with Spring Boot and Angular",
      date: "Next Session: Wednesday, 6:30 PM",
      icon: "📋"
    },
    {
      title: "Real-time Chat Application",
      description: "Develop a chat app with Spring WebSockets",
      date: "Next Session: Friday, 7:00 PM",
      icon: "💬"
    },
    {
      title: "E-Commerce Platform",
      description: "Build an online store with Spring Boot, JPA, and React",
      date: "Next Session: Saturday, 11:00 AM",
      icon: "🛒"
    }
  ];

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-900/70 to-red-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Live Java Projects</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Real-world Java Full Stack Projects</h3>
          <p className="text-gray-300">
            Join our live coding sessions where we build complete enterprise-grade applications from scratch.
            Learn best practices, architecture patterns, and deployment strategies.
          </p>
        </div>
        
        {/* Live Projects */}
        <div className="space-y-4">
          {liveProjects.map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start p-4 border border-gray-700/30 bg-gray-800/30 rounded-lg hover:bg-gray-700/40 transition-colors cursor-pointer"
              onClick={showPremiumOverlay}
            >
              <div className="text-3xl mr-4">{project.icon}</div>
              <div>
                <h4 className="font-medium text-white">{project.title}</h4>
                <p className="text-sm text-gray-300 mb-1">{project.description}</p>
                <p className="text-xs text-orange-300 font-medium">{project.date}</p>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Project Roadmap */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8 bg-gray-800/30 border border-gray-700/30 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold text-white mb-2">Project Development Roadmap</h3>
          <p className="text-gray-300 mb-4">
            Follow our structured approach to building enterprise Java applications.
          </p>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-900/50 border border-orange-700/30 flex items-center justify-center text-orange-400 mr-3">1</div>
              <div>
                <h4 className="font-medium text-white">Project Planning & Setup</h4>
                <p className="text-sm text-gray-300">Define requirements, create wireframes, and set up Spring Boot project</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-900/50 border border-orange-700/30 flex items-center justify-center text-orange-400 mr-3">2</div>
              <div>
                <h4 className="font-medium text-white">Backend Development</h4>
                <p className="text-sm text-gray-300">Build RESTful APIs, JPA entities, and service layer</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-900/50 border border-orange-700/30 flex items-center justify-center text-orange-400 mr-3">3</div>
              <div>
                <h4 className="font-medium text-white">Frontend Implementation</h4>
                <p className="text-sm text-gray-300">Create UI components, implement state management, and connect to backend</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-orange-900/50 border border-orange-700/30 flex items-center justify-center text-orange-400 mr-3">4</div>
              <div>
                <h4 className="font-medium text-white">Testing & Deployment</h4>
                <p className="text-sm text-gray-300">Write unit and integration tests, and deploy to production</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default JavaFullStackLiveClasses;