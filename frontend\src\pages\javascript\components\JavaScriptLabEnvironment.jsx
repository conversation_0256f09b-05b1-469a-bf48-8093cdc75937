import React from "react";
import { motion } from "framer-motion";
import ReusableCodeEditor from "../../../components/ui/ReusableCodeEditor";

const JavaScriptLabEnvironment = ({ showPremiumOverlay }) => {
  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-yellow-900/70 to-orange-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">JavaScript Practice Lab</h2>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Interactive JavaScript Practice Environment</h3>
          <p className="text-gray-300">
            Sharpen your JavaScript skills with our interactive coding environment. Solve problems, 
            test your solutions, and track your progress.
          </p>
        </div>
        
        {/* Code Editor */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <ReusableCodeEditor 
            showPremiumOverlay={showPremiumOverlay} 
            challenges={[
              {
                id: "js-challenge-1",
                title: "JavaScript Fundamentals",
                difficulty: "Beginner",
                description: "Practice JavaScript fundamentals with these exercises.",
                requirements: [
                  "Create a function to check if a number is even or odd",
                  "Write a function to reverse a string",
                  "Implement a function to find the factorial of a number"
                ],
                template: `// JavaScript Challenge: Fundamentals

// 1. Check if a number is even or odd
function isEven(num) {
  // TODO: Implement your solution here
  
  return; // true if even, false if odd
}

// 2. Reverse a string
function reverseString(str) {
  // TODO: Implement your solution here
  
  return;
}

// 3. Find the factorial of a number
function factorial(num) {
  // TODO: Implement your solution here
  
  return;
}`
              },
              {
                id: "js-challenge-2",
                title: "JavaScript Arrays and Objects",
                difficulty: "Intermediate",
                description: "Practice working with arrays and objects in JavaScript.",
                requirements: [
                  "Implement array filtering and mapping operations",
                  "Create and manipulate JavaScript objects",
                  "Use array methods like reduce, filter, and map"
                ],
                template: `// JavaScript Challenge: Arrays and Objects

// 1. Filter an array to get only even numbers
function filterEvenNumbers(numbers) {
  // TODO: Implement your solution here
  
  return;
}

// 2. Map an array of numbers to their squares
function squareNumbers(numbers) {
  // TODO: Implement your solution here
  
  return;
}

// 3. Use reduce to sum all numbers in an array
function sumArray(numbers) {
  // TODO: Implement your solution here
  
  return;
}`
              }
            ]} 
          />
        </motion.div>
      </div>
    </div>
  );
};

export default JavaScriptLabEnvironment;