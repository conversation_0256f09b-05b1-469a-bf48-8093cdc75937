import React from 'react';

const CaseStudyCard = ({ study, index }) => {
  return (
    <div className="border-l-4 border-blue-500 pl-6 py-4 mb-8">
      <h3 className="text-3xl font-extrabold mb-4 text-blue-700 drop-shadow-sm">
        Case Study {index + 1}: <span className="text-black dark:text-white">{study.title}</span>
      </h3>
      <div className="space-y-4">
        <div>
          <h4 className="text-lg font-bold text-blue-600 mb-1">Objective:</h4>
          <p className="text-lg text-gray-900 dark:text-gray-100 font-medium">{study.objective}</p>
        </div>
        <div>
          <h4 className="text-lg font-bold text-blue-600 mb-1">Scenario:</h4>
          <p className="text-lg text-gray-900 dark:text-gray-100 font-medium">{study.scenario}</p>
        </div>
        <div>
          <h4 className="text-lg font-bold text-blue-600 mb-1">Key Concepts:</h4>
          <ul className="list-disc ml-8 text-base text-gray-800 dark:text-gray-200 font-semibold space-y-1 !bg-transparent">
            {study.keyConcepts.map((concept, index) => (
              <li key={index} className="!bg-transparent text-blue-900 dark:text-blue-200 font-bold !text-inherit">{concept}</li>
            ))}
          </ul>
        </div>
        <div>
          <h4 className="text-lg font-bold text-blue-600 mb-1">Solution:</h4>
          <pre className="p-4 rounded-lg overflow-x-auto border border-blue-200 dark:border-gray-700 bg-transparent dark:bg-transparent">
            <code className="text-base font-mono text-blue-900 dark:text-blue-200">{study.solution}</code>
          </pre>
        </div>
      </div>
    </div>
  );
};

export default CaseStudyCard;