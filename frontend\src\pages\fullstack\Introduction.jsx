import React, { useState } from "react";

const FullstackIntroduction = ({ showPremiumOverlay }) => {
  const [showMore, setShowMore] = useState(false);

  const toggleReadMore = () => {
    setShowMore(!showMore);
  };

  // CSS styles for animations
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.95); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    @keyframes rotate180 {
      from { transform: rotate(0deg); }
      to { transform: rotate(180deg); }
    }
    
    @keyframes bounce {
      0%, 100% { transform: translateX(0); }
      50% { transform: translateX(5px); }
    }
    
    .section-animate {
      opacity: 0;
      animation: fadeIn 0.8s forwards;
    }
    
    .card-animate {
      opacity: 0;
      animation: scaleIn 0.6s forwards;
    }
    
    .hover-lift {
      transition: transform 0.3s ease;
    }
    .hover-lift:hover {
      transform: translateY(-5px);
    }
    
    .hover-scale {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .hover-scale:hover {
      transform: scale(1.05);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .hover-scale:active {
      transform: scale(0.95);
    }
    
    .arrow-bounce {
      animation: bounce 1.5s infinite;
    }
    
    .delay-1 { animation-delay: 0.1s; }
    .delay-2 { animation-delay: 0.3s; }
    .delay-3 { animation-delay: 0.5s; }
    .delay-4 { animation-delay: 0.7s; }
  `;

  return (
    <div className="py-8">
      {/* Add our CSS animations */}
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Header Section */}
        <div className="text-center mb-16 section-animate delay-1">
          <div
            className="inline-block px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg"
          >
            💻 Course Introduction
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Full Stack Development{" "}
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Mastery
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Master complete web development from frontend to backend with comprehensive video-based lessons and real-world applications
          </p>
        </div>

        {/* Main Content Card */}
        <div
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-3xl shadow-2xl p-8 md:p-12 mb-12 card-animate delay-2"
        >
          <div className="space-y-8 section-animate delay-3">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                Welcome to Full Stack Development
              </h3>
              <p className="text-white/80 text-lg leading-relaxed">
                Embark on a comprehensive journey to become a complete web developer. This course covers everything from frontend technologies like React and modern JavaScript to backend development with Node.js, Express, and databases like MongoDB. You'll learn industry-standard practices, deployment strategies, and build real-world applications that showcase your skills.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h4 className="text-xl font-semibold text-blue-300 flex items-center gap-2">
                  <span className="text-2xl">🎯</span>
                  What You'll Learn
                </h4>
                <ul className="space-y-3 text-white/80">
                  <li className="flex items-start gap-3">
                    <span className="text-green-400 mt-1">✓</span>
                    <span>Frontend: React, JavaScript ES6+, HTML5, CSS3, Tailwind</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-green-400 mt-1">✓</span>
                    <span>Backend: Node.js, Express.js, RESTful APIs</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-green-400 mt-1">✓</span>
                    <span>Database: MongoDB, Mongoose, SQL basics</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-green-400 mt-1">✓</span>
                    <span>Deployment: AWS, Heroku, Docker containers</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-green-400 mt-1">✓</span>
                    <span>Tools: Git/GitHub, VS Code, DevTools</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <h4 className="text-xl font-semibold text-purple-300 flex items-center gap-2">
                  <span className="text-2xl">🚀</span>
                  Course Features
                </h4>
                <ul className="space-y-3 text-white/80">
                  <li className="flex items-start gap-3">
                    <span className="text-blue-400 mt-1">📹</span>
                    <span>50+ hours of video content</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-blue-400 mt-1">🛠️</span>
                    <span>15+ hands-on projects</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-blue-400 mt-1">📝</span>
                    <span>Real-world case studies</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-blue-400 mt-1">🎓</span>
                    <span>Industry certification</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="text-blue-400 mt-1">💬</span>
                    <span>24/7 community support</span>
                  </li>
                </ul>
              </div>
            </div>

            {showMore && (
              <div className="space-y-6 section-animate">
                <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                  <h4 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                    <span className="text-2xl">📚</span>
                    Curriculum Overview
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h5 className="text-lg font-medium text-blue-300 mb-3">Frontend Development</h5>
                      <ul className="space-y-2 text-white/70 text-sm">
                        <li>• HTML5 & Semantic markup</li>
                        <li>• CSS3 & Flexbox/Grid</li>
                        <li>• JavaScript fundamentals & ES6+</li>
                        <li>• React.js & component architecture</li>
                        <li>• State management with Redux</li>
                        <li>• Responsive design & Mobile-first</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="text-lg font-medium text-purple-300 mb-3">Backend Development</h5>
                      <ul className="space-y-2 text-white/70 text-sm">
                        <li>• Node.js runtime environment</li>
                        <li>• Express.js framework</li>
                        <li>• RESTful API design</li>
                        <li>• Database design & MongoDB</li>
                        <li>• Authentication & Authorization</li>
                        <li>• Testing & deployment strategies</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                  <h4 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                    <span className="text-2xl">🎯</span>
                    Who Is This Course For?
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                      <div className="text-3xl mb-2">👨‍💻</div>
                      <h5 className="font-medium text-white mb-2">Beginners</h5>
                      <p className="text-white/70 text-sm">Starting web development journey</p>
                    </div>
                    <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                      <div className="text-3xl mb-2">📈</div>
                      <h5 className="font-medium text-white mb-2">Career Switchers</h5>
                      <p className="text-white/70 text-sm">Transitioning to tech industry</p>
                    </div>
                    <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                      <div className="text-3xl mb-2">🚀</div>
                      <h5 className="font-medium text-white mb-2">Developers</h5>
                      <p className="text-white/70 text-sm">Expanding skillset</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Read More Button */}
            <div className="text-center">
              <button
                onClick={toggleReadMore}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                <span>{showMore ? 'Show Less' : 'Read More'}</span>
                <span className={`transform transition-transform duration-300 ${showMore ? 'rotate-180' : ''}`}>
                  ↓
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center section-animate delay-4">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 shadow-2xl">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Ready to Become a Full Stack Developer?
            </h3>
            <p className="text-blue-100 text-lg mb-6 max-w-2xl mx-auto">
              Join thousands of students who have successfully launched their web development careers
            </p>
            <button
              onClick={showPremiumOverlay}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl hover-scale"
            >
              Start Learning Today →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullstackIntroduction;
