import express from "express";
import {
  getAllFAQs,
  getFAQById,
  createFAQ,
  updateFAQ,
  deleteFAQ,
  getFAQsByCategory
} from "../controllers/faqController.js";

const router = express.Router();

// Public routes
router.get("/", getAllFAQs);
router.get("/:id", getFAQById);
router.get("/category/:category", getFAQsByCategory);

// Admin routes (you can add authentication middleware later)
router.post("/", createFAQ);
router.put("/:id", updateFAQ);
router.delete("/:id", deleteFAQ);

export default router;
