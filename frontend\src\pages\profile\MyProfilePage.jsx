import { FaCamera } from "react-icons/fa";
import { <PERSON> } from "react-router-dom";
import { Bar, Line } from "react-chartjs-2";
import { Doughn<PERSON> } from "react-chartjs-2";
import { ArcElement, BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, Title, Tooltip } from "chart.js/auto";
import { useSelector } from "react-redux";

ChartJS.register(ArcElement, Tooltip, Legend,CategoryScale, LinearScale, BarElement, Title,);

const MyProfilePage = () => {
  const { user } = useSelector((state) => state.auth);

  const data = {
    labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    datasets: [
      {
        label: "Progress",
        data: [0, 50, 100, 150, 200],
        fill: false,
        borderColor: "rgb(75, 192, 192)",
        tension: 0.1,
      },
    ],
  };

  const problemData = {
    labels: ["Easy", "Medium", "Hard"],
    datasets: [
      {
        label: "Problems Solved",
        data: [50, 30, 20],
        backgroundColor: ["#4CAF50", "#FFC107", "#F44336"],
        borderColor: ["#388E3C", "#FFA000", "#D32F2F"],
        borderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "bottom",
      },
    },
  };

  const errorData = {
    labels: ["Correct Answer", "Compilation Error", "Wrong Answer"],
    datasets: [
      {
        label: "Submission Analysis",
        data: [50, 30, 20],
        backgroundColor: ["#3498DB", "#5DADE2", "#1B4F72"],
        borderColor: ["#2E86C1", "#2874A6", "#154360"],
        borderWidth: 2,
      },
    ],
  };

  const errorOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "bottom",
      },
    },
  };

  const courseData = {
    labels: ["Python Course", "Full Stack Course", "Data Science Course", "AI Course", "ML Course", "SQL Course", "Python DSA"],
    datasets: [
      {
        label: "Students Enrolled",
        data: [120, 95, 85, 90, 75, 110, 150], 
        backgroundColor: ["#3498DB", "#00D9FF", "#1ABC9C", "#9B59B6", "#E67E22", "#E74C3C", "#F39C12"],
        borderColor: ["#2980B9", "#00A8CC", "#16A085", "#8E44AD", "#D35400", "#C0392B", "#E67E22"],
        borderWidth: 2,
      },
    ],
  };

  const courseOptions = {
    indexAxis: "y", 
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true, 
      },
    },
    scales: {
      x: {
        beginAtZero: true,
      },
    },
  };

  const topicsData = [
    {
      name: "LinkedList Problems",
      easy: 10,
      medium: 5,
      hard: 8,
    },
    {
      name: "Array Manipulation",
      easy: 15,
      medium: 10,
      hard: 6,
    },
    {
      name: "Binary Trees",
      easy: 8,
      medium: 12,
      hard: 5,
    },
    {
      name: "Dynamic Programming",
      easy: 7,
      medium: 14,
      hard: 9,
    },
    {
      name: "Graph Algorithms",
      easy: 5,
      medium: 7,
      hard: 10,
    },
    {
      name: "Searching Algorithms",
      easy: 20,
      medium: 12,
      hard: 6,
    },
    {
      name: "Sorting Algorithms",
      easy: 18,
      medium: 9,
      hard: 4,
    },
    {
      name: "Recursion Problems",
      easy: 14,
      medium: 8,
      hard: 5,
    },
    {
      name: "Greedy Algorithms",
      easy: 12,
      medium: 9,
      hard: 6,
    },
    {
      name: "Backtracking Problems",
      easy: 8,
      medium: 6,
      hard: 7,
    },
  ];

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 py-10 px-2 md:px-0">
      <div className="max-w-5xl mx-auto flex flex-col gap-8">
        {/* My Profile Card */}
        <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 rounded-2xl shadow-2xl p-8 flex flex-col md:flex-row items-center gap-8 border border-slate-700">
          <div className="flex flex-col items-center gap-2">
            <div className="relative w-32 h-32">
              <img src={user ? user?.image : "/images/logonew.png"} alt="user" className="rounded-full w-32 h-32 object-cover border-4 border-blue-500 shadow-lg" />
              <div className="absolute bottom-2 right-2 bg-slate-700/80 rounded-full p-2 shadow-md cursor-pointer border border-blue-500">
                <FaCamera className="text-blue-300" />
              </div>
            </div>
            <button className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700 transition">
              <Link to={"/profile/edit-profile"} style={{ textDecoration: "none", color: "#fff" }}>
                Edit Profile
              </Link>
            </button>
          </div>
          <div className="flex-1 flex flex-col items-center md:items-start gap-2">
            <h2 className="text-3xl font-bold text-white mb-1">{user?.name}</h2>
            <p className="text-blue-200 text-lg">{user?.email}</p>
            <span className="text-slate-400 text-sm">Member since: Aug 2002</span>
          </div>
        </div>

        {/* Activities & Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Progress Line Chart */}
          <div className="bg-gradient-to-br from-blue-900/80 to-slate-800/90 rounded-2xl shadow-xl p-8 flex flex-col items-center border border-blue-700 hover:shadow-2xl transition-shadow duration-300 group">
            <div className="flex items-center gap-2 mb-4 w-full">
              <span className="inline-block w-2 h-6 bg-blue-500 rounded-full group-hover:bg-blue-400 transition" />
              <h3 className="text-xl font-bold text-blue-100 tracking-wide">Weekly Progress</h3>
            </div>
            <Line data={data} />
          </div>

          {/* Solved Problem List */}
          <div className="bg-gradient-to-br from-blue-900/80 to-slate-800/90 rounded-2xl shadow-xl p-8 border border-blue-700 hover:shadow-2xl transition-shadow duration-300 group">
            <div className="flex items-center gap-2 mb-4 w-full">
              <span className="inline-block w-2 h-6 bg-green-400 rounded-full group-hover:bg-green-300 transition" />
              <h3 className="text-xl font-bold text-green-100 tracking-wide">Solved Problem List</h3>
            </div>
            <div className="flex flex-col gap-3 max-h-72 overflow-y-auto pr-2">
              {topicsData.map((topic, index) => (
                <div className="flex flex-col md:flex-row md:items-center justify-between bg-slate-900/70 rounded-lg px-4 py-2 hover:bg-slate-800/80 transition" key={index}>
                  <span className="font-medium text-blue-100">{topic.name}</span>
                  <div className="flex gap-4 mt-2 md:mt-0">
                    <span className="text-green-400 text-sm font-semibold">Easy: {topic.easy}</span>
                    <span className="text-yellow-300 text-sm font-semibold">Medium: {topic.medium}</span>
                    <span className="text-red-400 text-sm font-semibold">Hard: {topic.hard}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Doughnut and Bar Charts */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Problem Solved Chart */}
          <div className="bg-gradient-to-br from-green-900/90 to-slate-800/95 rounded-3xl shadow-2xl p-8 flex flex-col items-center border-2 border-green-600 hover:shadow-2xl transition-shadow duration-300 group relative overflow-hidden">
            <div className="absolute top-4 right-4 text-green-300 text-3xl opacity-30 pointer-events-none select-none">✔️</div>
            <div className="flex items-center gap-3 mb-4 w-full justify-center">
              <span className="inline-block w-3 h-10 bg-green-400 rounded-full group-hover:bg-green-300 transition" />
              <h3 className="text-2xl font-extrabold text-green-100 tracking-wide drop-shadow">Problem Solved</h3>
            </div>
            <div className="w-full flex-1 flex items-center justify-center">
              <Doughnut data={problemData} options={options} />
            </div>
          </div>
          {/* Submission Analysis Chart */}
          <div className="bg-gradient-to-br from-blue-900/90 to-slate-800/95 rounded-3xl shadow-2xl p-8 flex flex-col items-center border-2 border-blue-600 hover:shadow-2xl transition-shadow duration-300 group relative overflow-hidden">
            <div className="absolute top-4 right-4 text-blue-300 text-3xl opacity-30 pointer-events-none select-none">📊</div>
            <div className="flex items-center gap-3 mb-4 w-full justify-center">
              <span className="inline-block w-3 h-10 bg-blue-400 rounded-full group-hover:bg-blue-300 transition" />
              <h3 className="text-2xl font-extrabold text-blue-100 tracking-wide drop-shadow">Submission Analysis</h3>
            </div>
            <div className="w-full flex-1 flex items-center justify-center">
              <Doughnut data={errorData} options={errorOptions} />
            </div>
          </div>
          {/* Course Enrollment Analysis Bar Chart */}
          <div className="bg-gradient-to-br from-yellow-900/90 to-slate-800/95 rounded-3xl shadow-2xl p-8 flex flex-col items-center border-2 border-yellow-600 hover:shadow-2xl transition-shadow duration-300 group relative overflow-hidden">
            <div className="absolute top-4 right-4 text-yellow-300 text-3xl opacity-30 pointer-events-none select-none">🎓</div>
            <div className="flex items-center gap-3 mb-4 w-full justify-center">
              <span className="inline-block w-3 h-10 bg-yellow-400 rounded-full group-hover:bg-yellow-300 transition" />
              <h3 className="text-2xl font-extrabold text-yellow-100 tracking-wide drop-shadow">Course Enrollment Analysis</h3>
            </div>
            <div className="w-full flex-1 flex items-center justify-center">
              <Bar data={courseData} options={courseOptions} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyProfilePage;
