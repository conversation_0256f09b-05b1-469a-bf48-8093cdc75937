import SQLHero from "./components/SQLHero";
import SQLNavigation from "./components/SQLNavigation";
import SQLCategorySection from "./components/SQLCategorySection";
import SQLChecklist from "./components/SQLChecklist";
import SQLLayout from "./components/SQLLayout";
import { LoadingSpinner } from "../../components/ui";
import useLoadingState from "../../hooks/useLoadingState";
import useSidebarState from "../../hooks/useSidebarState";
import "./components/SQLStyles.css";

const SQL = () => {
  const loading = useLoadingState(1000);
  const { isSidebarOpen, toggleSidebar } = useSidebarState(true); // Default to open

  if (loading) {
    return <LoadingSpinner />;
  }
  const sqlQuestion = [
    {
      level: "Beginner",
      icon: "leaf",
      difficulty: "Easy",
      questions: [
        {
          title: "Basic SQL Queries",
          tags: ["SQL", "Queries"],
          keyPoints: [
            "Understand SELECT, FROM, WHERE clauses",
            "Practice filtering and sorting data",
          ],
        },
        {
          title: "Table Joins",
          tags: ["SQL", "Joins"],
          keyPoints: [
            "Learn INNER JOIN, LEFT JOIN, RIGHT JOIN",
            "Understand use cases for each join type",
          ],
        },
      ],
    },
    {
      level: "Intermediate",
      icon: "fire",
      difficulty: "Medium",
      questions: [
        {
          title: "Subqueries and Nested Queries",
          tags: ["SQL", "Subqueries"],
          keyPoints: [
            "Understand how to use subqueries",
            "Use subqueries within SELECT, FROM, WHERE clauses",
          ],
        },
        {
          title: "Window Functions",
          tags: ["SQL", "Window Functions"],
          keyPoints: [
            "Learn about ROW_NUMBER, RANK, and PARTITION BY",
            "Understand their applications in data analysis",
          ],
        },
      ],
    },
    {
      level: "Advanced",
      icon: "bolt",
      difficulty: "Hard",
      questions: [
        {
          title: "Recursive CTEs",
          tags: ["SQL", "CTEs", "Recursion"],
          keyPoints: [
            "Understand Common Table Expressions",
            "Practice recursive queries for hierarchical data",
          ],
        },
        {
          title: "SQL Performance Optimization",
          tags: ["SQL", "Optimization", "Indexes"],
          keyPoints: [
            "Understand indexing, query optimization",
            "Analyze and optimize complex queries",
          ],
        },
      ],
    },
  ];
  
  const questions = [
    {
      question: "1. Write a Query to Find Duplicates",
      answer:
        "Use GROUP BY and HAVING COUNT > 1 to find duplicate entries in a table.",
    },
    {
      question: "2. Write a Query to Fetch the Second Highest Salary",
      answer:
        "Use ORDER BY DESC and LIMIT 1 OFFSET 1 to get the second-highest salary.",
    },
    {
      question: "3. Explain ACID Properties",
      answer:
        "ACID stands for Atomicity, Consistency, Isolation, and Durability, ensuring reliable transactions.",
    },
    {
      question: "4. What is a Primary Key and Foreign Key?",
      answer:
        "A Primary Key uniquely identifies each record; a Foreign Key links two tables together.",
    },
    {
      question:
        "5. Write a Query to Find the Number of Employees in Each Department",
      answer:
        "Use GROUP BY department_id and COUNT(*) to get the employee count per department.",
    },
    {
      question: "6. Explain the Difference Between WHERE and HAVING",
      answer:
        "WHERE filters rows before grouping; HAVING filters groups after grouping.",
    },
    {
      question: "7. Write a Query to Join Two Tables",
      answer:
        "Use INNER JOIN, LEFT JOIN, or RIGHT JOIN to retrieve data from multiple tables.",
    },
    {
      question: "8. Write a Query to Get Employees Who Joined Last Month",
      answer:
        "Use DATE_SUB() and BETWEEN to filter for employees who joined last month.",
    },
    {
      question: "9. Explain Normalization and Denormalization",
      answer:
        "Normalization reduces redundancy by splitting data; denormalization combines tables for faster reads.",
    },
    {
      question: "10. Write a Query to Remove Duplicates from a Table",
      answer:
        "Use ROW_NUMBER() or DELETE with JOIN to remove duplicate rows from a table.",
    },
    {
      question: "11. What is the Difference Between DELETE and TRUNCATE?",
      answer:
        "DELETE removes rows with a WHERE clause; TRUNCATE deletes all rows quickly but cannot be used with a WHERE clause.",
    },
    {
      question: "12. Write a Query to Find the Highest and Lowest Salary",
      answer:
        "Answer: Use MAX(salary) and MIN(salary) to retrieve the highest and lowest salary.",
    },
    {
      question: "13. Explain the Concept of an Index",
      answer:
        "Answer: Indexes improve the speed of data retrieval but can slow down inserts, updates, and deletes.",
    },
    {
      question: "14. Write a Query to Display Only Odd or Even Records",
      answer:
        "Answer: Use ROW_NUMBER() with modulo (%) to filter odd/even records.",
    },
    {
      question: "15. What is the Difference Between UNION and UNION ALL?",
      answer:
        "Answer: UNION removes duplicates; UNION ALL includes all duplicates.",
    },
    {
      question: "16. Write a Query to Find Employees with Salaries Above Average",
      answer:
        "Answer: Use a subquery to calculate the average salary and filter employees with salaries above this value.",
    },
    {
      question: "17. Explain the Difference Between INNER JOIN and OUTER JOIN",
      answer:
        "Answer: INNER JOIN returns only matching records; OUTER JOIN returns all records with NULL for non-matches.",
    },
    {
      question: "18. Write a Query to List All Table Names in a Database",
      answer:
        "Answer: Use SELECT table_name FROM information_schema.tables to list table names.",
    },
    {
      question: "19. What is a View, and How is it Used?",
      answer:
        "Answer: A view is a virtual table based on a query; it simplifies data access without storing data itself.",
    },
    {
      question: "20. Write a Query to Add a Column to an Existing Table",
      answer:
        "Answer: Use ALTER TABLE table_name ADD column_name data_type to add a column.",
    },
    {
      question:
        "21. What is the Difference Between a Primary Key and a Unique Key?",
      answer:
        "Answer: A primary key uniquely identifies each row and does not allow NULLs; a unique key also enforces uniqueness but can allow NULLs.",
    },
    {
      question: "22. Write a Query to Convert Rows to Columns",
      answer:
        "Answer: Use CASE statements or the PIVOT function to convert rows to columns.",
    },
    {
      question: "23. Explain the Concept of a Foreign Key Constraint",
      answer:
        "Answer: A foreign key constraint enforces a link between two tables, maintaining referential integrity.",
    },
    {
      question: "24. Write a Query to Remove a Column from a Table",
      answer:
        "Answer: Use ALTER TABLE table_name DROP COLUMN column_name to remove a column.",
    },
    {
      question: "25. What is the Use of the COALESCE Function?",
      answer:
        "Answer: COALESCE returns the first non-NULL value in a list of arguments.",
    },
    {
      question:
        "26. Write a Query to Calculate the Total Salary of Employees in Each Department",
      answer:
        "Answer: Use SUM(salary) with GROUP BY department_id to calculate total salary by department.",
    },
    {
      question: "27. Explain the Purpose of the EXISTS Keyword",
      answer:
        "Answer: EXISTS checks if a subquery returns any rows and is used for efficient conditional checks.",
    },
    {
      question: "28. Write a Query to Find the Length of a String",
      answer:
        "Answer: Use LENGTH() function to get the number of characters in a string.",
    },
    {
      question: "29. Explain the Difference Between CHAR and VARCHAR",
      answer:
        "Answer: CHAR is a fixed-length string, while VARCHAR is a variable-length string up to a specified limit.",
    },
    {
      question: "30. Write a Query to Fetch the Top N Records",
      answer:
        "Answer: Use LIMIT n to get the top N records in MySQL or TOP N in SQL Server.",
    },
  ];
  return (
    <SQLLayout isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar}>
      <SQLHero />
      <div className="container mx-auto px-4">
        <SQLNavigation />
        
        <div className="mt-10">
          {sqlQuestion.map((data, index) => (
            <SQLCategorySection key={index} data={data} />
          ))}
        </div>
        
        <SQLChecklist questions={questions} />
      </div>
    </SQLLayout>
  );
};

export default SQL;
