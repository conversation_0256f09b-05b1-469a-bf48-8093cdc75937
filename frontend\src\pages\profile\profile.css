* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* profile navbar */

.profile-header {
  display: flex;
  align-items: center;
  padding-block: 1rem;
  height: 60px;
  width: 100%;
  background-color: #f3f6f9;
}

.header-icn {
  padding-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.header-icn h2 {
  font-weight: 700;
  font-size: 22px;
  color: #0d6efd;
}

.profile-content {
  display: flex;
  gap: 10px;
  position: relative;
  transition: transform 0.4s ease-in-out;
}

.profile-link {
  text-decoration: none;
  color: #009cff;
}

/* profile sidebar */

.profile-sidebar {
  background-color: #f3f6f9;
  height: 87vh;
  width: 200px;
  transition: transform 0.4s ease-in-out;
  z-index: 999;
}

.profile-sidebar.closed {
  transform: translateX(-100%);
  z-index: -1;
}

.profile-sidebar.closed {
  width: 0;
  padding: 0;
  position: absolute;
  left: 0;
  visibility: hidden;
}

.main-contents {
  width: 100%;
  transition: width 0.4s ease-in-out;
  z-index: 1;
  height: 90vh;
  overflow-y: auto;
}

.main-contents.expanded {
  width: 100%;
}

@media (max-width: 768px) {
  .profile-sidebar {
    position: fixed;
    z-index: 9999;
    left: 0;
    height: 100vh;
  }

  .main-contents {
    z-index: 1;
  }
}

@media (max-width: 1100px) {
  .profile-sidebar {
    height: 104vh;
  }
}

.sidebar-list-icons {
  display: flex;
  gap: 12px;
  align-items: center;
  transition: transform 0.2s ease-in-out;
}

.sidebar-list-icons:hover {
  transform: scale(1.05);
}

.sidebar-list {
  margin-top: 10px;
}

.sidebar-list li {
  margin-bottom: 30px;
  background-color: #ffffff;
  padding-left: 15px;
  padding-block: 10px;
  margin-inline: 10px;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.sidebar-list li:hover {
  background-color: #f0f5fa;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

.delete-item {
  color: red;
  font-weight: bold;
}

/* cross icon */

.cross-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 12px;
  left: 240px;
  cursor: pointer;
  animation: zoomInOut 1.5s infinite ease-in-out;
}

@keyframes zoomInOut {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* change password */

.change-password {
  height: 100%;
  padding-top: 3rem;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-card {
  width: 90%;
  height: 450px;
  background-color: #fff;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding-inline: 2rem;
}

.password-card h1 {
  color: #009cff;
  margin-top: 30px;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
}

.password-card h1::after {
  content: "";
  width: 120px;
  height: 3px;
  background: #009cff;
  display: block;
  margin-left: 20px;
  margin-top: 5px;
  transition: width 0.3s ease-in-out;
}

.password-card h1:hover::after {
  width: 80px;
}

/* Input Styling */
.password-card input {
  width: 100%;
  padding: 12px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}

/* Focus Animation */
.password-card input:focus {
  border-color: #009cff;
  box-shadow: 0px 0px 8px rgba(0, 156, 255, 0.6);
  background: #fff;
}

/* Animated Button */
.password-card button {
  width: 100%;
  padding: 12px;
  background: #009cff;
  border: none;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease-in-out;
  margin-top: 15px;
}

.password-card button:hover {
  background: #007acc;
  transform: translateY(-2px);
  box-shadow: 0px 5px 10px rgba(0, 124, 255, 0.4);
}

/* social accounts */

.social-account {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.social-card {
  margin-top: 3rem;
  width: 90%;
  height: 450px;
  background-color: #fff;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding-inline: 2rem;
}

.social-icon {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #009cff;
}

.social-card h1 {
  color: #009cff;
  margin-top: 30px;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
}

.social-card h1::after {
  content: "";
  width: 120px;
  height: 3px;
  background: #009cff;
  display: block;
  margin-left: 20px;
  margin-top: 5px;
  transition: width 0.3s ease-in-out;
}

.social-card h1:hover::after {
  width: 80px;
}

/* Input Styling */
.social-card input {
  width: 100%;
  padding: 10px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}

.social-card input:focus {
  border-color: #009cff;
  box-shadow: 0px 0px 8px rgba(0, 156, 255, 0.6);
  background: #fff;
}

.social-card button {
  width: 100%;
  padding: 12px;
  background: #009cff;
  border: none;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease-in-out;
  margin-top: 15px;
}

.social-card button:hover {
  background: #007acc;
  transform: translateY(-2px);
  box-shadow: 0px 5px 10px rgba(0, 124, 255, 0.4);
}

/* edit profile */

.edit-profile {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-card {
  margin-top: 3rem;
  width: 90%;
  height: 550px;
  background-color: #fff;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding-inline: 2rem;
}

.edit-card h1 {
  color: #009cff;
  margin-top: 10px;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
}

.edit-card h1::after {
  content: "";
  width: 80px;
  height: 3px;
  background: #009cff;
  display: block;
  margin-left: 10px;
  margin-top: 5px;
  transition: width 0.3s ease-in-out;
}

.edit-card h1:hover::after {
  width: 80px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 30px;
}

.edit-avatar {
  height: 150px;
  width: 180px;
  border-radius: 50%;
  border: 6px solid #bdc5ca;
  cursor: pointer;
  position: relative;
}

.edit-avatar img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}

.profile-info {
  height: 100%;
  width: 100%;
}

.profile-info input {
  width: 100%;
  padding: 10px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}

.profile-info input:focus {
  border-color: #009cff;
  box-shadow: 0px 0px 8px rgba(0, 156, 255, 0.6);
  background: #fff;
}

.placed-avatar {
  position: absolute;
  justify-content: center;
  height: 30px;
  width: 30px;
  display: flex;
  align-items: center;
  border-radius: 50%;
  border: 5px solid #fff;
  top: 115px;
  left: 100px;
  cursor: pointer;
}

.extra-field input {
  width: 100%;
  padding: 10px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}

.extra-field select {
  width: 100%;
  padding: 10px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}

.extra-field input:focus {
  border-color: #009cff;
  box-shadow: 0px 0px 8px rgba(0, 156, 255, 0.6);
  background: #fff;
}

.country-city {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  width: 100%;
}

.country-city div {
  flex: 1;
}

.country-city input {
  width: 100%;
  padding: 10px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}

@media (max-width: 768px) {
  .user-profile {
    flex-direction: column;
  }
  .edit-card{
    height: 650px;
    align-items: center;
    justify-content: center;
  }
  .change-password {
    height: 100vh;
  }
  .social-account {
    height: 100vh;
  }
  .edit-avatar {
    width: 150px;
  }
}

/* My Profile */

.my-profile {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.my-card {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  /* border-radius: 10px; */
  padding-inline: 2rem;
  padding-bottom: 30px;
}

.my-card::-webkit-scrollbar {
  width: 4px;
}

.my-card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.my-card::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.my-card::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.my-card h1 {
  color: #009cff;
  margin-top: 10px;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
}

.my-card h1::after {
  content: "";
  width: 80px;
  height: 3px;
  background: #009cff;
  display: block;
  margin-left: 10px;
  margin-top: 5px;
  transition: width 0.3s ease-in-out;
}

.my-card h1:hover::after {
  width: 80px;
}

.me-profile {
  display: flex;
  gap: 30px;
}

.my-avatar {
  height: 150px;
  width: 150px;
  border-radius: 50%;
  border: 6px solid #bdc5ca;
  cursor: pointer;
  position: relative;
}

.my-avatar img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}

.my-info h3 {
  color: #717374;
  font-size: 25px;
  font-weight: 600;
  margin: 0;
}

.my-info p {
  color: #717374;
  font-size: 25px;
  font-weight: 600;
  margin: 0;
}

.my-info h4 {
  color: #717374;
  font-size: 15px;
  font-weight: 400;
  margin-top: 5px;
}

.my-edit-btn {
  width: 20%;
  padding: 12px;
  background: #009cff;
  border: none;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease-in-out;
  margin-top: 25px;
  margin-bottom: 20px;
}

.my-grid-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.grid1 {
  grid-column: span 2;
}

.grid2 {
  grid-column: span 2;
  height: 320px;
  overflow-y: auto;
}

.my-grid-item {
  background: #f8f9fa;
  padding: 15px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-weight: bold;
  width: 100%;
}

.my-grid-item canvas {
  width: 100% !important;
  height: auto !important;
}

.grid2::-webkit-scrollbar {
  width: 4px;
}

.grid2::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.grid2::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.grid2::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  text-align: left;
}

.topics-list {
  list-style-type: none;
  padding-left: 0;
}

.topic-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.bullet {
  font-size: 20px;
  margin-right: 10px;
  color: #007bff;
}

.topic-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  flex-grow: 1;
}

.difficulty-container {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.difficulty {
  font-size: 16px;
  font-weight: 500;
}

.easy {
  color: #28a745;
}

.medium {
  color: #ffc107;
}

.hard {
  color: #dc3545;
}

.my-grid-container-two {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.my-grid-item-two {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 15px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-weight: bold;
  height: 300px;
  width: 100%;
  box-sizing: border-box;
}

.grid-one p {
  font-size: 28px;
  font-weight: 700;
  color: #009cff;
}

.grid-two p {
  font-size: 28px;
  font-weight: 700;
  color: #009cff;
}

.my-grid-container-three {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
  width: 100%;
}

.my-grid-item-three {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-weight: bold;
  height: 500px;
}

.third-bar {
  width: 80%;
  height: 400px;
}

.my-grid-item-three p {
  font-size: 28px;
  font-weight: 700;
  color: #009cff;
}

@media (max-width: 1100px) {
.my-profile{
  height: auto;
}
  .my-grid-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .grid1,
  .grid2 {
    grid-column: span 2;
  }
  .my-grid-container-two {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (max-width: 768px) {
  .my-grid-container {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }

  .grid1,
  .grid2 {
    grid-column: span 1;
  }
  .my-grid-item-two {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }
  .my-grid-item-two canvas {
    max-height: 70%;
    max-width: 70%;
  }

  .grid-one p,
  .grid-two p {
    font-size: 22px;
  }
  .my-edit-btn{
    width: 30%;
  }
}

@media (max-width:960px){
  .me-profile{
    flex-direction: column;
  }
  .my-edit-btn{
    width: 40%;
  }
}

@media (max-width: 570px) {
  .my-grid-item-three {
    height: 450px;
  }
  .third-bar {
    width: 100%;
    height: 300px;
  }
  .my-edit-btn{
    width: 30%;
  }
}

/* Custom scrollbar for modern profile */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 10px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
}

/* Modern profile animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

/* Responsive improvements for modern profile */
@media (max-width: 1024px) {
  .grid.grid-cols-1.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid.grid-cols-1.lg\\:grid-cols-3,
  .grid.grid-cols-1.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

/* Modern form input animations */
.modern-input {
  position: relative;
  overflow: hidden;
}

.modern-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
  transition: left 0.5s;
}

.modern-input:focus-within::before {
  left: 100%;
}

/* Enhanced button hover effects */
.modern-button {
  position: relative;
  overflow: hidden;
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.modern-button:hover::before {
  left: 100%;
}

/* Improved select dropdown styling */
select option {
  background-color: #1e293b !important;
  color: white !important;
  padding: 8px !important;
}

/* Custom focus ring for better accessibility */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Delete Account */

.delete-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f8f9fa;
}
.delete-box {
  margin: 4rem;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 60%;
}

.delete-box h2 {
  color: #009cff;
  font-weight: 700;
  margin-bottom: 20px;
  font-size: 40px;
}

.delete-box p {
  color: #404244;
  font-weight: 500;
  font-family: "Courier New", Courier, monospace;
  margin-bottom: 20px;
}

.delete-button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 20px;
}
.delete-button-group button {
  width: 50%;
  border: none;
  padding: 10px;
  cursor: pointer;
  border-radius: 5px;
}
.delete-button-group .active {
  background-color: #007bff;
  color: white;
}
.delete-input-field input {
  width: 100%;
  padding: 10px;
  margin: 15px 0;
  font-size: 16px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: #effbff;
}
.delete-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 10px;
  width: 100%;
  margin-top: 15px;
  cursor: pointer;
  border-radius: 5px;
  margin-bottom: 10px;
}
.delete-modal {
  z-index: 99999;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.delete-modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  width: 400px;
}

.delete-modal-content h3 {
  font-size: 40px;
  color: #009cff;
  font-weight: 700;
  margin-bottom: 10px;
}

.delete-modal-content p {
  color: #404244;
  font-weight: 500;
  margin-bottom: 20px;
}

.delete-modal-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}
.cancel {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 700;
}
.confirm {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
}

@media (max-width: 1100px) {
  .delete-container {
    height: 100%;
  }
  .delete-box {
    width: 90%;
  }
}

@media (max-width: 768px) {
  .delete-container {
    height: 100%;
  }
  .delete-box {
    width: 90%;
  }
}
