import { useState } from "react";
import { DeleteModal } from "../../components/ui";

const Enrollment = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const casesPerPage = 5;

  const enrollmentData = [
    {
      date: "2025-03-10",
      invoice: "INV001",
      username: "<PERSON><PERSON><PERSON><PERSON>",
      amount: 4500,
      status: "Paid",
    },
    {
      date: "2025-03-12",
      invoice: "INV002",
      username: "<PERSON><PERSON><PERSON><PERSON>",
      amount: 5000,
      status: "Pending",
    },
    {
      date: "2025-03-15",
      invoice: "INV003",
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      amount: 6000,
      status: "<PERSON><PERSON>",
    },
    {
      date: "2025-03-17",
      invoice: "INV004",
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      amount: 4000,
      status: "Failed",
    },
    {
      date: "2025-03-18",
      invoice: "INV005",
      username: "<PERSON><PERSON><PERSON>_<PERSON>dav",
      amount: 7000,
      status: "Paid",
    },
    {
      date: "2025-03-19",
      invoice: "INV006",
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      amount: 5200,
      status: "Pending",
    },
    {
      date: "2025-03-20",
      invoice: "INV007",
      username: "Manoj_Patel",
      amount: 5800,
      status: "Paid",
    },
    {
      date: "2025-03-21",
      invoice: "INV008",
      username: "Rohan_Das",
      amount: 6300,
      status: "Failed",
    },
    {
      date: "2025-03-22",
      invoice: "INV009",
      username: "Kavita_Nair",
      amount: 4500,
      status: "Paid",
    },
    {
      date: "2025-03-23",
      invoice: "INV010",
      username: "Arjun_Mishra",
      amount: 8000,
      status: "Pending",
    },
    {
      date: "2025-03-24",
      invoice: "INV011",
      username: "Deepika_Chopra",
      amount: 4900,
      status: "Paid",
    },
    {
      date: "2025-03-25",
      invoice: "INV012",
      username: "Suraj_Pandey",
      amount: 5500,
      status: "Paid",
    },
    {
      date: "2025-03-26",
      invoice: "INV013",
      username: "Meera_Joshi",
      amount: 4700,
      status: "Pending",
    },
    {
      date: "2025-03-27",
      invoice: "INV014",
      username: "Sandeep_Reddy",
      amount: 6200,
      status: "Paid",
    },
    {
      date: "2025-03-28",
      invoice: "INV015",
      username: "Anjali_Sen",
      amount: 5100,
      status: "Failed",
    },
  ];

  const indexOfLastCase = currentPage * casesPerPage;
  const indexOfFirstCase = indexOfLastCase - casesPerPage;
  const currentCases = enrollmentData.slice(indexOfFirstCase, indexOfLastCase);

  const nextPage = () => {
    if (currentPage < Math.ceil(enrollmentData.length / casesPerPage)) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const openDeleteModal = () => {
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setIsOpen(false);
  };

  const deleteConfirmation = () => {
    alert("Enrollment Data Deleted!");
    setIsOpen(false);
  };
  return (
    <div className="bg-gray-100 min-h-screen py-10">
      <div className="w-[95%] max-w-[1100px] mx-auto p-5 rounded-lg text-white shadow-lg borderborder-gray-300">
        <h2 className="text-center text-3xl font-extrabold text-blue-400 mb-4">
          Enrollment Details
        </h2>

        {/* Responsive Table Container */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse mt-3 min-w-[600px]  text-sm sm:text-base">
            <thead className=" bg-blue-900 ">
              <tr className="text-left text-white uppercase font-bold">
                <th className="p-3">#</th>
                <th className="p-3">Date</th>
                <th className="p-3">Invoice</th>
                <th className="p-3">Username</th>
                <th className="p-3">Amount</th>
                <th className="p-3">Status</th>
                <th className="p-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentCases.map((enroll, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-600 text-gray-800"
                  // className={`${
                  //   index % 2 === 0 ? "bg-[#242434]" : ""
                  // } border-b border-gray-700`}
                >
                  <td className="p-3">{indexOfFirstCase + index + 1}</td>
                  <td className="p-3">{enroll.date}</td>
                  <td className="p-3">{enroll.invoice}</td>
                  <td className="p-3">{enroll.username}</td>
                  <td className="p-3">{enroll.amount}</td>
                  <td className="p-3">{enroll.status}</td>
                  <td className="p-3">
                    <button
                      className="px-3 py-2 bg-red-500 text-white rounded-md font-bold transition-transform transform hover:scale-105"
                      onClick={openDeleteModal}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination Buttons */}
        <div className="flex flex-wrap justify-center items-center text-center mt-4 gap-3 sm:gap-0 sm:flex-nowrap">
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md font-bold transition-transform transform hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ⬅ Prev
          </button>
          <span className="text-lg font-bold text-blue-500 bg-blue-100 px-4 py-2 rounded-md">
            Page {currentPage}
          </span>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md font-bold transition-transform transform hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={nextPage}
            disabled={
              currentPage >= Math.ceil(enrollmentData.length / casesPerPage)
            }
          >
            Next ➡
          </button>
        </div>
      </div>

      <DeleteModal
        title={"Delete Enrollment Data"}
        message={"Are You Sure Want to Delete?"}
        onClose={closeDeleteModal}
        isOpen={isOpen}
        onConfirm={deleteConfirmation}
      />
    </div>
  );
};

export default Enrollment;
