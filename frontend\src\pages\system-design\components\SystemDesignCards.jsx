import React from "react";

const SystemDesignCards = ({ onCardSelect }) => {
  const courseCards = [
    {
      id: "Introduction",
      title: "Introduction",
      subtitle: "Theory Only",
      icon: "fas fa-book-open",
      color: "from-blue-500 to-blue-600",
      description: "Learn system design fundamentals, patterns, and architectural concepts.",
      btnText: "Start Learning"
    },
    {
      id: "LabEnvironment",
      title: "Lab / Environment",
      subtitle: "Interactive Coding",
      icon: "fas fa-code",
      color: "from-green-500 to-green-600",
      description: "Practice system design with a built-in code editor and real-time examples.",
      btnText: "Start Coding"
    },
    {
      id: "Projects",
      title: "Real-time Projects",
      subtitle: "Hands-on Experience",
      icon: "fas fa-project-diagram",
      color: "from-purple-500 to-purple-600",
      description: "Build practical system design projects to strengthen your portfolio.",
      btnText: "View Projects"
    },
    {
      id: "InterviewChecklist",
      title: "Interview Checklist",
      subtitle: "Preparation Guide",
      icon: "fas fa-tasks",
      color: "from-yellow-500 to-yellow-600",
      description: "Get ready for system design interviews with our comprehensive checklist.",
      btnText: "View Checklist"
    }
  ];

  // CSS animation styles
  const animationStyles = `
    @keyframes fadeInUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .card-animate {
      opacity: 0;
      animation: fadeInUp 0.5s ease-out forwards;
    }
    .delay-1 { animation-delay: 0.1s; }
    .delay-2 { animation-delay: 0.2s; }
    .delay-3 { animation-delay: 0.3s; }
    .delay-4 { animation-delay: 0.4s; }
    
    .hover-scale {
      transition: transform 0.3s ease;
    }
    .hover-scale:hover {
      transform: translateY(-5px);
    }
  `;

  return (
    <div className="py-10">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {courseCards.map((card, index) => (
          <div 
            key={card.id} 
            className={`card-animate delay-${index + 1} bg-gray-800 rounded-xl overflow-hidden shadow-lg hover-scale border border-gray-700`}
            onClick={() => onCardSelect(card.id)}
          >
            <div className={`h-2 bg-gradient-to-r ${card.color}`}></div>
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${card.color} flex items-center justify-center text-white shadow-md`}>
                  <i className={`${card.icon} text-xl`}></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-bold text-white">{card.title}</h3>
                  <p className="text-sm text-gray-400">{card.subtitle}</p>
                </div>
              </div>
              <p className="text-gray-300 mb-5 h-16">{card.description}</p>
              <button 
                className={`w-full py-2 rounded-lg bg-gradient-to-r ${card.color} text-white font-medium hover:shadow-md transition duration-300`}
                onClick={(e) => {
                  e.stopPropagation();
                  onCardSelect(card.id);
                }}
              >
                {card.btnText}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SystemDesignCards;
