import { useState } from "react";
import toast from "react-hot-toast";
import { FaCamera } from "react-icons/fa";
import axiosInstance from "../../utils/axiosInstance";
import { useDispatch, useSelector } from "react-redux";
import { Spinner } from "../../components/ui";
import { userLoggedIn } from "../../features/authSlice";

const EditProfilePage = () => {
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch()
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    gender: "",
    phone: "",
    country: "",
    city: "",
    image: null,
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setFormData((prevData) => ({
      ...prevData,
      image: file,
    }));
  };

  const validateFields = () => {
    let newErrors = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[0-9]{10}$/;

    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    else if (!emailRegex.test(formData.email))
      newErrors.email = "Invalid email format";
    if (!formData.gender) newErrors.gender = "Gender is required";
    if (!formData.phone.trim()) newErrors.phone = "Phone number is required";
    else if (!phoneRegex.test(formData.phone))
      newErrors.phone = "Invalid phone number";
    if (!formData.country.trim()) newErrors.country = "Country is required";
    if (!formData.city.trim()) newErrors.city = "City is required";
    if (!formData.image) toast.error("Please select an image");

    setErrors(newErrors);
    setTimeout(() => setErrors({}), 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;
    setLoading(true);

    const formDataToSend = new FormData();
    for (const key in formData) {
      formDataToSend.append(key, formData[key]);
    }

    try {
      const { data } = await axiosInstance.patch(
        "http://localhost:8000/api/v1/auth/user/edit-profile",
        formDataToSend,
        {
          withCredentials: true,
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      console.log(data)
      if (data.success) {
        
        dispatch(userLoggedIn({
          accessToken: data.accessToken,
          user: data.updatedUser,
        }))
        
        toast.success(data.message);
        setFormData({
          name: "",
          email: "",
          gender: "",
          phone: "",
          country: "",
          city: "",
          image: null,
        });
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error(
        "Error updating profile:",
        error.response?.data || error.message
      );
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen w-full py-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      {loading ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
          </div>
        </div>
      ) : (
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-cyan-200 bg-clip-text text-transparent mb-2">
              ✨ Edit Your Profile
            </h1>
            <p className="text-slate-400 text-lg">Update your information and make it shine</p>
          </div>

          {/* Main Form Card */}
          <div className="relative bg-gradient-to-br from-slate-800/90 via-blue-900/80 to-purple-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500 group overflow-hidden z-10">
            {/* Animated background elements */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-cyan-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl transform translate-x-16 -translate-y-16 z-0"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-cyan-400/20 to-blue-400/20 rounded-full blur-2xl transform -translate-x-12 translate-y-12 z-0"></div>

            {/* Profile Image Section - Separate Card */}
            <div className="relative z-20 mb-8">
              <div className="bg-gradient-to-br from-indigo-900/60 via-purple-800/70 to-pink-900/60 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-indigo-500/30 hover:border-purple-400/50 transition-all duration-500 group">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-purple-600/5 to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

                <h3 className="text-xl font-bold bg-gradient-to-r from-indigo-200 to-purple-200 bg-clip-text text-transparent mb-6 flex items-center gap-2 relative z-10">
                  🖼️ Profile Picture
                </h3>

                <div className="flex flex-col md:flex-row items-center gap-6 relative z-30">
                  {/* Current Image Display */}
                  <div className="flex flex-col items-center gap-3 relative z-40">
                    <div className="relative group/avatar z-50">
                      <div className="w-24 h-24 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full p-1 animate-pulse relative z-50">
                        <div className="w-full h-full bg-slate-800 rounded-full overflow-hidden relative z-50">
                          <img
                            src={
                              formData.image
                                ? URL.createObjectURL(formData.image)
                                : user?.image || "/images/logonew.png"
                            }
                            alt="user"
                            className="w-full h-full object-cover relative z-50"
                          />
                        </div>
                      </div>
                      <div className="absolute inset-0 bg-black/20 rounded-full opacity-0 group-hover/avatar:opacity-100 transition-opacity duration-300 flex items-center justify-center z-50">
                        <span className="text-white text-xs font-semibold">Preview</span>
                      </div>
                    </div>
                    <span className="text-slate-400 text-xs relative z-40">Current Photo</span>
                  </div>

                  {/* Upload Controls */}
                  <div className="flex-1 space-y-4 relative z-30">
                    <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl p-4 border border-slate-600/50 relative z-30">
                      <h4 className="text-white font-semibold mb-3 flex items-center gap-2 relative z-30">
                        📤 Upload New Photo
                      </h4>

                      <label
                        htmlFor="fileInput"
                        className="flex flex-col items-center justify-center w-full h-28 border-2 border-dashed border-purple-400/50 rounded-xl cursor-pointer bg-purple-900/20 hover:bg-purple-800/30 transition-all duration-300 group/upload relative z-30"
                      >
                        <div className="flex flex-col items-center justify-center pt-4 pb-4 relative z-30">
                          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-2 group-hover/upload:scale-110 transition-transform duration-300 relative z-30">
                            <FaCamera className="text-white text-sm" />
                          </div>
                          <p className="mb-1 text-sm text-purple-200 font-semibold relative z-30">
                            <span>Click to upload</span>
                          </p>
                          <p className="text-xs text-slate-400 relative z-30">PNG, JPG or JPEG (MAX. 5MB)</p>
                        </div>
                        <input
                          id="fileInput"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleImageChange}
                        />
                      </label>

                      {formData.image && (
                        <div className="mt-3 p-3 bg-green-900/30 border border-green-500/30 rounded-lg relative z-30">
                          <p className="text-green-300 text-sm flex items-center gap-2 relative z-30">
                            ✅ New image selected: {formData.image.name}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="text-xs text-slate-400 space-y-1 relative z-30">
                      <p>• Use a clear, high-quality photo</p>
                      <p>• Square images work best</p>
                      <p>• Avoid blurry or dark photos</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Personal Information Section */}
            <div className="relative z-20 mb-8">
              <div className="bg-gradient-to-br from-blue-900/60 via-cyan-800/70 to-teal-900/60 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-blue-500/30 hover:border-cyan-400/50 transition-all duration-500 group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-cyan-600/5 to-teal-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

                <h3 className="text-xl font-bold bg-gradient-to-r from-blue-200 to-cyan-200 bg-clip-text text-transparent mb-6 flex items-center gap-2 relative z-10">
                  👤 Personal Information
                </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Name Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-cyan-200 flex items-center gap-2">
                        👤 Full Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        placeholder="Enter your full name..."
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 focus:outline-none transition-all duration-300"
                      />
                      {errors.name && (
                        <p className="text-red-400 text-sm flex items-center gap-1">
                          ⚠️ {errors.name}
                        </p>
                      )}
                    </div>

                    {/* Email Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-cyan-200 flex items-center gap-2">
                        📧 Email Address
                      </label>
                      <input
                        type="email"
                        name="email"
                        placeholder="Enter your email..."
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 focus:outline-none transition-all duration-300"
                      />
                      {errors.email && (
                        <p className="text-red-400 text-sm flex items-center gap-1">
                          ⚠️ {errors.email}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            {/* Additional Fields Section */}
            <div className="relative z-20 mb-8">
              <div className="bg-gradient-to-br from-emerald-900/60 via-green-800/70 to-teal-900/60 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-emerald-500/30 hover:border-green-400/50 transition-all duration-500 group">
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/5 via-green-600/5 to-teal-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

                <h3 className="text-xl font-bold bg-gradient-to-r from-emerald-200 to-green-200 bg-clip-text text-transparent mb-6 flex items-center gap-2 relative z-10">
                  📋 Additional Details
                </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Gender Select */}
                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-emerald-200 flex items-center gap-2">
                        ⚧️ Gender
                      </label>
                      <select
                        name="gender"
                        value={formData.gender}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-emerald-400/50 focus:ring-2 focus:ring-emerald-400/20 focus:outline-none transition-all duration-300 cursor-pointer"
                      >
                        <option value="" className="bg-slate-800">Select Gender</option>
                        <option value="male" className="bg-slate-800">👨 Male</option>
                        <option value="female" className="bg-slate-800">👩 Female</option>
                        <option value="other" className="bg-slate-800">🏳️‍⚧️ Other</option>
                      </select>
                      {errors.gender && (
                        <p className="text-red-400 text-sm flex items-center gap-1">
                          ⚠️ {errors.gender}
                        </p>
                      )}
                    </div>

                    {/* Phone Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-emerald-200 flex items-center gap-2">
                        📱 Phone Number
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        placeholder="Enter your phone number..."
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-emerald-400/50 focus:ring-2 focus:ring-emerald-400/20 focus:outline-none transition-all duration-300"
                      />
                      {errors.phone && (
                        <p className="text-red-400 text-sm flex items-center gap-1">
                          ⚠️ {errors.phone}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Location Fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Country Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-emerald-200 flex items-center gap-2">
                        🌍 Country
                      </label>
                      <input
                        type="text"
                        name="country"
                        placeholder="Enter your country..."
                        value={formData.country}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-emerald-400/50 focus:ring-2 focus:ring-emerald-400/20 focus:outline-none transition-all duration-300"
                      />
                      {errors.country && (
                        <p className="text-red-400 text-sm flex items-center gap-1">
                          ⚠️ {errors.country}
                        </p>
                      )}
                    </div>

                    {/* City Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-emerald-200 flex items-center gap-2">
                        🏙️ City
                      </label>
                      <input
                        type="text"
                        name="city"
                        placeholder="Enter your city..."
                        value={formData.city}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-emerald-400/50 focus:ring-2 focus:ring-emerald-400/20 focus:outline-none transition-all duration-300"
                      />
                      {errors.city && (
                        <p className="text-red-400 text-sm flex items-center gap-1">
                          ⚠️ {errors.city}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-6">
                    <button
                      onClick={handleSubmit}
                      disabled={loading}
                      className="w-full relative px-8 py-4 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 font-bold text-lg tracking-wide disabled:opacity-50 disabled:cursor-not-allowed group/btn overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-emerald-700 via-green-700 to-teal-700 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                      <span className="relative z-10 flex items-center justify-center gap-2">
                        {loading ? (
                          <>
                            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                            Updating Profile...
                          </>
                        ) : (
                          <>
                            ✨ Update Profile
                          </>
                        )}
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

          {/* Tips Card */}
          <div className="mt-8 bg-gradient-to-br from-emerald-900/60 via-slate-800/80 to-teal-900/60 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-emerald-500/30 hover:border-emerald-400/50 transition-all duration-500">
            <h3 className="text-xl font-bold text-emerald-200 mb-4 flex items-center gap-2">
              💡 Profile Tips
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-emerald-400">✓</span>
                <span>Use a clear, professional profile photo</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-emerald-400">✓</span>
                <span>Keep your contact information up to date</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-emerald-400">✓</span>
                <span>Use your real name for better recognition</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-emerald-400">✓</span>
                <span>Double-check your email for notifications</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditProfilePage;
