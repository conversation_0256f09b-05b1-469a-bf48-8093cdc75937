import { useState } from "react";
import toast from "react-hot-toast";
import { FaCamera } from "react-icons/fa";
import axiosInstance from "../../utils/axiosInstance";
import { useDispatch, useSelector } from "react-redux";
import { Spinner } from "../../components/ui";
import { userLoggedIn } from "../../features/authSlice";

const EditProfilePage = () => {
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch()
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    gender: "",
    phone: "",
    country: "",
    city: "",
    image: null,
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setFormData((prevData) => ({
      ...prevData,
      image: file,
    }));
  };

  const validateFields = () => {
    let newErrors = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[0-9]{10}$/;

    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    else if (!emailRegex.test(formData.email))
      newErrors.email = "Invalid email format";
    if (!formData.gender) newErrors.gender = "Gender is required";
    if (!formData.phone.trim()) newErrors.phone = "Phone number is required";
    else if (!phoneRegex.test(formData.phone))
      newErrors.phone = "Invalid phone number";
    if (!formData.country.trim()) newErrors.country = "Country is required";
    if (!formData.city.trim()) newErrors.city = "City is required";
    if (!formData.image) toast.error("Please select an image");

    setErrors(newErrors);
    setTimeout(() => setErrors({}), 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;
    setLoading(true);

    const formDataToSend = new FormData();
    for (const key in formData) {
      formDataToSend.append(key, formData[key]);
    }

    try {
      const { data } = await axiosInstance.patch(
        "http://localhost:8000/api/v1/auth/user/edit-profile",
        formDataToSend,
        {
          withCredentials: true,
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      console.log(data)
      if (data.success) {
        
        dispatch(userLoggedIn({
          accessToken: data.accessToken,
          user: data.updatedUser,
        }))
        
        toast.success(data.message);
        setFormData({
          name: "",
          email: "",
          gender: "",
          phone: "",
          country: "",
          city: "",
          image: null,
        });
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error(
        "Error updating profile:",
        error.response?.data || error.message
      );
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {loading ? (
        <Spinner />
      ) : (
        <div className="edit-profile">
          <div className="edit-card">
            <h1>Edit Profile</h1>
            <div className="user-profile">
              <div className="edit-avatar">
                <img
                  src={
                    formData.image
                      ? URL.createObjectURL(formData.image)
                      : user?.image
                  }
                  alt="user"
                  style={{ borderRadius: "50%" }}
                />
                <label htmlFor="fileInput" className="placed-avatar">
                  <FaCamera style={{ color: "#007acc" }} />
                </label>
                <input
                  id="fileInput"
                  type="file"
                  accept="image/*"
                  style={{ display: "none" }}
                  onChange={handleImageChange}
                />
              </div>
              <div className="profile-info">
                <input
                  type="text"
                  name="name"
                  placeholder="Enter Name..."
                  value={formData.name}
                  onChange={handleChange}
                />
                {errors.name && <p className="error-message">{errors.name}</p>}
                <input
                  type="email"
                  name="email"
                  placeholder="Enter Email..."
                  value={formData.email}
                  onChange={handleChange}
                />
                {errors.email && (
                  <p className="error-message">{errors.email}</p>
                )}
              </div>
            </div>
            <div className="extra-field">
              <select
                name="gender"
                value={formData.gender}
                onChange={handleChange}
              >
                <option value="">Select Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
              {errors.gender && (
                <p className="error-message">{errors.gender}</p>
              )}

              <input
                type="tel"
                name="phone"
                placeholder="Phone Number"
                value={formData.phone}
                onChange={handleChange}
              />
              {errors.phone && <p className="error-message">{errors.phone}</p>}
              <div className="country-city">
                <input
                  type="text"
                  name="country"
                  placeholder="Country"
                  value={formData.country}
                  onChange={handleChange}
                />
                {errors.country && (
                  <p className="error-message">{errors.country}</p>
                )}
                <input
                  type="text"
                  name="city"
                  placeholder="City"
                  value={formData.city}
                  onChange={handleChange}
                />
                {errors.city && <p className="error-message">{errors.city}</p>}
              </div>
              <button
                style={{
                  width: "100%",
                  height: "45px",
                  border: "none",
                  outline: "none",
                  borderRadius: "10px",
                  backgroundColor: "#009cff",
                  fontSize: "18px",
                  color: "#fff",
                  fontWeight: "700",
                }}
                onClick={handleSubmit}
              >
                Edit Profile
              </button>
            </div>
          </div>
        </div>
      )}
      <br />
      <br />
    </div>
  );
};

export default EditProfilePage;
