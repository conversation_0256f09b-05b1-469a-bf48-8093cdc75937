import React from 'react';
import { motion } from 'framer-motion';

// Course type specific icons as components
const PythonIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-blue-400" fill="currentColor">
    <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zm5.696 5.858c.093.003.178.014.256.036.119.033.237.087.348.17.21.157.368.392.403.7.036.308-.052.637-.27.942-.085.12-.195.23-.327.334l-.043.032c-.698.52-1.833.591-2.618.162-.83-.453-1.282-1.443-1.095-2.325.168-.788.886-1.328 1.766-1.363.146-.007.291-.002.433.016.423.054.798.192 1.092.406.076.055.137.103.181.142.106.094.158.142.172.146.031.008.059-.016.078-.072.013-.038.019-.085.018-.141 0-.113-.026-.274-.083-.444-.096-.287-.26-.507-.52-.675a1.96 1.96 0 00-.7-.27 2.432 2.432 0 00-.51-.051c-.09 0-.174.004-.254.012l.072-.003zm-9.697.597c-.731.004-1.35.447-1.62 1.149-.286.746-.172 1.633.298 2.298.494.7 1.343 1.106 2.225 1.064.882-.042 1.68-.517 2.093-1.249.31-.551.355-1.188.13-1.759a1.892 1.892 0 00-.094-.194l-.04-.069c-.01-.016-.014-.024-.015-.026a.26.26 0 01-.007-.02c-.006-.016-.003-.03.009-.04.042-.036.171-.03.347.024.209.064.44.187.666.36.067.051.131.104.191.16.305.284.492.62.557 1.005.066.39-.003.8-.205 1.204-.205.41-.533.77-.96 1.054-.115.076-.24.147-.37.211-.244.12-.497.209-.752.262-.37.077-.739.091-1.095.04-.698-.101-1.332-.441-1.77-.98a2.564 2.564 0 01-.517-.972c-.063-.204-.098-.41-.104-.611a2.27 2.27 0 01.062-.676c.063-.239.16-.462.287-.668.267-.433.66-.762 1.144-.96.52-.213 1.108-.26 1.682-.142.144.03.284.07.42.122.216.081.419.189.603.321.272.196.502.435.68.71.075.115.139.236.193.36.103.237.174.484.212.737" />
  </svg>
);

const AIIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-blue-400" fill="currentColor">
    <path d="M12 2C7.58 2 4 5.58 4 10c0 2.03.76 3.87 2 5.28V20c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2v-4.72c1.24-1.41 2-3.25 2-5.28 0-4.42-3.58-8-8-8zm4 13.5c-.06.17-.15.33-.26.47l-.06.06c-.09.09-.19.16-.3.22-.12.06-.24.09-.38.09h-6c-.14 0-.26-.03-.38-.09-.11-.06-.21-.13-.3-.22l-.06-.06c-.11-.14-.2-.3-.26-.47-.06-.16-.09-.33-.09-.5V15c0-.28.11-.53.29-.71l.29-.29h6.86l.29.29c.18.18.29.43.29.71v.5c.01.17-.02.34-.08.5zm-4-4.5H8c-.55 0-1-.45-1-1s.45-1 1-1h8c.55 0 1 .45 1 1s-.45 1-1 1zm0-3H8c-.55 0-1-.45-1-1s.45-1 1-1h8c.55 0 1 .45 1 1s-.45 1-1 1z" />
  </svg>
);

const FullStackIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-blue-400" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
  </svg>
);

const DataScienceIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-blue-400" fill="currentColor">
    <path d="M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zm0 6c-4.42 0-8-1.79-8-4v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4zm0 5c-4.42 0-8-1.79-8-4v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4z" />
  </svg>
);

const DefaultIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-blue-400/50" fill="currentColor">
    <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z" />
  </svg>
);

const CourseIcons = {
  python: PythonIcon,
  ai: AIIcon,
  fullstack: FullStackIcon,
  datascience: DataScienceIcon,
  machinelearning: AIIcon,
  default: DefaultIcon
};

const HotCourses = ({ courses = [] }) => {
  const getIconComponent = (courseType) => {
    if (!courseType) return <DefaultIcon />;
    const type = courseType.toLowerCase().replace(/[^a-z]/g, '');
    const IconComponent = CourseIcons[type] || CourseIcons.default;
    return <IconComponent />;
  };

  return (
    <motion.div
      className="bg-gradient-to-br from-slate-900/10 via-blue-900/10 to-slate-900/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10 shadow-[0_8px_30px_rgb(0,0,0,0.12)]"
      whileHover={{ scale: 1.01 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="relative w-12 h-12 flex items-center justify-center">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-[0_0_15px_rgba(234,88,12,0.5)] animate-pulse" />
          <svg viewBox="0 0 24 24" className="w-7 h-7 text-white relative z-10" fill="currentColor">
            <path d="M19.48 12.35c-1.57-4.08-7.16-4.3-5.81-10.23.1-.44-.37-.78-.75-.55C9.29 3.71 6.68 8 8.87 13.62c.18.46-.36.89-.75.59-1.81-1.37-2-3.34-1.84-4.75.06-.52-.62-.77-.91-.34C4.69 10.16 4 11.84 4 14.37c.38 5.6 5.11 7.32 6.81 7.54 2.43.31 5.06-.14 6.95-1.87 2.08-1.93 2.84-5.01 1.72-7.69zm-9.28 5.03c1.44-.35 2.18-1.39 2.38-2.31.33-1.43-.96-2.83-.09-5.09.33 1.87 3.27 3.04 3.27 5.08.08 2.53-2.66 4.7-5.56 2.32z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.3)]">
          Hot Courses
        </h2>
      </div>
      <div className="space-y-4">
        {courses.map((course, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="group flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-slate-800/30 to-blue-900/30 hover:from-slate-800/40 hover:to-blue-900/40 transition-all duration-300 cursor-pointer backdrop-blur-sm border border-white/10 hover:border-white/20"
          >
            <div className="relative overflow-hidden rounded-lg flex items-center justify-center w-24 h-16 bg-slate-800/50">
              {getIconComponent(course.type)}
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-white group-hover:text-blue-400 transition-colors duration-300">
                {course.title}
              </h3>
              <div className="flex items-center gap-3 mt-2">
                <span className="bg-blue-500/20 px-3 py-1 rounded-full text-sm text-white border border-blue-500/30 shadow-[0_0_10px_rgba(59,130,246,0.2)]">
                  {course.students} students
                </span>
                <div className="flex items-center gap-2 bg-yellow-500/20 px-3 py-1 rounded-full border border-yellow-500/30 shadow-[0_0_10px_rgba(234,179,8,0.2)]">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-yellow-500" fill="currentColor">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                  <span className="text-sm text-white">{course.rating}</span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default HotCourses;
