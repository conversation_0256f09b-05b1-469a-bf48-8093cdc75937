const iotCaseStudies = [
  {
    title: "IoT Data Analysis",
    objective: "Learn IoT data processing and analysis",
    scenario: "Analyze sensor data from IoT devices",
    keyConcepts: ["Sensor Data", "Real-time Processing", "Edge Computing", "Time Series Analysis"],
    solution: `# IoT Data Analysis Example
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from statsmodels.tsa.arima.model import ARIMA

# Load sensor data
def process_sensor_data(data):
    # Convert to time series
    df = pd.DataFrame(data, columns=['timestamp', 'value'])
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    # Resample to regular intervals
    df_resampled = df.resample('1min').mean()
    
    # Handle missing values
    df_resampled.interpolate(method='time', inplace=True)
    
    return df_resampled

# Anomaly detection
def detect_anomalies(data, threshold=3):
    # Standardize data
    scaler = StandardScaler()
    values_scaled = scaler.fit_transform(data.values.reshape(-1, 1))
    
    # Mark anomalies
    anomalies = np.abs(values_scaled) > threshold
    return anomalies.flatten()

# Time series forecasting
def forecast_values(data, steps=10):
    model = ARIMA(data, order=(1,1,1))
    results = model.fit()
    forecast = results.forecast(steps=steps)
    return forecast
`
  }
];

export default iotCaseStudies;
