export const sixtyQuestions = [
    {
        question: "1. Implement a Linked List",
        answer:
            "A linked list has nodes containing data and a reference to the next node.",
    },
    {
        question: "2. Explain Depth-First Search (DFS)",
        answer:
            "DFS explores as far as possible along each branch before backtracking.",
    },
    {
        question: "3. Explain the concept of Breadth-First Search (BFS)",
        answer:
            "BFS explores all neighbors at the present depth before moving to the next depth level.",
    },
    {
        question: "4. Implement Binary Search",
        answer:
            "Binary Search involves dividing a sorted array in half repeatedly until the target element is found.",
    },
    {
        question: "5. Explain Merge Sort Algorithm",
        answer:
            "Merge Sort divides the array into halves, sorts each half, and merges them in sorted order.",
    },
    {
        question: "6. What is Quick Sort?",
        answer:
            "Quick Sort selects a pivot, partitions the array around it, and recursively sorts each partition.",
    },
    {
        question: "7. Implement a Stack Using Queues",
        answer: "Use two queues to emulate stack behavior (LIFO).",
    },
    {
        question: "8. Implement a Queue Using Stacks",
        answer: "Use two stacks to achieve queue behavior (FIFO).",
    },
    {
        question: "9. Explain the concept of a Binary Search Tree (BST)",
        answer:
            "A BST is a tree structure where each node has values greater than its left children and less than its right.",
    },
    {
        question: "10. Implement Heap Sort",
        answer:
            "Heap Sort builds a max heap and repeatedly extracts the maximum element to sort the array.",
    },
    {
        question: "11. Explain the Two-Pointer Technique",
        answer:
            "Two-pointer technique uses two pointers moving at different speeds to solve problems efficiently.",
    },
    {
        question: "12. Find the Lowest Common Ancestor in a Binary Tree",
        answer:
            "Use recursion to trace ancestors of two nodes and find the first common ancestor.",
    },
    {
        question: "13. Describe Floyd’s Cycle-Finding Algorithm",
        answer:
            "Floyd's algorithm detects cycles in linked lists using two pointers moving at different speeds.",
    },
    {
        question: "14. Implement the KMP Pattern Matching Algorithm",
        answer:
            "KMP uses a partial match table to skip unnecessary comparisons in pattern matching.",
    },
    {
        question: "15. Explain Dynamic Programming (DP)",
        answer:
            "DP solves complex problems by breaking them into overlapping subproblems and storing results.",
    },
    {
        question: "16. Explain Dijkstra’s Algorithm",
        answer:
            "Dijkstra's algorithm finds the shortest path from a source node to other nodes in a weighted graph.",
    },
    {
        question: "17. Explain the Greedy Algorithm",
        answer:
            "Greedy algorithms make optimal choices at each step to reach a global solution.",
    },
    {
        question: "18. Solve the Knapsack Problem",
        answer:
            "Use dynamic programming to select items maximizing value without exceeding weight capacity.",
    },
    {
        question: "19. Explain Topological Sort",
        answer:
            "Topological Sort orders nodes in a directed acyclic graph such that each node comes before nodes it points to.",
    },
    {
        question: "20. What is a Trie Data Structure?",
        answer:
            "A Trie is a tree-like structure for efficient storage and retrieval of strings.",
    },
    {
        question: "21. Explain the concept of Hashing",
        answer:
            "Hashing uses a hash function to map keys to values, providing efficient data retrieval.",
    },
    {
        question: "22. Implement a Binary Tree",
        answer:
            "A binary tree is a tree structure where each node has at most two children.",
    },
    {
        question: "23. What is an AVL Tree?",
        answer:
            "An AVL tree is a self-balancing binary search tree that maintains balance factors for each node.",
    },
    {
        question: "24. Explain Bubble Sort Algorithm",
        answer:
            "Bubble Sort repeatedly steps through the list, compares adjacent elements, and swaps them if necessary.",
    },
    {
        question: "25. Explain Selection Sort Algorithm",
        answer:
            "Selection Sort finds the minimum element in each pass and places it in its correct position.",
    },
    {
        question: "26. What is Insertion Sort?",
        answer:
            "Insertion Sort builds a sorted array one element at a time by comparing with previous elements.",
    },
    {
        question: "27. Explain Radix Sort Algorithm",
        answer:
            "Radix Sort processes numbers by each digit place, using a stable sort to sort by each place.",
    },
    {
        question: "28. Implement Counting Sort",
        answer:
            "Counting Sort counts occurrences of each element to sort an array efficiently.",
    },
    {
        question: "29. Explain the concept of Backtracking",
        answer:
            "Backtracking explores all possibilities and backtracks once it realizes a path is unsuitable.",
    },
    {
        question: "30. Solve N-Queens Problem",
        answer:
            "Use backtracking to place queens on a chessboard such that no two queens attack each other.",
    },
    {
        question: "31. Explain the Floyd-Warshall Algorithm",
        answer:
            "Floyd-Warshall finds shortest paths between all pairs of nodes in a weighted graph.",
    },
    {
        question: "32. Explain Bellman-Ford Algorithm",
        answer:
            "Bellman-Ford computes shortest paths from a source to all other nodes in a graph with negative weights.",
    },
    {
        question: "33. Explain Prim’s Algorithm for MST",
        answer:
            "Prim's algorithm grows a minimum spanning tree by adding the cheapest edge from the tree to a new vertex.",
    },
    {
        question: "34. Explain Kruskal’s Algorithm for MST",
        answer:
            "Kruskal's algorithm builds a minimum spanning tree by selecting the lowest-weight edges that don’t form cycles.",
    },
    {
        question: "35. What is a Graph?",
        answer:
            "A graph is a collection of nodes connected by edges, representing relationships between objects.",
    },
    {
        question: "36. Explain Binary Tree Traversals",
        answer:
            "Binary tree traversals include In-order, Pre-order, and Post-order, each with specific node visiting orders.",
    },
    {
        question: "37. Describe HashMap vs. TreeMap",
        answer:
            "HashMap provides constant-time access, while TreeMap maintains sorted keys with logarithmic time access.",
    },
    {
        question: "38. Explain Divide and Conquer Strategy",
        answer:
            "Divide and Conquer splits a problem into subproblems, solves them, and combines solutions.",
    },
    {
        question: "39. Explain Segment Tree and Applications",
        answer:
            "A segment tree efficiently handles range queries and updates on an array.",
    },
    {
        question: "40. Describe Fenwick Tree and Applications",
        answer:
            "Fenwick Tree, or Binary Indexed Tree, is useful for cumulative frequency tables and range queries.",
    },
    {
        question: "41. Explain LRU Cache",
        answer:
            "LRU Cache is a cache replacement policy that discards the least recently used items first.",
    },
    {
        question: "42. Explain Graph Coloring Problem",
        answer:
            "Graph coloring assigns colors to nodes so adjacent nodes have different colors, often using backtracking.",
    },
    {
        question: "43. What is a Max-Heap?",
        answer:
            "A max-heap is a binary tree where each node is greater than or equal to its children.",
    },
    {
        question: "44. Implement Fibonacci with Memoization",
        answer:
            "Use a dictionary to store Fibonacci values and avoid redundant calculations.",
    },
    {
        question: "45. Explain Complexity of Binary Search",
        answer:
            "Binary Search has O(log n) complexity due to its divide-and-conquer approach on a sorted array.",
    },
    {
        question: "46. Explain Time Complexity of Quick Sort",
        answer:
            "Quick Sort has an average O(n log n) time, but O(n^2) in the worst case if poorly partitioned.",
    },
    {
        question: "47. Describe Kadane’s Algorithm",
        answer:
            "Kadane’s algorithm finds the maximum sum subarray in O(n) time using dynamic programming.",
    },
    {
        question: "48. Explain Binary Search Tree (BST) vs. AVL Tree",
        answer:
            "BSTs are binary trees, while AVL trees are balanced BSTs that maintain balance factors at each node.",
    },
    {
        question: "49. Explain Shortest Path in a Grid",
        answer:
            "BFS finds the shortest path in unweighted grids, while Dijkstra’s or A* is used for weighted grids.",
    },
    {
        question: "50. Explain the concept of Memoization in Dynamic Programming",
        answer:
            "Memoization stores intermediate results to avoid recomputation, optimizing recursive functions.",
    },
    {
        question: "51. Describe the Divide and Conquer Algorithm",
        answer:
            "Divide and Conquer breaks down problems into smaller subproblems, solves them, and combines the results.",
    },
    {
        question: "52. Explain the concept of Recursion",
        answer:
            "Recursion occurs when a function calls itself to solve a problem by breaking it down into subproblems.",
    },
    {
        question: "53. What is Memoization?",
        answer:
            "Memoization stores previously computed results to avoid redundant calculations in recursive functions.",
    },
    {
        question: "54. Implement a HashMap",
        answer:
            "A HashMap uses a hash function to map keys to values for efficient retrieval.",
    },
    {
        question: "55. What is the Sliding Window Technique?",
        answer:
            "The sliding window technique processes data over a subset that slides across the dataset to find optimal results.",
    },
    {
        question: "56. Explain Graph Theory Basics",
        answer:
            "Graph theory studies vertices (nodes) and edges (connections) to model relationships in data structures.",
    },
    {
        question: "57. Implement a Graph with Adjacency List",
        answer:
            "An adjacency list represents each node and its connections in a graph for efficient storage.",
    },
    {
        question: "58. What is the Bellman-Ford Algorithm?",
        answer:
            "Bellman-Ford finds the shortest path from a single source to all nodes in a graph with possible negative weights.",
    },
    {
        question: "59. Explain Floyd-Warshall Algorithm",
        answer:
            "Floyd-Warshall finds shortest paths between all pairs of nodes in a graph with dynamic programming.",
    },
    {
        question: "60. What is a Circular Linked List?",
        answer:
            "In a circular linked list, the last node points back to the first, forming a circular structure.",
    },
];
