import { useState } from "react";
import { DeleteModal } from "../../components/ui";
const AnalyticsReport = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isOpen, setIsOpen] = useState(false);

  const casesPerPage = 5;

  const reportData = [
    {
      username: "<PERSON><PERSON><PERSON><PERSON>",
      loginFrequency: 22,
      questionsAttempted: 105,
      lastActive: "2025-03-24 18:45",
    },
    {
      username: "<PERSON><PERSON><PERSON><PERSON>",
      loginFrequency: 18,
      questionsAttempted: 95,
      lastActive: "2025-03-23 12:30",
    },
    {
      username: "<PERSON><PERSON><PERSON><PERSON>",
      loginFrequency: 30,
      questionsAttempted: 140,
      lastActive: "2025-03-25 10:00",
    },
    {
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      loginFrequency: 12,
      questionsAttempted: 70,
      lastActive: "2025-03-22 14:20",
    },
    {
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      loginFrequency: 35,
      questionsAttempted: 190,
      lastActive: "2025-03-25 09:15",
    },
    {
      username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      loginFrequency: 28,
      questionsAttempted: 130,
      lastActive: "2025-03-24 21:30",
    },
    {
      username: "Man<PERSON>j_<PERSON>",
      loginFrequency: 20,
      questionsAttempted: 110,
      lastActive: "2025-03-23 19:10",
    },
    {
      username: "Rohan_Das",
      loginFrequency: 15,
      questionsAttempted: 80,
      lastActive: "2025-03-21 16:45",
    },
    {
      username: "Kavita_Nair",
      loginFrequency: 25,
      questionsAttempted: 125,
      lastActive: "2025-03-24 13:50",
    },
    {
      username: "Arjun_Mishra",
      loginFrequency: 40,
      questionsAttempted: 210,
      lastActive: "2025-03-25 08:30",
    },
    {
      username: "Deepika_Chopra",
      loginFrequency: 18,
      questionsAttempted: 85,
      lastActive: "2025-03-22 11:15",
    },
    {
      username: "Suraj_Pandey",
      loginFrequency: 32,
      questionsAttempted: 175,
      lastActive: "2025-03-24 17:20",
    },
    {
      username: "Meera_Joshi",
      loginFrequency: 10,
      questionsAttempted: 55,
      lastActive: "2025-03-20 15:05",
    },
    {
      username: "Sandeep_Reddy",
      loginFrequency: 27,
      questionsAttempted: 140,
      lastActive: "2025-03-24 22:00",
    },
    {
      username: "Anjali_Sen",
      loginFrequency: 22,
      questionsAttempted: 120,
      lastActive: "2025-03-23 18:10",
    },
  ];

  const indexOfLastCase = currentPage * casesPerPage;
  const indexOfFirstCase = indexOfLastCase - casesPerPage;
  const currentCases = reportData.slice(indexOfFirstCase, indexOfLastCase);

  const nextPage = () => {
    if (currentPage < Math.ceil(reportData.length / casesPerPage)) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const openDeleteModal = () => {
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setIsOpen(false);
  };

  const deleteConfirmation = () => {
    alert("Analytics and Report Deleted!");
    setIsOpen(false);
  };
  return (
    <div className="bg-gray-100 min-h-screen py-10">
      <div className="w-[95%] max-w-[1100px] mx-auto p-6 rounded-xl text-gray-900 shadow-lg border border-gray-300">
        <h2 className="text-center text-3xl font-extrabold text-blue-400 mb-4">
          📊 Report & Analytics
        </h2>

        {/* Responsive Table Wrapper */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse mt-3 min-w-[600px]  text-sm sm:text-base">
            <thead className=" bg-blue-900 text-gray-900">
              <tr className="text-white uppercase">
                <th className="p-3 text-left">#</th>
                <th className="p-3 text-left">Username</th>
                <th className="p-3 text-left">Login Frequency</th>
                <th className="p-3 text-left">Questions Attempted</th>
                <th className="p-3 text-left">Last Active</th>
                <th className="p-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentCases.map((report, index) => (
                <tr
                  key={index}
                  // className={index % 2 === 0 ? "bg-[#242434]" : ""}
                >
                  <td className="p-3 border-b border-gray-600">
                    {indexOfFirstCase + index + 1}
                  </td>
                  <td className="p-3 border-b border-gray-600">
                    {report.username}
                  </td>
                  <td className="p-3 border-b border-gray-600">
                    {report.loginFrequency}
                  </td>
                  <td className="p-3 border-b border-gray-600">
                    {report.questionsAttempted}
                  </td>
                  <td className="p-3 border-b border-gray-600">
                    {report.lastActive}
                  </td>
                  <td className="p-3 border-b border-gray-600">
                    <button
                      className="px-3 py-2 bg-red-500 hover:bg-red-400 text-white rounded-md font-bold transition-transform hover:scale-105"
                      onClick={openDeleteModal}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination Buttons */}
        <div className="flex flex-wrap items-center justify-center text-center mt-4 gap-3 sm:gap-0 sm:flex-nowrap">
          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-md font-bold transition hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ⬅ Prev
          </button>

          <span className="text-lg font-bold text-blue-600 bg-blue-100 px-4 py-2 rounded-lg">
            Page {currentPage}
          </span>

          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-md font-bold transition hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={nextPage}
            disabled={
              currentPage >= Math.ceil(reportData.length / casesPerPage)
            }
          >
            Next ➡
          </button>
        </div>
      </div>

      <DeleteModal
        title={"Delete Analytics & Reports"}
        message={"Are You Sure Want to Delete?"}
        onClose={closeDeleteModal}
        isOpen={isOpen}
        onConfirm={deleteConfirmation}
      />
    </div>
  );
};

export default AnalyticsReport;
