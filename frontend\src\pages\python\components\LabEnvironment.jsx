import React, { useState } from "react";
import CaseStudy from "../CaseStudy"; // Importing the existing CaseStudy component

const LabEnvironment = ({ showPremiumOverlay }) => {
  const [activeTab, setActiveTab] = useState("setup");

  const tabs = [
    { id: "setup", label: "Environment Setup", icon: "fas fa-cog" },
    { id: "exercises", label: "Practical Exercises", icon: "fas fa-laptop-code" },
    { id: "caseStudies", label: "Case Studies", icon: "fas fa-project-diagram" }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "setup":
        return <EnvironmentSetup />;
      case "exercises":
        return <PracticalExercises showPremiumOverlay={showPremiumOverlay} />;
      case "caseStudies":
        return <CaseStudy />; // Reusing the existing CaseStudy component
      default:
        return <EnvironmentSetup />;
    }
  };

  // Animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md overflow-hidden text-white">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1e293b]/20 to-[#0f172a]/20">
        <h2 className="text-3xl font-bold mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Python Lab Environment</h2>
        <p className="text-blue-100/80">Set up your Python environment and practice with hands-on exercises and case studies</p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-white/10 flex">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-colors duration-200 relative ${
              activeTab === tab.id
                ? "text-blue-400 border-b-2 border-blue-400"
                : "text-white/70 hover:text-white"
            }`}
          >
            <i className={`${tab.icon} mr-2`}></i>
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Content */}
      <div className="p-6 fade-in">
        {renderContent()}
      </div>
    </div>
  );
};

// Environment Setup Component
const EnvironmentSetup = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Setting Up Your Python Environment</h3>
      
      <div className="rounded-lg bg-[#303246]/30 backdrop-blur-sm p-6 border border-white/10">
        <h4 className="text-xl font-semibold mb-3 text-blue-300">Why Environment Setup Matters</h4>
        <p className="text-white/80">
          Having a proper Python environment ensures consistency across development, makes package management easier, and helps avoid version conflicts.
        </p>
      </div>
      
      <div className="space-y-8 mt-6">
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-blue-500/30 text-blue-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">1</span>
            Install Python
          </h4>
          <p className="text-white/80 mb-4">
            Download and install the latest version of Python from the official website. Make sure to check "Add Python to PATH" during installation.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Verify Python installation<br />
              python --version
            </code>
          </div>
        </div>

        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-green-500/20 backdrop-blur-sm text-green-300 w-8 h-8 rounded-full flex items-center justify-center mr-3 border border-green-500/20">2</span>
            Virtual Environments
          </h4>
          <p className="text-white/80 mb-4">
            Virtual environments allow you to have an isolated space for Python projects, ensuring dependencies don't conflict.
          </p>
          <div className="bg-gray-800 text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Create a virtual environment<br />
              python -m venv myenv<br /><br />
              # Activate on Windows<br />
              myenv\Scripts\activate<br /><br />
              # Activate on macOS/Linux<br />
              source myenv/bin/activate
            </code>
          </div>
        </div>
        
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-blue-500/30 text-blue-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">3</span>
            Install Packages
          </h4>
          <p className="text-white/80 mb-4">
            Use pip to install the packages required for this course.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Install required packages<br />
              pip install numpy pandas matplotlib jupyter
            </code>
          </div>
        </div>
        
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-blue-500/30 text-blue-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">4</span>
            IDE Setup
          </h4>
          <p className="text-white/80 mb-4">
            Choose an IDE that suits your needs. We recommend Visual Studio Code with the Python extension.
          </p>
          <div className="flex flex-wrap gap-4 mt-3">
            <div className="flex items-center bg-[#303246]/40 backdrop-blur-sm p-3 rounded-md border border-white/10">
              <i className="fab fa-python text-blue-400 text-xl mr-3"></i>
              <span className="text-white">Visual Studio Code</span>
            </div>
            <div className="flex items-center bg-[#303246]/40 backdrop-blur-sm p-3 rounded-md border border-white/10">
              <i className="fas fa-graduation-cap text-green-400 text-xl mr-3"></i>
              <span className="text-white">PyCharm</span>
            </div>
            <div className="flex items-center bg-[#303246]/40 backdrop-blur-sm p-3 rounded-md border border-white/10">
              <i className="fas fa-terminal text-purple-400 text-xl mr-3"></i>
              <span className="text-white">Jupyter Notebook</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Practical Exercises Component
const PracticalExercises = ({ showPremiumOverlay }) => {
  const exercises = [
    {
      title: "Variables and Data Types",
      difficulty: "Beginner",
      description: "Practice creating variables and working with Python's basic data types.",
      premium: false
    },
    {
      title: "Control Flow and Loops",
      difficulty: "Beginner",
      description: "Use if-else statements and loops to control program execution.",
      premium: false
    },
    {
      title: "Functions and Parameters",
      difficulty: "Beginner",
      description: "Create reusable code blocks with functions and parameters.",
      premium: false
    },
    {
      title: "Lists and Dictionaries",
      difficulty: "Intermediate",
      description: "Work with Python's collection data structures to store and manipulate data.",
      premium: true
    },
    {
      title: "File Operations",
      difficulty: "Intermediate",
      description: "Read from and write to files using Python's file handling capabilities.",
      premium: true
    },
    {
      title: "Error Handling",
      difficulty: "Advanced",
      description: "Implement try-except blocks to handle errors gracefully.",
      premium: true
    },
  ];

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-white mb-4" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Practical Exercises</h3>
      <p className="text-blue-100/80">Reinforce your learning with these hands-on exercises. Complete them to master Python concepts.</p>
      
      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {exercises.map((exercise, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
            <div className="flex justify-between items-start mb-3">
              <h4 className="text-lg font-semibold text-white">{exercise.title}</h4>
              <span className={`text-xs font-medium px-3 py-1 rounded-full ${
                exercise.difficulty === "Beginner" 
                  ? "bg-green-500/20 text-green-400 border border-green-500/20" 
                  : exercise.difficulty === "Intermediate"
                    ? "bg-yellow-500/20 text-yellow-400 border border-yellow-500/20"
                    : "bg-red-500/20 text-red-400 border border-red-500/20"
              }`}>
                {exercise.difficulty}
              </span>
            </div>
            <p className="text-blue-100/80 mb-4">{exercise.description}</p>
            <button
              onClick={exercise.premium ? showPremiumOverlay : undefined}
              className={`w-full py-2 rounded-lg ${
                exercise.premium 
                  ? "bg-gradient-to-r from-yellow-500/30 to-yellow-600/30 border border-yellow-500/20" 
                  : "bg-gradient-to-r from-green-500/30 to-green-600/30 border border-green-500/20"
              } text-white font-medium hover:shadow-md transition duration-300 flex items-center justify-center gap-2 backdrop-blur-sm`}
            >
              {exercise.premium && <i className="fas fa-lock text-sm"></i>}
              {exercise.premium ? "Upgrade to Access" : "Start Exercise"}
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LabEnvironment;
