import Course from "../models/courseModal.js";
import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import ErrorHandler from "../utils/ErrorHandler.js";

//  - Create a new course
export const createCourse = CatchAsyncError(async (req, res, next) => {
  const { slug } = req.body;

  const courseExists = await Course.findOne({ slug });
  if (courseExists) {
    return next(new ErrorHandler("Course with this slug already exists", 400));
  }

  const course = await Course.create(req.body);

  res.status(201).json({
    success: true,
    message: "Course created successfully",
    course,
  });
});
// - Get all courses
export const getAllCourses = CatchAsyncError(async (req, res, next) => {
  const courses = await Course.find();

  res.status(200).json({
    success: true,
    count: courses.length,
    courses,
  });
});
// - Update a course by slug
export const updateCourse = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  console.log(slug);

  let course = await Course.findOne({ id });

  if (!course) {
    return next(new ErrorHandler("Course not found", 404));
  }

  course = await Course.findOneAndUpdate({ id }, req.body, {
    new: true,
    runValidators: true,
  });

  res.status(200).json({
    success: true,
    message: "Course updated successfully",
    course,
  });
});
// - Delete a course by slug
export const deleteCourse = CatchAsyncError(async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log("Requested slug for deletion:", id);

    const course = await Course.findOne({ id });

    if (!course) {
      console.error("Error: Course not found for slug:", slug);
      return next(new ErrorHandler("Course not found", 404));
    }

    const deletionResult = await Course.deleteOne({ id });
    console.log("Deletion result:", deletionResult);

    if (deletionResult.deletedCount === 0) {
      return next(new ErrorHandler("Failed to delete course", 500));
    }

    res.status(200).json({
      success: true,
      message: "Course deleted successfully",
    });
  } catch (err) {
    console.error("Unexpected error during course deletion:", err);
    return next(new ErrorHandler("Internal Server Error", 500));
  }
});
