import React from 'react';
import { 
  FaBook, 
  FaVideo, 
  FaUsers, 
  FaChartBar, 
  FaCog, 
  FaComments, 
  FaClipboardList,
  FaGraduationCap
} from 'react-icons/fa';

const Sidebar = ({ activeSection, setActiveSection }) => {
  // Define all sidebar menu items
  const menuItems = [
    { id: 'courses', label: 'Courses', icon: <FaBook className="mr-3" /> },
    { id: 'liveClasses', label: 'Live Classes', icon: <FaVideo className="mr-3" /> },
    { id: 'students', label: 'Students', icon: <FaUsers className="mr-3" /> },
    { id: 'instructors', label: 'Instructors', icon: <FaGraduationCap className="mr-3" /> },
    { id: 'analytics', label: 'Analytics', icon: <FaChartBar className="mr-3" /> },
    { id: 'feedback', label: 'Feedback', icon: <FaComments className="mr-3" /> },
    { id: 'reports', label: 'Reports', icon: <FaClipboardList className="mr-3" /> },
    { id: 'settings', label: 'Settings', icon: <FaCog className="mr-3" /> }
  ];

  return (
    <div className="bg-gray-800 text-white w-64 flex-shrink-0 flex flex-col">
      <div className="p-4 border-b border-gray-700 flex items-center">
        <div className="bg-blue-600 rounded-lg w-10 h-10 flex items-center justify-center mr-3">
          <span className="font-bold text-lg">A</span>
        </div>
        <h2 className="text-xl font-semibold">Admin Panel</h2>
      </div>
      
      <div className="p-4 border-b border-gray-700">
        <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">
          Main
        </div>
        <nav>
          <ul>
            {menuItems.slice(0, 2).map((item) => (
              <li key={item.id}>
                <button
                  className={`flex items-center px-4 py-3 w-full text-left rounded-lg transition-all duration-200 ${
                    activeSection === item.id 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-300 hover:bg-gray-700'
                  }`}
                  onClick={() => setActiveSection(item.id)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      <div className="p-4">
        <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">
          Other Features
        </div>
        <ul>
          {menuItems.slice(2).map((item) => (
            <li key={item.id}>
              <button
                className={`flex items-center px-4 py-3 w-full text-left rounded-lg transition-all duration-200 ${
                  activeSection === item.id 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
                onClick={() => setActiveSection(item.id)}
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            </li>
          ))}
        </ul>
      </div>
      
      <div className="mt-auto p-4 border-t border-gray-700">
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <p className="text-sm text-gray-300 mb-2">Admin Dashboard v1.0</p>
          <p className="text-xs text-gray-400">© 2025 Codexus Lab</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;