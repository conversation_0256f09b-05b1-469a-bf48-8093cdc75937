import { useState } from "react";
import { DeleteModal } from "../../components/ui";

const TestCases = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [testCases, setTestCases] = useState([
    {
      input: "",
      output: "",
      explanation: "",
      edgeCase: "",
      constraint: "",
      complexity: "",
      failingCase: "",
    },
  ]);

  const addTestCase = () => {
    setTestCases([
      ...testCases,
      {
        input: "",
        output: "",
        explanation: "",
        edgeCase: "",
        constraint: "",
        complexity: "",
        failingCase: "",
      },
    ]);
  };

  const handleChange = (index, field, value) => {
    const updatedTestCases = [...testCases];
    updatedTestCases[index][field] = value;
    setTestCases(updatedTestCases);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Submitted Test Cases:", testCases);
  };

  const [currentPage, setCurrentPage] = useState(1);
  const casesPerPage = 5;

  const testCasesData = Array.from({ length: 15 }, (_, index) => ({
    input: `Input ${index + 1}`,
    output: `Output ${index + 1}`,
    explanation: `Explanation ${index + 1}`,
    edgeCase: `Edge Case ${index + 1}`,
    constraint: `Constraint ${index + 1}`,
    complexity: `O(N)`,
    failingCase: `Failing Case ${index + 1}`,
  }));

  const indexOfLastCase = currentPage * casesPerPage;
  const indexOfFirstCase = indexOfLastCase - casesPerPage;
  const currentCases = testCasesData.slice(indexOfFirstCase, indexOfLastCase);

  const nextPage = () => {
    if (currentPage < Math.ceil(testCasesData.length / casesPerPage)) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const openDeleteModal = () => {
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setIsOpen(false);
  };

  const deleteConfirmation = () => {
    alert("Test Cases Deleted!");
    setIsOpen(false);
  };

  return (
    <div>
      {/* Test Case Form */}
      <div className="w-11/12 max-w-5xl mx-auto bg-white p-6 rounded-lg shadow-md border border-gray-300">
        <h2 className="text-center text-2xl font-bold mb-4 text-blue-500 uppercase">
          Test Case Form
        </h2>
        <form onSubmit={handleSubmit}>
          {testCases.map((testCase, index) => (
            <div
              key={index}
              className="bg-gray-100 p-4 rounded-md mb-4 shadow-sm transition-transform duration-300 hover:shadow-md"
            >
              {[
                "input",
                "output",
                "edgeCase",
                "constraint",
                "complexity",
                "failingCase",
              ].map((field) => (
                <input
                  key={field}
                  type="text"
                  placeholder={field
                    .replace(/([A-Z])/g, " $1")
                    .replace(/^./, (c) => c.toUpperCase())}
                  value={testCase[field]}
                  onChange={(e) => handleChange(index, field, e.target.value)}
                  className="w-full p-2 mb-3 rounded border border-gray-300 bg-white text-black outline-none focus:border-blue-400 focus:ring-2 focus:ring-blue-200"
                />
              ))}
              <textarea
                placeholder="Explanation"
                value={testCase.explanation}
                onChange={(e) =>
                  handleChange(index, "explanation", e.target.value)
                }
                className="w-full p-2 mb-3 rounded border border-gray-300 bg-white text-black outline-none resize-none h-24 focus:border-blue-400 focus:ring-2 focus:ring-blue-200"
              />
            </div>
          ))}
          <button
            type="button"
            onClick={addTestCase}
            className="px-4 py-2 bg-green-500 text-white font-bold rounded-md transition hover:bg-green-600 hover:scale-105"
          >
            Add Test Case
          </button>
        </form>
      </div>

      <br />
      <br />

      {/* Test Case List Table */}
      <div className="w-11/12 max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-md border border-gray-300">
        <h2 className="text-center text-2xl font-bold mb-4 text-blue-500">
          Test Cases List
        </h2>

        <div className="overflow-x-auto">
          <table className="w-full border-collapse text-sm sm:text-base min-w-[800px]">
            <thead className="bg-blue-900 text-white">
              <tr>
                <th className="p-3 text-left">#</th>
                <th className="p-3 text-left">Input</th>
                <th className="p-3 text-left">Expected Output</th>
                <th className="p-3 text-left">Explanation</th>
                <th className="p-3 text-left">Edge Cases</th>
                <th className="p-3 text-left">Constraints</th>
                <th className="p-3 text-left">Time Complexity</th>
                <th className="p-3 text-left">Failing Cases</th>
                <th className="p-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentCases.map((testCase, index) => (
                <tr key={index} className="even:bg-gray-100 odd:bg-white">
                  <td className="p-3">{indexOfFirstCase + index + 1}</td>
                  <td className="p-3 break-words">{testCase.input}</td>
                  <td className="p-3 break-words">{testCase.output}</td>
                  <td className="p-3 break-words">{testCase.explanation}</td>
                  <td className="p-3 break-words">{testCase.edgeCase}</td>
                  <td className="p-3 break-words">{testCase.constraint}</td>
                  <td className="p-3 break-words">{testCase.complexity}</td>
                  <td className="p-3 break-words">{testCase.failingCase}</td>
                  <td className="p-3 whitespace-nowrap">
                    <button className="px-3 py-1 m-1 bg-yellow-400 text-black font-bold rounded-md hover:scale-105 hover:bg-yellow-500 transition">
                      Edit
                    </button>
                    <button
                      className="px-3 py-1 m-1 bg-red-500 text-white font-bold rounded-md hover:scale-105 hover:bg-red-600 transition"
                      onClick={openDeleteModal}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        <div className="flex justify-center items-center gap-4 mt-4">
          <button
            onClick={prevPage}
            disabled={currentPage === 1}
            className="px-4 py-2 rounded bg-blue-500 text-white font-bold hover:bg-blue-600 transition disabled:bg-gray-400"
          >
            ⬅ Prev
          </button>
          <span className="text-lg font-bold text-blue-600 bg-blue-100 px-4 py-2 rounded-lg">
            Page {currentPage}
          </span>
          <button
            onClick={nextPage}
            disabled={
              currentPage >= Math.ceil(testCasesData.length / casesPerPage)
            }
            className="px-4 py-2 rounded bg-blue-500 text-white font-bold hover:bg-blue-600 transition disabled:bg-gray-400"
          >
            Next ➡
          </button>
        </div>
      </div>

      {/* Delete Modal */}
      <DeleteModal
        title={"Delete Test Cases"}
        message={"Are You Sure Want to Delete?"}
        onClose={closeDeleteModal}
        isOpen={isOpen}
        onConfirm={deleteConfirmation}
      />
    </div>
  );
};

export default TestCases;
