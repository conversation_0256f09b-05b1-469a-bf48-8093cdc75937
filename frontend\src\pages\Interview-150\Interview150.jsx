import { useState } from "react";
import { sixtyQuestions } from "../../utils/SixtyQuestion";

const Interview150 = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleAnswer = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  return (
    <div className="main-div">
      <div
        className="headers"
        style={{
          display: "flex",
          alignItems: "center",
          position: "fixed",
          top: "0",
          width: "100%",
          padding: "10px",
          boxSizing: "border-box",
          gap: "0",
          backgroundColor: "white",
          zIndex: "1000",
        }}
      >
        <a
          href="/"
          style={{
            display: "flex",
            alignItems: "center",
            textDecoration: "none",
          }}
        >
          <img
            src="images\logonew.png"
            alt="Logo"
            style={{ maxHeight: "30px", marginRight: "10px" }}
          />
          <h1
            className="display-4"
            style={{
              margin: "0",
              color: "black",
              fontSize: "1.5rem",
              fontWeight: "700",
            }}
          >
            Upcoding
          </h1>
        </a>
      </div>
      <div className="spacer"></div>
      <div className="container">
        <div className="headers">
          <h1>Top 50 Interview Questions - MNCs</h1>
          <div className="logo-container">
            <img
              className="logo"
              src="images\interviewlogo.png"
              width="350"
              height="290"
              alt="DSA Logo"
            />
            <p className="lead mb-0">
              Comprehensive guide to frequently asked interview questions in top
              MNCs
            </p>
          </div>
          <br />
          <div className="section">
            <h2 className="section-title" id="interview-checklist">
              <i className="fas fa-clipboard-check"></i>
              Interview Checklist
            </h2>
            <ul>
              {sixtyQuestions.map((item, index) => (
                <li key={index}>
                  <div className="question" onClick={() => toggleAnswer(index)}>
                    {item.question}
                    <i
                      className={`fas fa-chevron-circle-down toggle-icon ${
                        openIndex === index ? "open" : ""
                      }`}
                    ></i>
                  </div>
                  {openIndex === index && (
                    <div className="answer">Answer: {item.answer}</div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Interview150;
