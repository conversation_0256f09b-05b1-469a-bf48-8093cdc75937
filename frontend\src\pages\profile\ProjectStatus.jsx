
import { useState } from "react";
import { FaLaptopCode, FaCheckCircle, FaRocket, FaFire } from "react-icons/fa";

// Import components
import StatsCard from "./components/StatsCard";
import NavigationTabs from "./components/NavigationTabs";
import ProjectCard from "./components/ProjectCard";
import EmptyState from "./components/EmptyState";
import QuickActions from "./components/QuickActions";

// Import utilities and data
import { calculateDaysRemaining, getProjectStats } from "./utils/projectUtils";
import { mockProjects } from "./data/mockProjects";

const ProjectStatus = () => {
  const [activeTab, setActiveTab] = useState("active");
  const [projects] = useState(mockProjects);
  const stats = getProjectStats(projects);

  const statsData = [
    {
      icon: FaLaptopCode,
      title: "Total Projects",
      value: stats.totalProjects,
      subtitle: "All time projects",
      gradientFrom: "from-purple-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-purple-500",
      iconTo: "to-purple-600"
    },
    {
      icon: FaCheckCircle,
      title: "Completed",
      value: stats.completedProjects,
      subtitle: `${stats.completionRate}% completion rate`,
      gradientFrom: "from-green-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-green-500",
      iconTo: "to-emerald-600"
    },
    {
      icon: FaRocket,
      title: "Active",
      value: stats.activeProjects,
      subtitle: "In development",
      gradientFrom: "from-blue-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-blue-500",
      iconTo: "to-cyan-600"
    },
    {
      icon: FaFire,
      title: "Time Spent",
      value: `${stats.totalTimeSpent}h`,
      subtitle: "Total coding time",
      gradientFrom: "from-orange-900/60",
      gradientTo: "to-slate-800/80",
      iconFrom: "from-orange-500",
      iconTo: "to-amber-600"
    }
  ];

  return (
    <div className="min-h-screen w-full py-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-200 via-blue-200 to-cyan-200 bg-clip-text text-transparent mb-2">
            📊 Project Status Dashboard
          </h1>
          <p className="text-slate-400 text-lg">Track your development projects and progress</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsData.map((stat, index) => (
            <StatsCard key={index} {...stat} />
          ))}
        </div>

        {/* Navigation Tabs */}
        <NavigationTabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          stats={stats}
        />

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {projects[activeTab].map((project) => (
            <div
              key={project.id}
              className={`relative bg-gradient-to-br ${getStatusColor(project.status)} backdrop-blur-xl rounded-2xl shadow-xl p-6 border transition-all duration-500 group overflow-hidden`}
            >
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

              <div className="relative z-10">
                {/* Project Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(project.status)}
                    <div>
                      <h3 className="text-xl font-bold text-white mb-1">{project.title}</h3>
                      <p className="text-slate-300 text-sm">{project.description}</p>
                    </div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs font-bold border ${getPriorityColor(project.priority)}`}>
                    {project.priority.toUpperCase()}
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-slate-300">Progress</span>
                    <span className="text-sm font-semibold text-white">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-slate-700/50 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        project.status === 'completed' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                        project.status === 'in-progress' ? 'bg-gradient-to-r from-blue-500 to-cyan-500' :
                        'bg-gradient-to-r from-yellow-500 to-orange-500'
                      }`}
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Technologies */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-slate-800/50 text-slate-300 text-xs rounded-lg border border-slate-600/30"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Project Details */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <div className="text-slate-400 mb-1">Start Date</div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaCalendarAlt className="text-xs" />
                      {new Date(project.startDate).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400 mb-1">
                      {project.status === 'completed' ? 'Completed' : 'Deadline'}
                    </div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaCalendarAlt className="text-xs" />
                      {project.status === 'completed'
                        ? new Date(project.completedDate).toLocaleDateString()
                        : new Date(project.deadline).toLocaleDateString()
                      }
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400 mb-1">Time Spent</div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaClock className="text-xs" />
                      {project.timeSpent}
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400 mb-1">Estimated</div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaClock className="text-xs" />
                      {project.estimatedTime}
                    </div>
                  </div>
                </div>

                {/* Special Status Indicators */}
                {project.status === 'completed' && project.rating && (
                  <div className="mb-4 p-3 bg-green-900/30 border border-green-500/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FaAward className="text-green-400" />
                      <span className="text-green-300 text-sm font-semibold">
                        Project Rating: {project.rating}/5.0
                      </span>
                    </div>
                  </div>
                )}

                {project.status === 'paused' && project.pauseReason && (
                  <div className="mb-4 p-3 bg-yellow-900/30 border border-yellow-500/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FaExclamationTriangle className="text-yellow-400" />
                      <span className="text-yellow-300 text-sm font-semibold">
                        Paused: {project.pauseReason}
                      </span>
                    </div>
                  </div>
                )}

                {project.status === 'in-progress' && (
                  <div className="mb-4 p-3 bg-blue-900/30 border border-blue-500/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FaChartLine className="text-blue-400" />
                      <span className="text-blue-300 text-sm font-semibold">
                        {calculateDaysRemaining(project.deadline) > 0
                          ? `${calculateDaysRemaining(project.deadline)} days remaining`
                          : `${Math.abs(calculateDaysRemaining(project.deadline))} days overdue`
                        }
                      </span>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  {project.githubUrl && (
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white rounded-lg transition-all duration-300 border border-slate-600/30 hover:border-slate-500/50"
                    >
                      <FaGithub className="text-sm" />
                      <span className="text-sm font-semibold">GitHub</span>
                    </a>
                  )}

                  {project.liveUrl && (
                    <a
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600/50 to-purple-600/50 hover:from-blue-600/70 hover:to-purple-600/70 text-white rounded-lg transition-all duration-300 border border-blue-500/30 hover:border-purple-500/50"
                    >
                      <FaExternalLinkAlt className="text-sm" />
                      <span className="text-sm font-semibold">Live Demo</span>
                    </a>
                  )}

                  {project.status === 'in-progress' && (
                    <button className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-green-600/50 to-emerald-600/50 hover:from-green-600/70 hover:to-emerald-600/70 text-white rounded-lg transition-all duration-300 border border-green-500/30 hover:border-emerald-500/50">
                      <FaPlay className="text-sm" />
                      <span className="text-sm font-semibold">Continue</span>
                    </button>
                  )}
                </div>

                {/* Last Updated */}
                <div className="mt-4 pt-4 border-t border-slate-600/30">
                  <div className="text-xs text-slate-400">
                    Last updated: {new Date(project.lastUpdated).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {projects[activeTab].length === 0 && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaCode className="text-slate-300 text-3xl" />
            </div>
            <h3 className="text-2xl font-bold text-slate-300 mb-2">
              No {activeTab} projects
            </h3>
            <p className="text-slate-400 mb-6">
              {activeTab === 'active' && "Start a new project to see it here"}
              {activeTab === 'completed' && "Complete some projects to see them here"}
              {activeTab === 'paused' && "No projects are currently paused"}
            </p>
            <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold">
              + Add New Project
            </button>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 bg-gradient-to-br from-indigo-900/60 via-purple-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-indigo-500/30 hover:border-purple-400/50 transition-all duration-500">
          <h3 className="text-xl font-bold text-indigo-200 mb-4 flex items-center gap-2">
            🚀 Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="flex items-center gap-3 p-4 bg-slate-900/50 hover:bg-slate-800/50 rounded-xl transition-all duration-300 border border-slate-600/30 hover:border-indigo-500/50 group">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <FaCode className="text-white" />
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">New Project</div>
                <div className="text-slate-400 text-sm">Start a new project</div>
              </div>
            </button>

            <button className="flex items-center gap-3 p-4 bg-slate-900/50 hover:bg-slate-800/50 rounded-xl transition-all duration-300 border border-slate-600/30 hover:border-purple-500/50 group">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <FaChartLine className="text-white" />
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">Analytics</div>
                <div className="text-slate-400 text-sm">View detailed stats</div>
              </div>
            </button>

            <button className="flex items-center gap-3 p-4 bg-slate-900/50 hover:bg-slate-800/50 rounded-xl transition-all duration-300 border border-slate-600/30 hover:border-green-500/50 group">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <FaAward className="text-white" />
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">Achievements</div>
                <div className="text-slate-400 text-sm">View your milestones</div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectStatus;