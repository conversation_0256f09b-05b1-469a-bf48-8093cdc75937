import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import User from "../models/userModal.js";
import { oauth2Client } from "../utils/googleConfig.js";
import axios from "axios";
import jwt from "jsonwebtoken";
import dotenv from "dotenv";
import <PERSON>rrorHandler from "../utils/ErrorHandler.js";
import { sendMail } from "../utils/sendMail.js";
import { fileURLToPath } from "url";
import path from "path";
import ejs from "ejs";
import cloudinary from "../utils/cloudinaryConfig.js";
import bcrypt from "bcrypt";
import crypto from "crypto";

dotenv.config();

const generateAccessAndRefreshToken = async (userId) => {
  try {
    const user = await User.findById(userId);

    const accessToken = await user.generateAccessToken();

    const refreshToken = await user.generateRefreshToken();

    user.refreshToken = refreshToken;
    await user.save({ validateBeforeSave: false });

    return { accessToken, refreshToken };
  } catch (error) {
    console.error("Error in generateAccessAndRefreshToken:", error.message);
    throw new Error("Failed to generate tokens");
  }
};

export const googleLogin = CatchAsyncError(async (req, res, next) => {
  try {
    const { code } = req.query;
    let googleRes;

    try {
      googleRes = await oauth2Client.getToken(code);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: "Error fetching Google token",
        error: error.message,
      });
    }

    oauth2Client.setCredentials(googleRes.tokens);

    const userRes = await axios.get(
      `https://www.googleapis.com/oauth2/v2/userinfo?alt=json&access_token=${googleRes.tokens.access_token}`
    );

    const { name, email, picture } = userRes.data;
    let user = await User.findOne({ email });

    if (!user) {
      user = await User.create({
        name,
        email,
        image: picture,
        googleAuth: true,
      });
    }

    const { _id } = user;
    const token = jwt.sign({ _id, email }, process.env.JWT_SECRET, {
      expiresIn: "2h",
    });

    res.cookie("token", token, {
      httpOnly: true,
      secure: false,
      sameSite: "strict",
      maxAge: 12 * 60 * 60 * 1000,
    });

    return res.status(200).json({
      success: true,
      message: "success",
      token,
      user,
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
});

export const registrationUser = async (req, res, next) => {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  try {
    const { name, email, password } = req.body;
    const isEmailExist = await User.findOne({ email });

    if (isEmailExist) {
      return next(new ErrorHandler("Email Already Exists", 400));
    }

    let image = req.file ? req.file.path.secure_url : "";
    if (req.file) {
      try {
        const uploadRes = await cloudinary.uploader.upload(req.file.path);
        image = uploadRes.secure_url;
      } catch (error) {
        return next(new ErrorHandler("Image upload failed", 500));
      }
    }
    const user = {
      name,
      email,
      password,
      image,
    };

    const activationToken = createActivationToken(user);
    const activationCode = activationToken.activationCode;
    const data = { user: { name: user.name }, activationCode };
    const html = await ejs.renderFile(
      path.join(__dirname, "../mails/activation-mail.ejs"),
      data
    );

    try {
      await sendMail({
        email: user.email,
        subject: "Activate Your Account",
        template: "activation-mail.ejs",
        data,
      });
      res.status(201).json({
        success: true,
        message: `Please check your email: ${user.email} to activate your account`,
        activationToken: activationToken.token,
      });
    } catch (error) {
      return next(new ErrorHandler(error.message, 400));
    }
  } catch (error) {
    console.log(error.message);
    return next(new ErrorHandler(error.message, 400));
  }
};

export const createActivationToken = (user) => {
  const activationCode = Math.floor(100000 + Math.random() * 900000).toString();
  console.log(
    activationCode,
    "find the acticate token",
    " process.env.ACTIVATION_SECRET",
    process.env.ACTIVATION_SECRET
  );

  const token = jwt.sign(
    { user, activationCode },
    process.env.ACTIVATION_SECRET,
    {
      expiresIn: "5m",
    }
  );
  return { token, activationCode };
};

export const activateUser = CatchAsyncError(async (req, res, next) => {
  try {
    const { activation_token, activation_code } = req.body;

    const newUser = jwt.verify(activation_token, process.env.ACTIVATION_SECRET);

    if (newUser.activationCode !== activation_code) {
      return next(new ErrorHandler("Invalid Activation Code", 400));
    }

    const { name, email, password, image } = newUser.user;
    console.log(image, "ajnjajn");

    const existUser = await User.findOne({ email });

    if (existUser) return next(new ErrorHandler("Email Already Exists", 400));

    const user = await User.create({
      name,
      email,
      password,
      image,
    });
    user.password = undefined;
    res.status(201).json({
      success: true,
      message: "User Registered Successfully",
      user,
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
});

export const loginUser = CatchAsyncError(async (req, res, next) => {
  try {
    const { email, password } = req.body;
    console.log(email);

    if (!email || !password) {
      return next(new ErrorHandler("Please Enter Email or Password", 400));
    }

    const user = await User.findOne({ email });

    if (!user) {
      return next(new ErrorHandler("Invalid Email or Password", 400));
    }

    const isPasswordMatch = await user.isPasswordCorrect(password);

    if (!isPasswordMatch) {
      return next(new ErrorHandler("Invalid Email or Password", 400));
    }

    if (user.isTwoFactorEnabled) {
      const otpToken = crypto.randomBytes(3).toString("hex").toUpperCase();

      const otpPayload = {
        userId: user._id,
        otp: otpToken,
      };

      const otpJwt = jwt.sign(otpPayload, process.env.OTP_SECRET, {
        expiresIn: "10m",
      });

      await sendMail({
        email: user.email,
        subject: "Verify Your Login with OTP",
        template: "otp-2fa.ejs",
        data: { user: { name: user.name }, otpToken },
      });

      res.cookie("otpToken", otpJwt, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 10 * 60 * 1000,
      });

      return res.status(200).json({
        success: true,
        message: "OTP sent to your email. Please verify to complete login.",
        requiresTwoFA: true,
      });
    }

    const { accessToken, refreshToken } = await generateAccessAndRefreshToken(
      user._id
    );

    res.cookie("accessToken", accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 15 * 60 * 1000,
    });

    res.cookie("refreshToken", refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 2 * 24 * 60 * 60 * 1000,
    });

    const loggedInUser = await User.findById(user._id).select(
      "-password -refreshToken"
    );
    return res.status(200).json({
      success: true,
      user: loggedInUser,
      accessToken: accessToken,
      message: "User LoggedIn Successfully",
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
});

export const verifyTwoFAOtp = CatchAsyncError(async (req, res, next) => {
  try {
    const { otp_code } = req.body;
    const otpToken = req.cookies.otpToken;

    if (!otpToken) {
      return next(new ErrorHandler("OTP token not found", 400));
    }

    const decoded = jwt.verify(otpToken, process.env.OTP_SECRET);

    if (decoded.otp !== otp_code) {
      return next(new ErrorHandler("Invalid OTP Code", 400));
    }

    const user = await User.findById(decoded.userId);
    if (!user) {
      return next(new ErrorHandler("User not found", 404));
    }

    const { accessToken, refreshToken } = await generateAccessAndRefreshToken(
      user._id
    );

    res.cookie("accessToken", accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 15 * 60 * 1000,
    });

    res.cookie("refreshToken", refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 2 * 24 * 60 * 60 * 1000,
    });

    res.clearCookie("otpToken");

    return res.status(200).json({
      success: true,
      message: "OTP verified successfully. Logged in!",
      accessToken,
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
});

export const refreshAccessToken = CatchAsyncError(async (req, res, next) => {
  const incomingRefreshToken = req.cookies.refreshToken;

  if (!incomingRefreshToken) {
    return next(new ErrorHandler("UnAuthorized Request!", 401));
  }

  try {
    const decodedToken = jwt.verify(
      incomingRefreshToken,
      process.env.REFRESH_TOKEN_SECRET
    );
    const user = await User.findById(decodedToken._id);

    if (!user) {
      return next(new ErrorHandler("Invalid Token!", 404));
    }

    if (incomingRefreshToken !== user.refreshToken) {
      return next(new ErrorHandler("UnAuthorized Token is Expired!", 401));
    }

    const { accessToken, refreshToken: newRefreshToken } =
      await generateAccessAndRefreshToken(user._id);

    user.refreshToken = newRefreshToken;
    await user.save();

    res.cookie("accessToken", accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 15 * 60 * 1000,
    });

    res.cookie("refreshToken", newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 2 * 24 * 60 * 60 * 1000,
    });

    return res.status(200).json({
      success: true,
      message: "Token Refreshed Successfully",
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
});

export const logoutUser = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);

    if (user) {
      user.refreshToken = undefined;
      await user.save();

      res.cookie("accessToken", "", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        expires: new Date(0),
      });

      res.cookie("refreshToken", "", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        expires: new Date(0),
      });

      res.status(200).json({
        success: true,
        message: "Logged Out Successfully",
      });
    } else {
      res.status(404).json({ success: false, message: "User not found" });
    }
  } catch (error) {
    console.log("Error:", error.message);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

export const getUserDetails = async (req, res, next) => {
  try {
    const token = req.cookies?.accessToken;

    if (!token) {
      return next(new ErrorHandler("UnAuthorized Token is Required!", 401));
    }

    const decodedToken = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET);

    if (!decodedToken) {
      return next(new ErrorHandler("Invalid or Expired Token!", 401));
    }

    const userId = decodedToken?._id;

    const user = await User.findById(userId).select("-password -refreshToken");

    if (!user) {
      return next(new ErrorHandler("User Not Found!", 404));
    }

    return res.status(200).json({
      success: true,
      user,
      accessToken: token,
      message: "User details fetched successfully!",
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
};

export const getAllUsers = CatchAsyncError(async (req, res, next) => {
  const users = await User.find().select("name email image role");

  if (!users || users.length === 0) {
    return next(new ErrorHandler("No users found in the database!", 404));
  }

  return res.status(200).json({
    success: true,
    users,
    message: "All users fetched successfully!",
  });
});

export const deleteUser = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!id) {
    return next(new ErrorHandler("User ID is required!", 400));
  }

  const user = await User.findById(id);

  if (!user) {
    return next(new ErrorHandler("User not found!", 404));
  }

  await user.deleteOne();

  return res.status(200).json({
    success: true,
    message: "User deleted successfully!",
  });
});

export const updateUserRole = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  const { newRole } = req.body;

  if (!id) {
    return next(new ErrorHandler("User ID is required!", 400));
  }

  if (!newRole || typeof newRole !== "string") {
    return next(
      new ErrorHandler("Role is required and must be a string!", 400)
    );
  }

  const user = await User.findById(id);

  if (!user) {
    return next(new ErrorHandler("User not found!", 404));
  }

  user.role = newRole.trim().toLowerCase();
  await user.save();

  return res.status(200).json({
    success: true,
    message: `User role updated to '${newRole}' successfully!`,
  });
});

export const generateResetPasswordToken = async (req, res, next) => {
  const { email } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return next(new ErrorHandler("User Not Found!", 404));
    }

    const resetToken = crypto.randomBytes(32).toString("hex");
    user.resetPasswordToken = resetToken;
    user.resetPasswordExpires = Date.now() + 15 * 60 * 1000;

    await user.save();

    res.cookie("resetToken", resetToken, {
      httpOnly: true,
      secure: false,
      sameSite: "strict",
      maxAge: 15 * 60 * 1000,
    });

    const resetLink = `${process.env.FRONTEND_URL}/reset-password-page?token=${resetToken}`;

    await sendMail({
      email: user.email,
      subject: "Reset Your Password",
      template: "reset-password.ejs",
      data: { user: { name: user.name }, resetLink },
    });

    res.status(200).json({
      success: true,
      message: `Check your email: ${user.email} for the reset link`,
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
};

export const resetPassword = async (req, res, next) => {
  const { newPassword } = req.body;
  const resetToken = req.cookies.resetToken;

  if (!resetToken) {
    return next(new ErrorHandler("Reset Token is missing", 400));
  }

  try {
    const user = await User.findOne({
      resetPasswordToken: resetToken,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return next(new ErrorHandler("Invalid Request!", 404));
    }

    if (
      !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{6,}$/.test(
        newPassword
      )
    ) {
      return next(
        new ErrorHandler(
          "Password must be at least 6 characters long and include uppercase, lowercase, numbers, and special characters",
          400
        )
      );
    }

    user.password = newPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;

    await user.save();

    res.clearCookie("resetToken");

    res.status(200).json({
      success: true,
      message: "Password has been reset successfully",
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
};

export const generateTwoFactorToken = async (req, res, next) => {
  const { email } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return next(new ErrorHandler("User Not Found!", 404));
    }

    const verificationToken = crypto.randomBytes(32).toString("hex");
    user.twoFactorVerificationToken = verificationToken;
    user.twoFactorVerificationExpires = Date.now() + 15 * 60 * 1000;

    await user.save();

    res.cookie("twoFactorToken", verificationToken, {
      httpOnly: true,
      secure: false,
      sameSite: "strict",
      maxAge: 15 * 60 * 1000,
    });

    const verificationLink = `${process.env.FRONTEND_URL}/verify-2fa?token=${verificationToken}`;

    await sendMail({
      email: user.email,
      subject: "Enable Two-Factor Authentication",
      template: "enable-2fa.ejs",
      data: { user: { name: user.name }, verificationLink },
    });

    res.status(200).json({
      success: true,
      message: `Check your email: ${user.email} for the verification link`,
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 400));
  }
};

export const verifyTwoFactorToken = async (req, res, next) => {
  const { twoFactorToken } = req.cookies;

  try {
    if (!twoFactorToken) {
      return next(new ErrorHandler("Token not found in cookies!", 400));
    }

    const user = await User.findOne({
      twoFactorVerificationToken: twoFactorToken,
      twoFactorVerificationExpires: { $gt: Date.now() },
    });

    if (!user) {
      return next(new ErrorHandler("Invalid or expired token!", 400));
    }

    user.isTwoFactorEnabled = true;
    user.twoFactorVerificationToken = undefined;
    user.twoFactorVerificationExpires = undefined;

    await user.save();

    res.clearCookie("twoFactorToken");

    res.status(200).json({
      success: true,
      message: "Two-factor authentication enabled!",
    });
  } catch (error) {
    next(new ErrorHandler(error.message, 500));
  }
};

export const updateUserDetails = async (req, res, next) => {
  try {
    const token = req.cookies?.accessToken;

    if (!token) {
      return next(
        new ErrorHandler("Unauthorized! Access token is required.", 401)
      );
    }

    let decodedToken;
    try {
      decodedToken = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET);
    } catch (err) {
      return next(new ErrorHandler("Invalid or Expired Token!", 403));
    }

    const userId = decodedToken?._id;

    const user = await User.findById(userId).select(
      "-password -role -refreshToken"
    );

    if (!user) {
      return next(new ErrorHandler("User not found!", 404));
    }

    const updateData = {};
    const allowedFields = [
      "name",
      "email",
      "phone",
      "gender",
      "country",
      "city",
    ];
    Object.keys(req.body).forEach((key) => {
      if (allowedFields.includes(key)) {
        updateData[key] = req.body[key];
      }
    });

    if (req.file) {
      try {
        const uploadRes = await cloudinary.uploader.upload(req.file.path);
        updateData.image = uploadRes.secure_url;
      } catch (error) {
        return next(new ErrorHandler("Image upload failed!", 500));
      }
    }

    let updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select("-password -role -refreshToken");

    if (!updatedUser) {
      return next(new ErrorHandler("User not found!", 404));
    }

    return res.status(200).json({
      success: true,
      updatedUser,
      message: "User details updated successfully!",
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 500));
  }
};

export const updateSocialMedia = async (req, res, next) => {
  try {
    const token = req.cookies?.accessToken;
    if (!token) {
      return next(
        new ErrorHandler("Unauthorized! Access token is required.", 401)
      );
    }

    let decodedToken;
    try {
      decodedToken = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET);
    } catch (err) {
      return next(new ErrorHandler("Invalid or Expired Token!", 403));
    }

    const userId = decodedToken?._id;

    const { linkedin, github, twitter } = req.body;

    if (!linkedin && !github && !twitter) {
      return res.status(400).json({
        success: false,
        message: "At least one social media link is required.",
      });
    }
    const urlRegex =
      /^(https?:\/\/)?(www\.)?([a-zA-Z0-9]+)([-._]?[a-zA-Z0-9]*)*\.[a-zA-Z]{2,}(:\d+)?(\/.*)?$/;

    const errors = {};
    if (linkedin && !urlRegex.test(linkedin))
      errors.linkedin = "Invalid LinkedIn URL";
    if (github && !urlRegex.test(github)) errors.github = "Invalid GitHub URL";
    if (twitter && !urlRegex.test(twitter))
      errors.twitter = "Invalid Twitter URL";

    if (Object.keys(errors).length) {
      return res.status(400).json({
        success: false,
        errors,
      });
    }

    const user = await User.findById(userId).select(
      "-password -role -refreshToken"
    );
    if (!user) {
      return next(new ErrorHandler("User not found!", 404));
    }

    const updatedData = {
      linkedin: linkedin || user.socialMedia?.linkedin,
      github: github || user.socialMedia?.github,
      twitter: twitter || user.socialMedia?.twitter,
    };

    user.socialMedia = updatedData;
    await user.save();

    const sanitizedUser = user.toObject();
    delete sanitizedUser.password;
    delete sanitizedUser.role;
    delete sanitizedUser.refreshToken;

    return res.status(200).json({
      success: true,
      user: sanitizedUser,
      message: "Social media links updated successfully!",
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 500));
  }
};

export const updatePassword = async (req, res, next) => {
  try {
    const token = req.cookies?.accessToken;
    if (!token) {
      return next(
        new ErrorHandler("Unauthorized! Access token is required.", 401)
      );
    }

    let decodedToken;
    try {
      decodedToken = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET);
    } catch (err) {
      return next(new ErrorHandler("Invalid or Expired Token!", 403));
    }

    const userId = decodedToken?._id;
    const { currentPassword, newPassword, confirmNewPassword } = req.body;

    if (!currentPassword || !newPassword || !confirmNewPassword) {
      return res.status(400).json({
        success: false,
        message:
          "All fields are required: currentPassword, newPassword, confirmNewPassword",
      });
    }

    if (newPassword !== confirmNewPassword) {
      return res.status(400).json({
        success: false,
        message: "New passwords do not match.",
      });
    }

    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{6,}$/;
    if (!passwordRegex.test(newPassword)) {
      return res.status(400).json({
        success: false,
        message:
          "Password must be at least 6 characters long, contain one uppercase letter, one lowercase letter, one digit, and one special character.",
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return next(new ErrorHandler("User not found!", 404));
    }

    const isMatch = await user.isPasswordCorrect(currentPassword);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: "Current password is incorrect.",
      });
    }

    user.password = newPassword;
    await user.save();

    return res.status(200).json({
      success: true,
      message: "Password updated successfully!",
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 500));
  }
};
