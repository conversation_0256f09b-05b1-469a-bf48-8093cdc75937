import { motion } from "framer-motion";
import { AnimatedBackground } from "../ui";
import ReusableNavbar from "./ReusableNavbar";
import { MainContentWrapper } from "./index";

const HomepageLayout = ({ toggleSidebar }) => {
  // Dummy function for navbar since we don't want sidebar on homepage
  const dummyToggleSidebar = () => {};

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden"
    >
      {/* Background animated elements */}
      <AnimatedBackground />
      
      {/* Fixed Navbar */}
      <ReusableNavbar 
        toggleSidebar={dummyToggleSidebar} 
        title="Upcoding" 
        showSidebarToggle={false}
        isSidebarOpen={false}
      />
      
      {/* Main Content without sidebar margin */}
      <MainContentWrapper isSidebarOpen={false} toggleSidebar={toggleSidebar} />
    </motion.div>
  );
};

export default HomepageLayout;
