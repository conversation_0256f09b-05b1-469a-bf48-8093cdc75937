import { useState } from "react";
import { FaLinkedin, FaGithubSquare } from "react-icons/fa";
import { FaSquareTwitter } from "react-icons/fa6";
import toast from "react-hot-toast";
import axiosInstance from "../../utils/axiosInstance";

const SocialAccount = () => {
  const [socialLinks, setSocialLinks] = useState({
    linkedin: "",
    github: "",
    twitter: "",
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSocialLinks((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateFields = () => {
    let newErrors = {};

    const urlRegex =
      /^(https?:\/\/)?(www\.)?([a-zA-Z0-9]+)([-._]?[a-zA-Z0-9])*\.[a-zA-Z]{2,}(:\d+)?(\/.*)?$/;

    if (!socialLinks.linkedin) {
      newErrors.linkedin = "LinkedIn URL is required";
    } else if (!urlRegex.test(socialLinks.linkedin)) {
      newErrors.linkedin = "Invalid LinkedIn URL";
    }

    if (!socialLinks.github) {
      newErrors.github = "GitHub URL is required";
    } else if (!urlRegex.test(socialLinks.github)) {
      newErrors.github = "Invalid GitHub URL";
    }

    if (!socialLinks.twitter) {
      newErrors.twitter = "Twitter URL is required";
    } else if (!urlRegex.test(socialLinks.twitter)) {
      newErrors.twitter = "Invalid Twitter URL";
    }

    setErrors(newErrors);
    setTimeout(() => setErrors({}), 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;
    try {
      const { data } = await axiosInstance.patch(
        "http://localhost:8000/api/v1/auth/user/social-accounts",
        socialLinks,
        { withCredentials: true }
      );

      if (data.success) {
        toast.success(data.message || "Social media links updated!");
        setSocialLinks({
          linkedin: "",
          github: "",
          twitter: "",
        });
      } else {
        toast.error(data.message || "Failed to update links.");
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Something went wrong");
    }
  };

  return (
    <div>
      <div className="social-account">
        <form onSubmit={handleSubmit} className="social-card">
          <h1>Social Accounts</h1>
          <div className="social-icon">
            <FaLinkedin size={30} />
            <input
              type="text"
              name="linkedin"
              placeholder="LinkedIn"
              value={socialLinks.linkedin}
              onChange={handleChange}
            />
          </div>
          {errors.linkedin && (
            <p className="error-message">{errors.linkedin}</p>
          )}
          <div className="social-icon">
            <FaGithubSquare size={30} />
            <input
              type="text"
              name="github"
              placeholder="Github"
              value={socialLinks.github}
              onChange={handleChange}
            />
          </div>
          {errors.github && <p className="error-message">{errors.github}</p>}
          <div className="social-icon">
            <FaSquareTwitter size={30} />
            <input
              type="text"
              name="twitter"
              placeholder="Twitter"
              value={socialLinks.twitter}
              onChange={handleChange}
            />
          </div>
          {errors.twitter && <p className="error-message">{errors.twitter}</p>}

          <button style={{ border: "none", outline: "none" }} type="submit">
            Save
          </button>
        </form>
      </div>
    </div>
  );
};

export default SocialAccount;
