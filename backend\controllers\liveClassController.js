import LiveClass from "../models/liveClassModal.js";
import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import ErrorHandler from "../utils/ErrorHandler.js";

// Create a new live class
export const createLiveClass = CatchAsyncError(async (req, res, next) => {
  const liveClass = await LiveClass.create(req.body);

  res.status(201).json({
    success: true,
    message: "Live class created successfully",
    liveClass,
  });
});

// Get all upcoming live classes
export const getAllUpcomingClasses = CatchAsyncError(async (req, res, next) => {
  const currentDate = new Date();
  
  // Find all classes where dateTime is greater than current date
  const upcomingClasses = await LiveClass.find({
    dateTime: { $gt: currentDate },
    isActive: true,
  }).sort({ dateTime: 1 }); // Sort by date in ascending order

  res.status(200).json({
    success: true,
    count: upcomingClasses.length,
    upcomingClasses,
  });
});

// Get upcoming live classes by category
export const getUpcomingClassesByCategory = CatchAsyncError(async (req, res, next) => {
  const { category } = req.params;
  const currentDate = new Date();
  
  const upcomingClasses = await LiveClass.find({
    dateTime: { $gt: currentDate },
    category,
    isActive: true,
  }).sort({ dateTime: 1 });

  res.status(200).json({
    success: true,
    count: upcomingClasses.length,
    upcomingClasses,
  });
});

// Get a live class by ID
export const getLiveClassById = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  const liveClass = await LiveClass.findById(id);

  if (!liveClass) {
    return next(new ErrorHandler("Live class not found", 404));
  }

  res.status(200).json({
    success: true,
    liveClass,
  });
});

// Update a live class
export const updateLiveClass = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  let liveClass = await LiveClass.findById(id);

  if (!liveClass) {
    return next(new ErrorHandler("Live class not found", 404));
  }

  liveClass = await LiveClass.findByIdAndUpdate(id, req.body, {
    new: true,
    runValidators: true,
  });

  res.status(200).json({
    success: true,
    message: "Live class updated successfully",
    liveClass,
  });
});

// Delete a live class
export const deleteLiveClass = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  const liveClass = await LiveClass.findById(id);

  if (!liveClass) {
    return next(new ErrorHandler("Live class not found", 404));
  }

  await LiveClass.findByIdAndDelete(id);

  res.status(200).json({
    success: true,
    message: "Live class deleted successfully",
  });
});

// Register for a live class
export const registerForLiveClass = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  
  const liveClass = await LiveClass.findById(id);

  if (!liveClass) {
    return next(new ErrorHandler("Live class not found", 404));
  }

  // Check if class is full
  if (liveClass.registeredParticipants >= liveClass.maxParticipants) {
    return next(new ErrorHandler("This class is already full", 400));
  }

  // Increment registered participants
  liveClass.registeredParticipants += 1;
  await liveClass.save();

  res.status(200).json({
    success: true,
    message: "Successfully registered for the live class",
    liveClass,
  });
});