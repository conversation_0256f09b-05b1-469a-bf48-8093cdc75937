import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Courses } from '../features';
import { HotCourses, RecommendedCourses } from '../ui';
import { GradientOrbs } from './common/BackgroundEffects';

const CourseSection = ({ itemVariants }) => {
  return (
    <motion.div variants={itemVariants} className="py-16 px-6 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <GradientOrbs />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="courses-container text-white">
          <Courses />

          {/* Hot & Recommended Courses Grid */}
          <motion.section
            variants={itemVariants}
            className="container mx-auto px-4 py-12 max-w-3xl"
          >
            <div className="flex flex-col gap-8">
              <HotCourses 
                courses={[
                  {
                    title: "Machine Learning Fundamentals",
                    students: "2.5k+",
                    rating: 4.8,
                    image: "https://source.unsplash.com/300x200?machine-learning",
                  },
                  {
                    title: "Full Stack Development",
                    students: "1.8k+",
                    rating: 4.7,
                    image: "https://source.unsplash.com/300x200?programming",
                  },
                  {
                    title: "Data Science Masterclass",
                    students: "3k+",
                    rating: 4.9,
                    image: "https://source.unsplash.com/300x200?data-science",
                  },
                ]}
              />

              <RecommendedCourses 
                courses={[
                  {
                    title: "Advanced Python Programming",
                    level: "Intermediate",
                    duration: "8 weeks",
                    image: "https://source.unsplash.com/300x200?python",
                  },
                  {
                    title: "React & Next.js Development",
                    level: "Advanced",
                    duration: "10 weeks",
                    image: "https://source.unsplash.com/300x200?javascript",
                  },
                  {
                    title: "Cloud Computing Essentials",
                    level: "Beginner",
                    duration: "6 weeks",
                    image: "https://source.unsplash.com/300x200?cloud-computing",
                  },
                ]}
              />
            </div>
          </motion.section>
        </div>
        
        <div className="mt-12 text-center">
          <Link 
            to="/courses" 
            className="inline-block px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg transition-colors duration-300 shadow-[0_0_15px_rgba(59,130,246,0.3)]"
          >
            View All Courses
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default CourseSection;
