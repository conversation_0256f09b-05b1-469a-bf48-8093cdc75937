import React from 'react';
import CaseStudyCardNew from './CaseStudyCardNew';
import { motion, AnimatePresence } from 'framer-motion';

const CaseStudySectionNew = ({ title, studies, activeIndex, showSolutions, toggleContent, toggleSolution, isExpanded, toggleSection }) => {
  return (
    <motion.div 
      id={title.toLowerCase().replace(/\s+/g, '-')} 
      className="main-content py-3"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      style={{ backgroundColor: "transparent" }}
    >
      <motion.div 
        className="p-4 rounded-xl border border-white/10 cursor-pointer bg-gradient-to-r from-[#1e293b]/70 to-[#1e293b]/50 hover:from-[#1e293b]/80 hover:to-[#1e293b]/60 transition-colors shadow-md hover:shadow-lg"
        onClick={() => toggleSection(title)}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>{title}</h2>
            <p className="text-blue-200/70 text-sm">{studies.length} case studies</p>
          </div>
          <motion.span 
            className="text-blue-400 text-2xl"
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            ↓
          </motion.span>
        </div>
      </motion.div>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <ul className="chapter-list mt-3 pl-2">
              {studies.map((study, index) => (
                <CaseStudyCardNew
                  key={index}
                  study={study}
                  index={index}
                  isActive={activeIndex === index}
                  showSolution={showSolutions[index]}
                  toggleContent={toggleContent}
                  toggleSolution={toggleSolution}
                />
              ))}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default CaseStudySectionNew;