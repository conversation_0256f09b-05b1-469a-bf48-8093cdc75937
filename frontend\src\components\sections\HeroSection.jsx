import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FloatingParticles, GradientOrbs } from './common/BackgroundEffects';
import { HeroTerminal, HeroTrustIndicators } from './common/HeroComponents';
import HeroStats from './common/components/HeroStats';

const HeroSection = ({ itemVariants }) => {
  return (
    <motion.section
      variants={itemVariants}
      className="relative overflow-hidden bg-[linear-gradient(114deg,#1a3c50,#010509)] text-white min-h-[90vh] flex items-center px-6"
    >
      <div className="absolute inset-0 overflow-hidden">
        <GradientOrbs />
        <FloatingParticles />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto pt-20 pb-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <HeroContent itemVariants={itemVariants} />
          
          {/* Right Column - Interactive Elements */}
          <HeroTerminal itemVariants={itemVariants} />
        </div>

        {/* Stats Section */}
        <HeroStats itemVariants={itemVariants} />
      </div>
    </motion.section>
  );
};

const HeroContent = ({ itemVariants }) => (
  <div className="text-center lg:text-left">
    <motion.h1
      initial={{ y: 30, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: 0.3 }}
      className="text-4xl md:text-5xl lg:text-[3.5rem] font-bold mb-6 leading-[1.15] tracking-tight whitespace-nowrap"
    >
      Code,{" "}
      <span className="bg-gradient-to-r from-blue-400 to-blue-500 bg-clip-text text-transparent">
        Crash,
      </span>{" "}
      <span className="bg-gradient-to-r from-indigo-400 to-indigo-500 bg-clip-text text-transparent">
        Debug,
      </span>{" "}
      <span className="bg-gradient-to-r from-purple-400 to-purple-500 bg-clip-text text-transparent">
        Grow
      </span>
    </motion.h1>

    <motion.p
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="text-lg md:text-xl mb-10 text-gray-300 max-w-xl mx-auto lg:mx-0 leading-relaxed"
    >
      That's how real engineers learn DevOps<br className="hidden md:block" /> and that's exactly how we teach.
    </motion.p>

    <HeroCTA />
    <HeroTrustIndicators />
  </div>
);

const HeroCTA = () => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.6, delay: 0.5 }}
    className="flex flex-wrap gap-8 justify-center lg:justify-start items-center mb-12"
  >
    <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
      <Link
        to="/"
        className="inline-flex items-center px-10 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-md text-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-[0_0_15px_rgba(59,130,246,0.5)]"
      >
        <span className="relative z-10">Try Now</span>
      </Link>
    </motion.div>

    <div className="flex items-center gap-1">
      <div className="text-3xl font-bold text-white mr-2">1M+</div>
      <span className="text-base text-gray-300">Enrolled</span>
    </div>
  </motion.div>
);

export default HeroSection;
