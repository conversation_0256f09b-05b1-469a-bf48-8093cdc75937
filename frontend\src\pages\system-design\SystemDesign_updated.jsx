import React from "react";
import SystemDesignIntroduction from "./components/SystemDesignIntroduction";
import LabEnvironment from "./components/LabEnvironment";
import ProjectsComponent from "./components/ProjectsComponent";
import InterviewChecklist from "./components/InterviewChecklist";
import SystemDesignHero from "./components/SystemDesignHero";
import SystemDesignLayout from "./components/SystemDesignLayout";
import SystemDesignPremiumModal from "./components/SystemDesignPremiumModal";
import SystemDesignLabEnvironment from "./components/SystemDesignLabEnvironment";
import SystemDesignLiveClasses from "./components/SystemDesignLiveClasses";
import SystemDesignFAQS from "./components/SystemDesignFAQS";
import { CourseResourcesSection } from "../../components/ui";
import useSidebarState from "../../hooks/useSidebarState";

const SystemDesign = () => {
  const courseConfig = {
    title: "System Design Course Resources",
    subtitle: "Master system design through comprehensive modules, real-world projects, live workshops, and expert guidance",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn system design fundamentals and principles",
        icon: "🏗️",
        component: SystemDesignIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice with system design tools and environments",
        icon: "🔧",
        component: SystemDesignLabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join expert-led system design workshops",
        icon: "🎓",
        component: SystemDesignLiveClasses,
        props: {}
      },
      {
        id: "Projects",
        title: "Projects",
        description: "Build real-world system design projects",
        icon: "📁",
        component: ProjectsComponent,
        props: {}
      },
      {
        id: "InterviewPrep",
        title: "Interview Prep",
        description: "Prepare for system design interviews",
        icon: "💼",
        component: InterviewChecklist,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to system design questions",
        icon: "❓",
        component: SystemDesignFAQS,
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={SystemDesignHero}
      LayoutComponent={SystemDesignLayout}
      PremiumModalComponent={SystemDesignPremiumModal}
      useSidebarHook={() => useSidebarState(true)}
    />
  );
};

export default SystemDesign;
