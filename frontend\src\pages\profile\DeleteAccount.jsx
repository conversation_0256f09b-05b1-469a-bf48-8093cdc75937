import { useState } from "react";
import {
  FaEnvelope,
  FaLock,
  FaExclamationTriangle,
  FaShieldAlt,
  FaEye,
  FaEyeSlash,
  FaTrashAlt,
  FaUserSlash,
  FaCheckCircle,
  FaTimes
} from "react-icons/fa";
import { RiDeleteBin5Fill } from "react-icons/ri";
import toast from "react-hot-toast";

const DeleteAccount = () => {
  const [showModal, setShowModal] = useState(false);
  const [verificationType, setVerificationType] = useState("password");
  const [inputValue, setInputValue] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmationStep, setConfirmationStep] = useState(1);
  const [errors, setErrors] = useState({});

  const validateInput = () => {
    let newErrors = {};

    if (!inputValue.trim()) {
      newErrors.input = verificationType === "password"
        ? "Password is required"
        : "Email is required";
    } else if (verificationType === "email" && !/\S+@\S+\.\S+/.test(inputValue)) {
      newErrors.input = "Please enter a valid email address";
    } else if (verificationType === "password" && inputValue.length < 6) {
      newErrors.input = "Password must be at least 6 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleDeleteClick = () => {
    if (!validateInput()) return;
    setShowModal(true);
  };

  const handleConfirmDelete = async () => {
    setLoading(true);

    try {
      // Simulate API call - replace with actual delete account API
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast.success("Account deletion initiated. You will be logged out shortly.");
      setShowModal(false);

      // Redirect to login or home page after successful deletion
      setTimeout(() => {
        window.location.href = "/";
      }, 2000);

    } catch (error) {
      toast.error("Failed to delete account. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationTypeChange = (type) => {
    setVerificationType(type);
    setInputValue("");
    setErrors({});
  };
  return (
    <div className="min-h-screen w-full pt-4 pb-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-red-200 via-orange-200 to-yellow-200 bg-clip-text text-transparent mb-2">
            ⚠️ Delete Account
          </h1>
          <p className="text-slate-400 text-lg">Permanently remove your account and all associated data</p>
        </div>

        {/* Warning Card */}
        <div className="mb-8 bg-gradient-to-br from-red-900/60 via-orange-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-red-500/30 hover:border-orange-400/50 transition-all duration-500 group">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
              <FaExclamationTriangle className="text-white text-xl" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-red-200 mb-3">⚠️ Critical Warning</h3>
              <div className="space-y-2 text-slate-300">
                <p className="flex items-center gap-2">
                  <FaTimes className="text-red-400 text-sm" />
                  <span>This action is <strong className="text-red-300">permanent and irreversible</strong></span>
                </p>
                <p className="flex items-center gap-2">
                  <FaTimes className="text-red-400 text-sm" />
                  <span>All your data, settings, and preferences will be <strong className="text-red-300">permanently deleted</strong></span>
                </p>
                <p className="flex items-center gap-2">
                  <FaTimes className="text-red-400 text-sm" />
                  <span>Your profile, projects, and progress will be <strong className="text-red-300">lost forever</strong></span>
                </p>
                <p className="flex items-center gap-2">
                  <FaTimes className="text-red-400 text-sm" />
                  <span>You will be immediately logged out and cannot recover your account</span>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Delete Form */}
        <div className="relative bg-gradient-to-br from-slate-800/90 via-red-900/80 to-orange-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-red-400/50 transition-all duration-500 group overflow-hidden">
          {/* Animated background elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-600/10 via-orange-600/10 to-yellow-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-full blur-3xl transform translate-x-16 -translate-y-16 z-0"></div>

          <div className="relative z-10">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
              <FaUserSlash className="text-red-400" />
              Account Verification Required
            </h2>

            {/* Verification Method Selection */}
            <div className="mb-6">
              <label className="text-sm font-semibold text-orange-200 mb-3 block">
                Choose verification method:
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => handleVerificationTypeChange("password")}
                  className={`flex items-center gap-3 p-4 rounded-xl border-2 transition-all duration-300 ${
                    verificationType === "password"
                      ? "bg-gradient-to-r from-blue-600/50 to-purple-600/50 border-blue-400/50 text-white"
                      : "bg-slate-900/50 border-slate-600/50 text-slate-300 hover:border-blue-400/30 hover:bg-slate-800/50"
                  }`}
                >
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    verificationType === "password"
                      ? "bg-blue-500"
                      : "bg-slate-700"
                  }`}>
                    <FaLock className="text-white" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold">Use Password</div>
                    <div className="text-sm opacity-75">Verify with your current password</div>
                  </div>
                </button>

                <button
                  onClick={() => handleVerificationTypeChange("email")}
                  className={`flex items-center gap-3 p-4 rounded-xl border-2 transition-all duration-300 ${
                    verificationType === "email"
                      ? "bg-gradient-to-r from-green-600/50 to-emerald-600/50 border-green-400/50 text-white"
                      : "bg-slate-900/50 border-slate-600/50 text-slate-300 hover:border-green-400/30 hover:bg-slate-800/50"
                  }`}
                >
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    verificationType === "email"
                      ? "bg-green-500"
                      : "bg-slate-700"
                  }`}>
                    <FaEnvelope className="text-white" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold">Use Email OTP</div>
                    <div className="text-sm opacity-75">Verify with email verification</div>
                  </div>
                </button>
              </div>
            </div>

            {/* Input Field */}
            <div className="mb-6">
              <label className="text-sm font-semibold text-orange-200 mb-2 block">
                {verificationType === "password" ? "🔒 Enter your password" : "📧 Enter your email"}
              </label>
              <div className="relative">
                <input
                  type={verificationType === "password" && !showPassword ? "password" : "text"}
                  placeholder={
                    verificationType === "password"
                      ? "Enter your current password..."
                      : "Enter your email address..."
                  }
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  className="w-full px-4 py-3 pr-12 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-red-400/50 focus:ring-2 focus:ring-red-400/20 focus:outline-none transition-all duration-300"
                />
                {verificationType === "password" && (
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-red-300 transition-colors duration-200"
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                )}
              </div>
              {errors.input && (
                <p className="text-red-400 text-sm flex items-center gap-1 mt-2">
                  ⚠️ {errors.input}
                </p>
              )}
            </div>

            {/* Security Checklist */}
            <div className="mb-6 p-4 bg-slate-900/30 rounded-xl border border-slate-600/30">
              <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                <FaShieldAlt className="text-blue-400" />
                Before you proceed, please confirm:
              </h4>
              <div className="space-y-2 text-sm text-slate-300">
                <div className="flex items-center gap-2">
                  <FaCheckCircle className="text-green-400 text-xs" />
                  <span>I have backed up any important data</span>
                </div>
                <div className="flex items-center gap-2">
                  <FaCheckCircle className="text-green-400 text-xs" />
                  <span>I understand this action cannot be undone</span>
                </div>
                <div className="flex items-center gap-2">
                  <FaCheckCircle className="text-green-400 text-xs" />
                  <span>I want to permanently delete my account</span>
                </div>
              </div>
            </div>

            {/* Delete Button */}
            <button
              onClick={handleDeleteClick}
              disabled={loading || !inputValue.trim()}
              className="w-full relative px-8 py-4 bg-gradient-to-r from-red-600 via-red-700 to-red-800 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 font-bold text-lg tracking-wide disabled:opacity-50 disabled:cursor-not-allowed group/btn overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-red-700 via-red-800 to-red-900 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
              <span className="relative z-10 flex items-center justify-center gap-2">
                <FaTrashAlt />
                Delete My Account Permanently
              </span>
            </button>

            <p className="text-center text-slate-400 text-sm mt-4">
              This action will immediately log you out and delete all your data
            </p>
          </div>
        </div>

        {/* Support Section */}
        <div className="mt-8 bg-gradient-to-br from-blue-900/60 via-slate-800/80 to-cyan-900/60 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-blue-500/30 hover:border-cyan-400/50 transition-all duration-500">
          <h3 className="text-xl font-bold text-blue-200 mb-4 flex items-center gap-2">
            💬 Need Help?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-300">
            <div className="flex items-start gap-2">
              <span className="text-blue-400">💡</span>
              <span>Consider deactivating instead of deleting</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-blue-400">📞</span>
              <span>Contact support for account issues</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-blue-400">📥</span>
              <span>Download your data before deletion</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-blue-400">🔄</span>
              <span>Take a break instead of permanent deletion</span>
            </div>
          </div>
        </div>
      </div>
      {showModal && (
        <div className="delete-modal">
          <div className="delete-modal-content">
            <RiDeleteBin5Fill size={50} style={{ color: "red" }} />
            <h3>Are you sure?</h3>
            <p>
              Once Deleted, You Won’t Be Able to Recover Your Account. This
              action is permanent and cannot be undone.
            </p>
            <div className="delete-modal-buttons">
              <button className="cancel" onClick={() => setShowModal(false)}>
                Cancel
              </button>
              <button className="confirm" onClick={handleConfirmDelete}>
                Yes, Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeleteAccount;
