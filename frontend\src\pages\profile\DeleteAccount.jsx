import { useState } from "react";
import { FaEnvelope, FaLock } from "react-icons/fa";
import { RiDeleteBin5Fill } from "react-icons/ri";

const DeleteAccount = () => {
  const [showModal, setShowModal] = useState(false);
  const [verificationType, setVerificationType] = useState("password");
  const [inputValue, setInputValue] = useState("");

  const handleDeleteClick = () => {
    setShowModal(true);
  };

  const handleConfirmDelete = () => {
    setShowModal(false);
  };
  return (
    <div>
      <div className="delete-container">
        <div className="delete-box">
          <h2>Delete Account</h2>
          <p>
            Deleting your account is a permanent action and cannot be undone.
            All your data, settings, and preferences will be lost forever.
            Please ensure you have backed up any important information before
            proceeding.
          </p>
          <div className="delete-button-group">
            <button
              className={verificationType === "password" ? "active" : ""}
              onClick={() => setVerificationType("password")}
            >
              <FaLock /> Use Password
            </button>
            <button
              className={verificationType === "email" ? "active" : ""}
              onClick={() => setVerificationType("email")}
            >
              <FaEnvelope /> Use Email OTP
            </button>
          </div>
          <div className="delete-input-field">
            <input
              type={verificationType === "password" ? "password" : "email"}
              placeholder={
                verificationType === "password"
                  ? "Enter Your Password"
                  : "Enter Your Email"
              }
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
          </div>
          <button className="btn-danger" onClick={handleDeleteClick}>
            Delete My Account
          </button>
        </div>
      </div>
      {showModal && (
        <div className="delete-modal">
          <div className="delete-modal-content">
            <RiDeleteBin5Fill size={50} style={{ color: "red" }} />
            <h3>Are you sure?</h3>
            <p>
              Once Deleted, You Won’t Be Able to Recover Your Account. This
              action is permanent and cannot be undone.
            </p>
            <div className="delete-modal-buttons">
              <button className="cancel" onClick={() => setShowModal(false)}>
                Cancel
              </button>
              <button className="confirm" onClick={handleConfirmDelete}>
                Yes, Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeleteAccount;
