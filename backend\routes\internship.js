import express from "express"
import { createInternshipRegistration, deleteInternshipRegistration, getAllInternshipRegistrations } from "../controllers/internship.js";
import { isAuthenticated } from "../middleware/auth.js";


const internshipRouter = express.Router();

internshipRouter.post("/internship-registration", isAuthenticated, createInternshipRegistration)

internshipRouter.get("/get-internship", isAuthenticated, getAllInternshipRegistrations)

internshipRouter.delete("/delete-internship/:id", isAuthenticated, deleteInternshipRegistration)


// userRouter.get("/me",isAuthenticated,authenticateMe)

export default internshipRouter