import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { HiMenuAlt2 } from 'react-icons/hi';

const PythonHeader = ({ toggleSidebar }) => {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <motion.nav 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled 
          ? 'bg-white shadow-lg backdrop-blur-sm' 
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-full px-6 py-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          {/* Sidebar Toggle Button */}
          <motion.button
            onClick={toggleSidebar}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className={`p-2 rounded-md ${
              scrolled ? 'text-blue-600 hover:bg-blue-100' : 'text-white hover:bg-white/20'
            }`}
            aria-label="Toggle Sidebar"
          >
            <HiMenuAlt2 className="w-6 h-6" />
          </motion.button>
          
          <Link to="/" className="flex items-center gap-3">
            <motion.img 
              src="/images/logonew.png" 
              alt="Logo" 
              className="w-10 h-10"
              whileHover={{ 
                scale: 1.1,
                rotate: [0, 5, -5, 0]
              }}
              transition={{ duration: 0.3 }}
            />
            <span className={`font-bold text-xl transition-colors duration-300 ${
              scrolled ? 'text-blue-600' : 'text-white'
            }`}>
              Python Course
            </span>
          </Link>
        </div>

        {/* Navigation Links */}
        <div className="hidden md:flex items-center space-x-8">
          <NavLink to="/courses" scrolled={scrolled}>Courses</NavLink>
          <NavLink to="/dashboard" scrolled={scrolled}>Dashboard</NavLink>
          <NavLink to="/about" scrolled={scrolled}>About</NavLink>
        </div>

        {/* Auth Button */}
        <div className="flex items-center gap-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`px-5 py-2 rounded-xl shadow transition-colors duration-300 ${
              scrolled
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-white hover:bg-gray-100 text-blue-600'
            }`}
          >
            Get Started
          </motion.button>
        </div>
      </div>
    </motion.nav>
  );
};

const NavLink = ({ children, to, scrolled }) => {
  return (
    <Link
      to={to}
      className={`font-medium transition-colors duration-300 hover:text-blue-500 ${
        scrolled ? 'text-gray-700' : 'text-white'
      }`}
    >
      {children}
    </Link>
  );
};

export default PythonHeader;
