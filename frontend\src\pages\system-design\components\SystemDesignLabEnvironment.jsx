import React, { useState } from "react";
import { motion } from "framer-motion";

const SystemDesignLabEnvironment = ({ showPremiumOverlay, onBackToCourse }) => {
  const [activeTab, setActiveTab] = useState("tools");

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const tools = [
    {
      name: "Draw.io (Diagrams.net)",
      description: "Free online diagram software for system architecture",
      icon: "🎨",
      features: ["System Architecture", "Database Design", "Network Diagrams", "Flowcharts"],
      link: "https://app.diagrams.net/"
    },
    {
      name: "Lucidchart",
      description: "Professional diagramming and visualization tool",
      icon: "📊",
      features: ["Collaborative Design", "Templates", "Real-time Editing", "Integration"],
      link: "https://www.lucidchart.com/"
    },
    {
      name: "AWS Architecture Center",
      description: "AWS tools and resources for system design",
      icon: "☁️",
      features: ["Reference Architectures", "Best Practices", "Solution Templates", "Cost Calculator"],
      link: "https://aws.amazon.com/architecture/"
    },
    {
      name: "Figma",
      description: "Design tool for creating system mockups and user flows",
      icon: "🎭",
      features: ["UI/UX Design", "Prototyping", "Collaboration", "Component Libraries"],
      link: "https://www.figma.com/"
    }
  ];

  const environments = [
    {
      name: "Docker Playground",
      description: "Practice containerization and microservices",
      icon: "🐳",
      features: ["Container Orchestration", "Kubernetes", "Docker Compose", "Service Mesh"],
      action: "Launch Environment"
    },
    {
      name: "Database Sandbox",
      description: "Experiment with different database systems",
      icon: "🗄️",
      features: ["SQL Queries", "NoSQL Design", "Data Modeling", "Performance Testing"],
      action: "Start Sandbox"
    },
    {
      name: "Load Testing Lab",
      description: "Test system performance and scalability",
      icon: "⚡",
      features: ["Performance Testing", "Load Simulation", "Metrics Analysis", "Bottleneck Detection"],
      action: "Begin Testing"
    },
    {
      name: "API Design Studio",
      description: "Design and test RESTful APIs",
      icon: "🔌",
      features: ["API Documentation", "Testing Tools", "Mock Servers", "Version Control"],
      action: "Design APIs"
    }
  ];

  return (
    <div className="py-8">
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🔧 System Design Lab Environment
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Your System Design{" "}
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Workshop
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Access professional tools and environments to practice system design, architecture planning, and solution implementation
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-12">
          <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-2 flex gap-2">
            <button
              onClick={() => setActiveTab("tools")}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === "tools"
                  ? "bg-blue-500 text-white shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/10"
              }`}
            >
              Design Tools
            </button>
            <button
              onClick={() => setActiveTab("environments")}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === "environments"
                  ? "bg-blue-500 text-white shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/10"
              }`}
            >
              Practice Environments
            </button>
          </div>
        </div>

        {/* Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {activeTab === "tools" && tools.map((tool, index) => (
            <motion.div
              key={tool.name}
              variants={itemVariants}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8 hover:bg-white/10 transition-all duration-300 group"
            >
              <div className="flex items-start gap-4 mb-6">
                <div className="text-4xl">{tool.icon}</div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-blue-300 transition-colors">
                    {tool.name}
                  </h3>
                  <p className="text-white/70 mb-4">{tool.description}</p>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="text-white font-semibold mb-3">Key Features:</h4>
                <ul className="grid grid-cols-2 gap-2">
                  {tool.features.map((feature, idx) => (
                    <li key={idx} className="text-white/80 text-sm flex items-center">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <button
                onClick={() => window.open(tool.link, '_blank')}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 rounded-lg font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105"
              >
                Open Tool →
              </button>
            </motion.div>
          ))}

          {activeTab === "environments" && environments.map((env, index) => (
            <motion.div
              key={env.name}
              variants={itemVariants}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8 hover:bg-white/10 transition-all duration-300 group"
            >
              <div className="flex items-start gap-4 mb-6">
                <div className="text-4xl">{env.icon}</div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-green-300 transition-colors">
                    {env.name}
                  </h3>
                  <p className="text-white/70 mb-4">{env.description}</p>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="text-white font-semibold mb-3">What You'll Practice:</h4>
                <ul className="grid grid-cols-2 gap-2">
                  {env.features.map((feature, idx) => (
                    <li key={idx} className="text-white/80 text-sm flex items-center">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <button
                onClick={showPremiumOverlay}
                className="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-3 rounded-lg font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105"
              >
                {env.action} →
              </button>
            </motion.div>
          ))}
        </motion.div>

        {/* Premium CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg border border-white/10 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              🚀 Unlock Full Lab Access
            </h3>
            <p className="text-white/80 mb-6 max-w-2xl mx-auto">
              Get unlimited access to all design tools, practice environments, and expert guidance for your system design journey.
            </p>
            <button
              onClick={showPremiumOverlay}
              className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105"
            >
              Upgrade to Premium
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default SystemDesignLabEnvironment;
