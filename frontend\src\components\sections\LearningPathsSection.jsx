import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { GradientOrbs } from './common/BackgroundEffects';

const learningPaths = [
	{
		title: 'DevOps Engineer',
		courses: '17 Courses',
		hours: '350h',
		image: '/images/dsa-logo.png',
		path: '/devops-path',
		gradient: 'from-blue-500 to-blue-700',
	},
	{
		title: 'Kubernetes Administrator',
		courses: '12 Courses',
		hours: '250h',
		image: '/images/interviewlogo.png',
		path: '/kubernetes-path',
		gradient: 'from-indigo-500 to-indigo-700',
	},
	{
		title: 'System Administrator',
		courses: '13 Courses',
		hours: '340h',
		image: '/images/systemdesignlogo-.png',
		path: '/system-path',
		gradient: 'from-purple-500 to-purple-700',
	},
];

const LearningPathsSection = ({ itemVariants }) => {
	return (
		<motion.div
			variants={itemVariants}
			className="py-16 px-6 relative overflow-hidden"
		>
			<div className="absolute inset-0 overflow-hidden">
				<GradientOrbs />
			</div>

			<div className="max-w-7xl mx-auto relative z-10">
				<h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-white">
					Choose Your Path, Master Your Skills
				</h2>
				<p className="text-gray-300 text-center mb-12 max-w-3xl mx-auto">
					Select the career path that matches your goals and access a curated
					learning journey.
				</p>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					{learningPaths.map((path, index) => (
						<PathCard key={index} path={path} />
					))}
				</div>

				<div className="mt-12 text-center">
					<Link
						to="/learning-paths"
						className="inline-block px-8 py-3 border border-blue-400/50 text-blue-400 hover:border-blue-300 hover:text-blue-300 font-medium rounded-lg transition-colors duration-300 backdrop-blur-sm"
					>
						View All Learning Paths
					</Link>
				</div>
			</div>
		</motion.div>
	);
};

const PathCard = ({ path }) => (
	<motion.div
		whileHover={{ y: -5 }}
		className="bg-[#1e293b]/80 backdrop-blur-md rounded-lg overflow-hidden shadow-lg hover:shadow-xl border border-white/10 transition-all duration-300"
	>
		<div
			className={`h-48 bg-gradient-to-br ${path.gradient}/20 flex items-center justify-center border-b border-white/10`}
		>
			<img
				src={path.image}
				alt={path.title}
				className="h-32 w-auto object-contain"
			/>
		</div>
		<div className="p-6">
			<h3 className="text-xl font-bold text-white mb-2">{path.title}</h3>
			<div className="flex justify-between text-gray-300 mb-4">
				<div className="flex items-center gap-1">
					<svg
						className="w-4 h-4 text-blue-400"
						fill="currentColor"
						viewBox="0 0 20 20"
					>
						<path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 005.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
					</svg>
					{path.courses}
				</div>
				<div className="flex items-center gap-1">
					<svg
						className="w-4 h-4 text-blue-400"
						fill="currentColor"
						viewBox="0 0 20 20"
					>
						<path
							fillRule="evenodd"
							d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
							clipRule="evenodd"
						></path>
					</svg>
					{path.hours}
				</div>
			</div>
			<Link
				to={path.path}
				className="inline-block w-full py-2 text-center bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded transition-colors duration-300 shadow-[0_0_15px_rgba(59,130,246,0.2)]"
			>
				Get Started
			</Link>
		</div>
	</motion.div>
);

export default LearningPathsSection;
