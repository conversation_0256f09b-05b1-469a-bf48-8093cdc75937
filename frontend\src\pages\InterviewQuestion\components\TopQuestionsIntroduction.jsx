import React, { useState } from "react";
import { motion } from "framer-motion";

const TopQuestionsIntroduction = ({ onBackToCourse, showPremiumOverlay }) => {
  const [activeTab, setActiveTab] = useState("overview");

  const companies = [
    {
      name: "Google",
      logo: "🔍",
      questions: "150+",
      difficulty: "Hard",
      focus: "Algorithms, System Design, Problem Solving",
      color: "from-blue-500 to-green-500"
    },
    {
      name: "Amazon",
      logo: "📦",
      questions: "200+",
      difficulty: "Medium-Hard",
      focus: "Leadership Principles, Scalability, Customer Focus",
      color: "from-orange-500 to-yellow-500"
    },
    {
      name: "Microsoft",
      logo: "🪟",
      questions: "120+",
      difficulty: "Medium",
      focus: "Coding, Design, Behavioral",
      color: "from-blue-500 to-purple-500"
    },
    {
      name: "Meta",
      logo: "📘",
      questions: "100+",
      difficulty: "Hard",
      focus: "Social Impact, Innovation, Collaboration",
      color: "from-blue-600 to-indigo-600"
    },
    {
      name: "<PERSON>",
      logo: "🍎",
      questions: "80+",
      difficulty: "Medium-Hard",
      focus: "Innovation, Design Thinking, Quality",
      color: "from-gray-500 to-black"
    },
    {
      name: "Netflix",
      logo: "🎬",
      questions: "60+",
      difficulty: "Hard",
      focus: "Performance, Scalability, Culture Fit",
      color: "from-red-500 to-red-600"
    }
  ];

  const interviewTypes = [
    {
      type: "Coding Interviews",
      description: "Algorithm and data structure problems",
      duration: "45-60 minutes",
      skills: ["Problem Solving", "Code Quality", "Optimization", "Communication"],
      icon: "💻",
      color: "from-blue-500 to-cyan-500"
    },
    {
      type: "System Design",
      description: "Design scalable systems and architectures",
      duration: "45-60 minutes",
      skills: ["Architecture", "Scalability", "Trade-offs", "Real-world Systems"],
      icon: "🏗️",
      color: "from-green-500 to-teal-500"
    },
    {
      type: "Behavioral",
      description: "Assess cultural fit and soft skills",
      duration: "30-45 minutes",
      skills: ["Leadership", "Communication", "Teamwork", "Problem Resolution"],
      icon: "🤝",
      color: "from-purple-500 to-pink-500"
    },
    {
      type: "Domain Specific",
      description: "Role-specific technical questions",
      duration: "45-60 minutes",
      skills: ["Expertise", "Best Practices", "Tools", "Industry Knowledge"],
      icon: "🎯",
      color: "from-orange-500 to-red-500"
    }
  ];

  const preparationStrategy = [
    {
      phase: "Foundation Building",
      duration: "2-4 weeks",
      activities: [
        "Master core DSA concepts",
        "Practice easy-medium problems",
        "Learn common patterns",
        "Build coding confidence"
      ],
      focus: "Build strong fundamentals",
      icon: "🏗️"
    },
    {
      phase: "Pattern Recognition",
      duration: "3-4 weeks",
      activities: [
        "Identify problem patterns",
        "Practice medium-hard problems",
        "Time-bound practice sessions",
        "Mock interview preparation"
      ],
      focus: "Recognize and solve patterns efficiently",
      icon: "🧩"
    },
    {
      phase: "Company Preparation",
      duration: "2-3 weeks",
      activities: [
        "Study company-specific questions",
        "Practice system design problems",
        "Prepare behavioral stories",
        "Research company culture"
      ],
      focus: "Target specific companies and roles",
      icon: "🎯"
    },
    {
      phase: "Final Polish",
      duration: "1-2 weeks",
      activities: [
        "Take full mock interviews",
        "Refine communication skills",
        "Review weak areas",
        "Mental preparation"
      ],
      focus: "Perfect your interview performance",
      icon: "✨"
    }
  ];

  return (
    <div className="py-8">
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🎯 Interview Mastery
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            Ace Your{" "}
            <span className="bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
              Technical Interviews
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Master the art of technical interviews with our comprehensive collection of questions, strategies, and expert guidance from top tech companies.
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-center mb-12"
        >
          <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-2 inline-flex">
            {[
              { id: "overview", label: "Overview", icon: "👋" },
              { id: "companies", label: "Companies", icon: "🏢" },
              { id: "types", label: "Interview Types", icon: "📋" },
              { id: "strategy", label: "Preparation", icon: "📚" }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Tab Content */}
        <div className="min-h-[600px]">
          {/* Overview Tab */}
          {activeTab === "overview" && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="grid lg:grid-cols-2 gap-8"
            >
              <div className="space-y-6">
                <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
                    🎯 What We Offer
                  </h3>
                  <ul className="space-y-3 text-white/80">
                    <li className="flex items-start gap-3">
                      <span className="text-green-400 mt-1">✓</span>
                      <span>1000+ interview questions from top tech companies</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <span className="text-green-400 mt-1">✓</span>
                      <span>Detailed solutions with multiple approaches</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <span className="text-green-400 mt-1">✓</span>
                      <span>Company-specific interview patterns and insights</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <span className="text-green-400 mt-1">✓</span>
                      <span>Mock interviews with real-time feedback</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <span className="text-green-400 mt-1">✓</span>
                      <span>Behavioral and system design question banks</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
                    📈 Success Metrics
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-400 mb-1">95%</div>
                      <div className="text-white/70 text-sm">Success Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-400 mb-1">50+</div>
                      <div className="text-white/70 text-sm">Top Companies</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-purple-400 mb-1">10K+</div>
                      <div className="text-white/70 text-sm">Students Placed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-400 mb-1">4.9</div>
                      <div className="text-white/70 text-sm">Average Rating</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
                    🏆 Why Students Choose Us
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        1
                      </div>
                      <div>
                        <h4 className="text-white font-bold mb-1">Real Interview Experience</h4>
                        <p className="text-white/70 text-sm">Questions from actual interviews at top companies</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        2
                      </div>
                      <div>
                        <h4 className="text-white font-bold mb-1">Expert Guidance</h4>
                        <p className="text-white/70 text-sm">Learn from engineers who work at FAANG companies</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        3
                      </div>
                      <div>
                        <h4 className="text-white font-bold mb-1">Comprehensive Coverage</h4>
                        <p className="text-white/70 text-sm">All interview types: coding, system design, behavioral</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-500/20 to-red-500/20 backdrop-blur-lg border border-white/10 rounded-xl p-6">
                  <h3 className="text-xl font-bold text-white mb-3">💡 Success Tips</h3>
                  <ul className="space-y-2 text-white/80 text-sm">
                    <li>• Practice consistently - aim for 2-3 problems daily</li>
                    <li>• Focus on understanding patterns, not memorizing</li>
                    <li>• Practice explaining your thought process aloud</li>
                    <li>• Time yourself to simulate real interview pressure</li>
                  </ul>
                </div>
              </div>
            </motion.div>
          )}

          {/* Companies Tab */}
          {activeTab === "companies" && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {companies.map((company, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6 hover:border-orange-400/50 transition-all duration-300"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${company.color} rounded-xl flex items-center justify-center text-3xl mb-4 mx-auto`}>
                    {company.logo}
                  </div>
                  
                  <h3 className="text-xl font-bold text-white text-center mb-2">{company.name}</h3>
                  
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-white/70">Questions:</span>
                      <span className="text-white font-semibold">{company.questions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Difficulty:</span>
                      <span className="text-orange-400 font-semibold">{company.difficulty}</span>
                    </div>
                    <div>
                      <span className="text-white/70 block mb-1">Focus Areas:</span>
                      <span className="text-white/80 text-xs">{company.focus}</span>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => {
                      if (showPremiumOverlay) {
                        showPremiumOverlay();
                      }
                    }}
                    className={`w-full mt-4 bg-gradient-to-r ${company.color} text-white py-2 rounded-lg font-medium hover:opacity-90 transition-all duration-300`}
                  >
                    View Questions
                  </button>
                </motion.div>
              ))}
            </motion.div>
          )}

          {/* Interview Types Tab */}
          {activeTab === "types" && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="grid md:grid-cols-2 gap-8"
            >
              {interviewTypes.map((type, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6"
                >
                  <div className={`w-12 h-12 bg-gradient-to-r ${type.color} rounded-lg flex items-center justify-center text-2xl mb-4`}>
                    {type.icon}
                  </div>
                  
                  <h3 className="text-xl font-bold text-white mb-2">{type.type}</h3>
                  <p className="text-white/70 mb-3">{type.description}</p>
                  <p className="text-sm text-orange-400 mb-4">Duration: {type.duration}</p>
                  
                  <div>
                    <h4 className="text-white font-semibold mb-2">Key Skills:</h4>
                    <div className="flex flex-wrap gap-2">
                      {type.skills.map((skill, skillIndex) => (
                        <span key={skillIndex} className="px-3 py-1 bg-white/10 text-white/80 rounded-full text-sm">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {/* Preparation Strategy Tab */}
          {activeTab === "strategy" && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-3">📚 Your Interview Preparation Roadmap</h3>
                <p className="text-white/70">Follow this structured approach to maximize your interview success</p>
              </div>
              
              {preparationStrategy.map((phase, index) => (
                <div key={index} className="relative">
                  {index < preparationStrategy.length - 1 && (
                    <div className="absolute left-8 top-20 w-0.5 h-16 bg-gradient-to-b from-orange-400 to-transparent"></div>
                  )}
                  <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6 ml-4">
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center text-2xl flex-shrink-0">
                        {phase.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="text-xl font-bold text-white">{phase.phase}</h4>
                          <span className="px-3 py-1 bg-orange-500/20 text-orange-400 rounded-full text-sm font-medium">
                            {phase.duration}
                          </span>
                        </div>
                        <p className="text-white/70 mb-4">{phase.focus}</p>
                        <div className="grid md:grid-cols-2 gap-2">
                          {phase.activities.map((activity, activityIndex) => (
                            <div key={activityIndex} className="flex items-center gap-2 text-sm text-white/80">
                              <span className="text-green-400">•</span>
                              <span>{activity}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </motion.div>
          )}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-orange-500/20 to-red-500/20 backdrop-blur-lg border border-white/10 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              🚀 Ready to Start Your Interview Journey?
            </h3>
            <p className="text-white/80 mb-6">
              Join thousands of successful candidates who landed their dream jobs with our proven methodology!
            </p>
            <button
              onClick={() => {
                if (showPremiumOverlay) {
                  showPremiumOverlay();
                }
              }}
              className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-orange-600 hover:to-red-600 transition-all duration-300 transform hover:scale-105"
            >
              Start Preparing Now
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default TopQuestionsIntroduction;
