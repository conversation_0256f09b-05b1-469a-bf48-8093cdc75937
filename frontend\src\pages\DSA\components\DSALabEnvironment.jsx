import React from "react";
import { motion } from "framer-motion";
import ReusableCodeEditor from "../../../components/ui/ReusableCodeEditor";

const DSALabEnvironment = ({ onBackToCourse, showPremiumOverlay }) => {
  const practiceProblems = [
    {
      title: "Array Manipulation",
      difficulty: "Easy",
      description: "Practice basic array operations and manipulations",
      tags: ["Arrays", "Loops", "Indexing"]
    },
    {
      title: "Linked List Operations",
      difficulty: "Medium",
      description: "Implement and manipulate linked list data structures",
      tags: ["Linked Lists", "Pointers", "Memory Management"]
    },
    {
      title: "Binary Search Tree",
      difficulty: "Medium",
      description: "Create and traverse binary search trees",
      tags: ["Trees", "Recursion", "Sorting"]
    },
    {
      title: "Graph Algorithms",
      difficulty: "Hard",
      description: "Implement BFS, DFS and shortest path algorithms",
      tags: ["Graphs", "Traversal", "Pathfinding"]
    }
  ];

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-teal-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Practice Lab</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Interactive DSA Practice Environment</h3>
          <p className="text-gray-300">
            Sharpen your DSA skills with our interactive coding environment. Solve problems, 
            test your solutions, and track your progress.
          </p>
        </div>
        
        {/* Practice Problems */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {practiceProblems.map((problem, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 bg-gray-800/30 rounded-lg p-4 hover:shadow-md transition-shadow"
              onClick={showPremiumOverlay}
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-white">{problem.title}</h4>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  problem.difficulty === "Easy" ? "bg-green-100 text-green-800" :
                  problem.difficulty === "Medium" ? "bg-yellow-100 text-yellow-800" :
                  "bg-red-100 text-red-800"
                }`}>
                  {problem.difficulty}
                </span>
              </div>
              <p className="text-sm text-gray-300 mb-3">{problem.description}</p>
              <div className="flex flex-wrap gap-2">
                {problem.tags.map((tag, idx) => (
                  <span key={idx} className="px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded-md border border-blue-700/30">
                    {tag}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Code Editor */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8"
        >
          <ReusableCodeEditor 
            showPremiumOverlay={showPremiumOverlay} 
            challenges={[
              {
                id: "dsa-challenge-1",
                title: "Array Manipulation",
                difficulty: "Beginner",
                description: "Implement basic array operations and algorithms.",
                requirements: [
                  "Create a function to find the maximum element in an array",
                  "Implement a function to reverse an array in-place",
                  "Write a function to remove duplicates from an array"
                ],
                template: `// DSA Challenge: Array Manipulation

// 1. Find the maximum element in an array
function findMax(arr) {
  // TODO: Implement your solution here
  
  return max;
}

// 2. Reverse an array in-place
function reverseArray(arr) {
  // TODO: Implement your solution here
  
  return arr;
}

// 3. Remove duplicates from an array
function removeDuplicates(arr) {
  // TODO: Implement your solution here
  
  return result;
}`
              },
              {
                id: "dsa-challenge-2",
                title: "Linked List Operations",
                difficulty: "Intermediate",
                description: "Implement common linked list operations and algorithms.",
                requirements: [
                  "Create a Node class for a singly linked list",
                  "Implement a function to reverse a linked list",
                  "Write a function to detect a cycle in a linked list"
                ],
                template: `// DSA Challenge: Linked List Operations

// Node class for a singly linked list
class Node {
  constructor(value) {
    this.value = value;
    this.next = null;
  }
}

// 1. Reverse a linked list
function reverseLinkedList(head) {
  // TODO: Implement your solution here
  
  return newHead;
}

// 2. Detect a cycle in a linked list
function hasCycle(head) {
  // TODO: Implement your solution here
  
  return false; // or true if cycle exists
}`
              }
            ]} 
          />
        </motion.div>
      </div>
    </div>
  );
};

export default DSALabEnvironment;