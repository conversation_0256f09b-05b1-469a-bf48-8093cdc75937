const nlpCaseStudies = [
  {
    title: "Sentiment Analysis for Social Media Monitoring",
    difficulty: "Beginner",
    objective: "Build a sentiment analysis system to monitor brand perception on social media platforms.",
    scenario: "A marketing team needs to track customer sentiment about their brand across social media. Create a sentiment analysis model that can classify tweets and posts as positive, negative, or neutral to help them respond quickly to customer feedback.",
    keyConcepts: ["Sentiment Analysis", "Text Preprocessing", "Transformers", "Social Media Analytics", "Brand Monitoring"],
    solution: `from transformers import pipeline
import pandas as pd
from datetime import datetime

# Initialize pre-trained sentiment analyzer
sentiment_analyzer = pipeline(
    "sentiment-analysis",
    model="cardiffnlp/twitter-roberta-base-sentiment-latest",
    return_all_scores=True
)

# Sample social media posts
social_posts = [
    "Just tried the new product and I'm absolutely loving it! #amazing",
    "Customer service was terrible today. Very disappointed.",
    "The delivery was on time and packaging was good.",
    "Not sure about this new update, seems confusing.",
    "Best purchase I've made this year! Highly recommend!"
]

# Analyze sentiment for each post
results = []
for post in social_posts:
    sentiment_scores = sentiment_analyzer(post)
    best_sentiment = max(sentiment_scores[0], key=lambda x: x['score'])
    
    results.append({
        'post': post,
        'sentiment': best_sentiment['label'],
        'confidence': best_sentiment['score'],
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

# Create DataFrame for analysis
df = pd.DataFrame(results)

# Display results
print("Social Media Sentiment Analysis Results:")
for _, row in df.iterrows():
    print(f"Post: {row['post'][:50]}...")
    print(f"Sentiment: {row['sentiment']} (Confidence: {row['confidence']:.3f})")
    print("-" * 30)

# Summary statistics
sentiment_counts = df['sentiment'].value_counts()
print("Sentiment Distribution:")
for sentiment, count in sentiment_counts.items():
    percentage = (count / len(df)) * 100
    print(f"{sentiment}: {count} posts ({percentage:.1f}%)")`
  },
  {
    title: "Advanced Named Entity Recognition for Document Processing",
    difficulty: "Intermediate",
    objective: "Develop a comprehensive NER system to extract and categorize entities from business documents.",
    scenario: "A legal firm needs to automatically extract key information from contracts and legal documents. Build an NER system that can identify people, organizations, dates, monetary amounts, and legal terms to streamline document review processes.",
    keyConcepts: ["Named Entity Recognition", "Document Processing", "Custom NER Models", "Legal Text Analysis", "Information Extraction"],
    solution: `import spacy
import pandas as pd
from collections import defaultdict
import re

# Load English model with NER capabilities
nlp = spacy.load("en_core_web_sm")

# Sample legal document text
legal_text = """
This Agreement is entered into on March 15, 2024, between TechCorp Inc., 
a Delaware corporation ("Company"), and John Smith, an individual 
("Consultant"). The Consultant agrees to provide software development 
services for a total compensation of $50,000. The term of this agreement 
shall commence on April 1, 2024, and terminate on December 31, 2024.
"""

# Process the document
doc = nlp(legal_text)

# Extract and categorize entities
entities = defaultdict(list)
for ent in doc.ents:
    entities[ent.label_].append({
        'text': ent.text,
        'start': ent.start_char,
        'end': ent.end_char
    })

# Custom pattern matching for legal-specific entities
def extract_monetary_amounts(text):
    pattern = r'$[d,]+(?:.d{2})?'
    return re.findall(pattern, text)

def extract_dates(text):
    date_pattern = r'b(?:January|February|March|April|May|June|July|August|September|October|November|December)s+d{1,2},s+d{4}b'
    return re.findall(date_pattern, text)

# Extract custom entities
monetary_amounts = extract_monetary_amounts(legal_text)
dates = extract_dates(legal_text)

# Display results
print("Named Entity Recognition Results")
print("=" * 40)

# Standard NER entities
for label, ents in entities.items():
    if ents:
        print(f"{label}:")
        for ent in ents:
            print(f"  - {ent['text']}")

# Custom extracted entities
if monetary_amounts:
    print("MONETARY AMOUNTS:")
    for amount in monetary_amounts:
        print(f"  - {amount}")

if dates:
    print("DATES:")
    for date in dates:
        print(f"  - {date}")

# Create structured output
structured_data = {
    'parties': [ent['text'] for ent in entities.get('ORG', []) + entities.get('PERSON', [])],
    'locations': [ent['text'] for ent in entities.get('GPE', [])],
    'monetary_amounts': monetary_amounts,
    'dates': dates,
    'document_type': 'Service Agreement'
}

print("Structured Document Summary:")
print(f"Document Type: {structured_data['document_type']}")
print(f"Parties Involved: {', '.join(structured_data['parties'])}")
print(f"Monetary Values: {', '.join(structured_data['monetary_amounts'])}")
print(f"Key Dates: {', '.join(structured_data['dates'])}")`
  }
];

export default nlpCaseStudies;