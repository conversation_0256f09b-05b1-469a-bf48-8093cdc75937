import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

const faqAPI = axios.create({
  baseURL: `${API_BASE_URL}/faqs`,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const faqService = {
  // Get all FAQs
  getAllFAQs: async () => {
    try {
      const response = await faqAPI.get('');
      return response.data;
    } catch (error) {
      console.error('Error fetching FAQs:', error);
      throw error;
    }
  },

  // Get FAQs by category
  getFAQsByCategory: async (category) => {
    try {
      const response = await faqAPI.get(`category/${category}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching FAQs for category ${category}:`, error);
      throw error;
    }
  },

  // Get FAQ by ID
  getFAQById: async (id) => {
    try {
      const response = await faqAPI.get(`${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching FAQ with ID ${id}:`, error);
      throw error;
    }
  },

  // Create new FAQ (Admin)
  createFAQ: async (faqData) => {
    try {
      const response = await faqAPI.post('', faqData);
      return response.data;
    } catch (error) {
      console.error('Error creating FAQ:', error);
      throw error;
    }
  },

  // Update FAQ (Admin)
  updateFAQ: async (id, faqData) => {
    try {
      const response = await faqAPI.put(`${id}`, faqData);
      return response.data;
    } catch (error) {
      console.error(`Error updating FAQ with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete FAQ (Admin)
  deleteFAQ: async (id) => {
    try {
      const response = await faqAPI.delete(`${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting FAQ with ID ${id}:`, error);
      throw error;
    }
  }
};

export default faqService;
