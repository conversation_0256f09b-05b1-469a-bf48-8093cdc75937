import React from "react";
import { motion } from "framer-motion";
import CaseStudyCard from "./CaseStudyCard";

const CaseStudySection = ({ 
  activeSection,
  currentCaseStudies,
  topicDisplayName,
  activeIndex,
  showSolutions,
  toggleChapter,
  toggleSolution,
  itemVariants 
}) => {
  return (
    <motion.div variants={itemVariants} className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-3xl shadow-xl p-8 md:p-12">
      <div className="flex items-center justify-between mb-8">
        <h2 id={activeSection} className="text-3xl font-bold text-white">
          {topicDisplayName} Case Studies
        </h2>
        <div className="text-sm text-white/80 bg-white/10 px-4 py-2 rounded-full">
          {currentCaseStudies.length} Studies Available
        </div>
      </div>
      
      <div className="space-y-6">
        {currentCaseStudies.length > 0 ? (
          currentCaseStudies.map((study, index) => (
            <CaseStudyCard
              key={`${activeSection}-${index}`}
              study={study}
              index={index}
              activeSection={activeSection}
              activeIndex={activeIndex}
              showSolutions={showSolutions}
              toggleChapter={toggleChapter}
              toggleSolution={toggleSolution}
              itemVariants={itemVariants}
            />
          ))
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12 text-gray-500"
          >
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-semibold mb-2">No Case Studies Available</h3>
            <p>Case studies for this topic are coming soon!</p>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default CaseStudySection;
