{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon index.js", "seed:faqs": "node seedFAQs.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.9", "bcrypt": "^5.1.1", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.10.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "validator": "^13.12.0"}}