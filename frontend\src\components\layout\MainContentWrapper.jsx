import Content from "./Content";

const MainContentWrapper = ({ isSidebarOpen, toggleSidebar }) => {
  return (
    <div
      className="relative z-10 transition-all duration-300 ease-out content-animate"
      style={{ 
        marginLeft: isSidebarOpen ? "280px" : "0px"
      }}
    >
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes slideInContent {
          from { transform: translateX(100px); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        .content-animate {
          animation: slideInContent 0.4s ease-out forwards;
        }
        
        /* KodeKloud style overrides - Exact Match */
        :root {
          --kodekloud-primary: #2563eb;
          --kodekloud-primary-dark: #1d4ed8;
          --kodekloud-secondary: #4f46e5;
          --kodekloud-accent: #7c3aed;
          --kodekloud-text: #0f172a;
          --kodekloud-text-light: #64748b;
          --kodekloud-bg-light: #f8fafc;
          --kodekloud-gradient-start: #0f172a;
          --kodekloud-gradient-middle: #1e3b8a;
          --kodekloud-gradient-end: #4c1d95;
          --kodekloud-card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }
      `}} />
      <Content toggleSidebar={toggleSidebar} />
    </div>
  );
};

export default MainContentWrapper;
