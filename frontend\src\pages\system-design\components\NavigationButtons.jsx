import { motion } from "framer-motion";

const NavigationButtons = () => {
  const navItems = [
    {
      id: "real-time-projects",
      icon: "fas fa-folder",
      label: "Real time Projects",
      gradient: "from-blue-500 to-blue-600"
    },
    {
      id: "interview-questions",
      icon: "fas fa-book",
      label: "Interview Questions",
      gradient: "from-purple-500 to-purple-600"
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"
    >
      {navItems.map((item, index) => (
        <motion.div
          key={item.id}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
          whileHover={{ scale: 1.05, y: -5 }}
          whileTap={{ scale: 0.95 }}
          className={`
            bg-gradient-to-br ${item.gradient} text-white 
            rounded-xl p-6 text-center cursor-pointer 
            shadow-lg hover:shadow-xl transition-all duration-300
            backdrop-blur-sm border border-white/20
          `}
          id={item.id}
        >
          <motion.i
            className={`${item.icon} text-3xl mb-3 block`}
            whileHover={{ rotate: 10, scale: 1.1 }}
            transition={{ duration: 0.3 }}
          />
          <span className="text-lg font-semibold">{item.label}</span>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default NavigationButtons;
