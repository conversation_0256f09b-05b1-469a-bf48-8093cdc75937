import { Link, useNavigate } from "react-router-dom";
import "./authentication.css";
import { useState } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import { Spinner } from "../../components/ui";

const SignIn = () => {
  const navigate = useNavigate();
  const [image, setImage] = useState(null);
  const [file, setFile] = useState(null);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateFields = () => {
    let newErrors = {};
    if (!name) newErrors.name = "Name is required";
    if (!email) newErrors.email = "Email is required";
    if (!password) newErrors.password = "Password is required";
    if (!file) {
      toast.error("Please select an Image");
      return false;
    }
    setErrors(newErrors);
    setTimeout(() => {
      setErrors({});
    }, 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignUp = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;

    setLoading(true);
    const formData = new FormData();
    formData.append("name", name);
    formData.append("email", email);
    formData.append("password", password);
    formData.append("image", file);

    try {
      const { data } = await axios.post(
        "http://localhost:8000/api/v1/auth/register",

        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (data.success) {
        localStorage.setItem("activationToken", data.activationToken);
        toast.success(data.message);
        navigate("/verification");
      } else {
        toast.error(data.error);
      }
    } catch (error) {
      console.error(
        "Registration Error:",
        error.response?.data || error.message
      );

      toast.error("Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="login-container">
        <div className="signup-box">
          <h2
            style={{
              marginBottom: "20px",
              fontSize: "26px",
              fontWeight: "700",
            }}
          >
            Sign Up Page
          </h2>
          <label className="avatar-label">
            <input
              type="file"
              accept="image/*"
              className="file-input"
              onChange={handleImageChange}
            />
            <div className="avatar">
              {image ? (
                <img src={image} alt="Avatar" className="avatar-img" />
              ) : (
                "📷"
              )}
            </div>
          </label>
          <input
            type="text"
            placeholder="Name"
            className="input-field"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
          {errors.name && <p className="error-message">{errors.name}</p>}
          <input
            type="email"
            placeholder="Email"
            className="input-field"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          {errors.email && <p className="error-message">{errors.email}</p>}
          <input
            type="password"
            placeholder="Password"
            className="input-field"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          {errors.password && (
            <p className="error-message">{errors.password}</p>
          )}
          <button
            className="signup-button"
            style={{ marginBottom: "20px" }}
            onClick={handleSignUp}
            disabled={loading}
          >
            {loading ? <Spinner /> : "Sign Up"}
          </button>
          <button className="google-signin-button">
            <img
              src="/images/google.jpg"
              alt="Google"
              className="google-logo"
            />
            Sign In With Google
          </button>
          <p className="signin-text">
            Already have an account?{" "}
            <Link to={"/log-In"} className="signin-link">
              Log In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
