/* SQLStyles.css */

/* Animation keyframes for different effects */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* General Styles */
.sql-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  animation: fadeIn 0.8s ease-out;
}

.section {
  margin-bottom: 3rem;
  animation: slideUp 0.6s ease-out;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #2d3748;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.75rem;
}

.section-title i {
  margin-right: 0.75rem;
  color: #4299e1;
}

/* Question Cards */
.question-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  transition: all 0.3s ease;
  border-left: 4px solid #4299e1;
}

.question-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.question-header {
  margin-bottom: 0.75rem;
}

.question-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

/* Tags */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1rem;
  gap: 0.5rem;
}

.tag {
  background: #ebf4ff;
  color: #4299e1;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.tag:hover {
  background: #4299e1;
  color: white;
}

/* Key Points */
.key-points {
  margin: 0;
  padding-left: 1.5rem;
  list-style-type: disc;
}

.key-points li {
  margin-bottom: 0.5rem;
  color: #4a5568;
  line-height: 1.5;
}

/* Difficulty Badges */
.difficulty-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.75rem;
}

.difficulty-easy {
  background: #c6f6d5;
  color: #22543d;
}

.difficulty-medium {
  background: #feebc8;
  color: #744210;
}

.difficulty-hard {
  background: #fed7d7;
  color: #742a2a;
}

/* Interview Checklist */
.question {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  color: #2d3748;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question:hover {
  background: #f7fafc;
}

.toggle-icon {
  transition: transform 0.3s ease;
}

.toggle-icon.active {
  transform: rotate(180deg);
}

.answer {
  background: #f7fafc;
  padding: 1rem;
  margin-top: -0.5rem;
  margin-bottom: 1rem;
  border-radius: 0 0 8px 8px;
  color: #4a5568;
  animation: fadeIn 0.4s ease-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Navigation Box */
.nav-container {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.nav-box {
  flex: 1;
  min-width: 200px;
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.nav-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.nav-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #4299e1;
  transition: all 0.3s ease;
}

.nav-box:hover .nav-icon {
  animation: pulse 1s infinite;
}

/* Hero Section */
.logo-container {
  text-align: center;
  margin: 2rem 0;
}

.logo {
  max-width: 300px;
  height: auto;
  transition: all 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

/* Custom SQL Button Style */
.sql-button {
  background: #4299e1;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.sql-button:hover {
  background: #3182ce;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(66, 153, 225, 0.3);
}

.sql-button i {
  margin-right: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.5rem;
  }
  
  .nav-container {
    flex-direction: column;
  }
  
  .logo {
    max-width: 200px;
  }
}
