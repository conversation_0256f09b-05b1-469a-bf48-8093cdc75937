import mongoose from "mongoose";

const liveClassSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Class title is required"],
    },
    instructor: {
      type: String,
      required: [true, "Instructor name is required"],
    },
    description: {
      type: String,
      default: "",
    },
    dateTime: {
      type: Date,
      required: [true, "Class date and time is required"],
    },
    duration: {
      type: Number, // Duration in minutes
      required: [true, "Class duration is required"],
    },
    category: {
      type: String,
      required: [true, "Class category is required"],
    },
    maxParticipants: {
      type: Number,
      default: 100,
    },
    registeredParticipants: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    meetingLink: {
      type: String,
      default: "",
    },
    thumbnail: {
      type: String,
      default: "",
    },
    tags: [
      {
        type: String,
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Create a virtual field to check if the class is upcoming
liveClassSchema.virtual("isUpcoming").get(function () {
  return new Date(this.dateTime) > new Date();
});

// Create an index on dateTime for efficient querying of upcoming classes
liveClassSchema.index({ dateTime: 1 });

const LiveClass = mongoose.model("LiveClass", liveClassSchema);
export default LiveClass;