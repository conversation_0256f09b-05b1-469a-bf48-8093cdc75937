import { FaUsers } from "react-icons/fa";
import { BsChatDotsFill } from "react-icons/bs";
import { BsBox2Fill } from "react-icons/bs";
import { MdOutlinePendingActions } from "react-icons/md";
import { useEffect, useState } from "react";
import { DeleteModal } from "../../components/ui";
import axiosInstance from "../../utils/axiosInstance";
import toast from "react-hot-toast";

const User = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdateOpen, setIsUpdateOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [newRole, setNewRole] = useState("");
  const [users, setUsers] = useState([]);

  const gridData = [
    {
      icon: <FaUsers size={50} className="text-white" />,
      label: "Active Users",
      count: "1,234",
      gradient: "from-blue-500 to-indigo-500",
    },
    {
      icon: <BsChatDotsFill size={50} className="text-white" />,
      label: "Messages",
      count: "567",
      gradient: "from-green-400 to-green-600",
    },
    {
      icon: <BsBox2Fill size={45} className="text-white" />,
      label: "Orders",
      count: "789",
      gradient: "from-yellow-400 to-yellow-600",
    },
    {
      icon: <MdOutlinePendingActions size={50} className="text-white" />,
      label: "Pending Tasks",
      count: "432",
      gradient: "from-red-400 to-red-600",
    },
  ];

  const openUpdateModal = (userId) => {
    setSelectedUserId(userId);
    setIsUpdateOpen(true);
  };

  const closeUpdateModal = () => {
    setSelectedUserId(null);
    setIsUpdateOpen(false);
    setNewRole("");
  };

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        // setLoading(true);
        const { data } = await axiosInstance.get(
          "http://localhost:8000/api/v1/auth/all-users",
          { withCredentials: true }
        );
        setUsers(data.users);
        // setLoading(false);
      } catch (err) {
        console.log(err.response?.data?.message || "Something went wrong!");
        // setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const handleUpdateRole = async () => {
    if (!newRole.trim()) return toast.success("Please Enter Role!");

    try {
      const { data } = await axiosInstance.put(
        `http://localhost:8000/api/v1/auth/update-role/${selectedUserId}`,
        {
          newRole,
        },
        { withCredentials: true }
      );

      if (data.success) {
        toast.success(data.message);
        setNewRole("");
      } else {
        toast.error(data.error || "Error Updating Role");
      }
      closeUpdateModal();
      window.location.reload();
    } catch (err) {
      toast.error(err?.data?.message || "Error updating role!");
      console.log();
    }
  };

  const openDeleteModal = (userId) => {
    setSelectedUserId(userId);
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setSelectedUserId(null);
    setIsOpen(false);
  };

  const deleteConfirmation = async () => {
    if (!selectedUserId) {
      toast.error("User ID missing!");
      return;
    }

    try {
      const { data } = await axiosInstance.delete(
        `http://localhost:8000/api/v1/auth/delete-user/${selectedUserId}`,
        {
          withCredentials: true,
        }
      );

      if (data.success) {
        toast.success(data.message);
      } else {
        toast.error(data.error || "Error deleting user");
      }

      closeDeleteModal();
      window.location.reload();
    } catch (err) {
      toast.error(err?.response?.data?.message || "Error deleting user!");
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
        {gridData.map((item, index) => (
          <div
            key={index}
            className={`bg-gradient-to-br ${item.gradient} text-white p-5 flex justify-between items-center cursor-pointer rounded-md shadow-md hover:bg-blue-200 transition  hover:-translate-y-1 hover:shadow-xl`}
          >
            <div className="flex items-center">{item.icon}</div>
            <div>
              <p className="text-white  text-lg">{item.label}</p>
              <h4 className="text-xl font-semibold">{item.count}</h4>
            </div>
          </div>
        ))}
      </div>
      <br />
      <br />
      <div className="w-full h-[500px] bg-gray-200 rounded-md p-5 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-300">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-4 gap-3">
          <h3 className="text-lg md:text-xl font-semibold text-center md:text-left">
            User/Admin Management
          </h3>
        </div>

        {/* Search Input */}
        <div className="w-full mb-5">
          <input
            type="text"
            placeholder="Search By Name, Email or Role"
            className="w-full h-12 px-4 rounded-md border border-gray-300 bg-white text-sm focus:ring-2 focus:ring-[#7B61FF] outline-none"
          />
        </div>

        {/* Responsive Table Wrapper */}
        <div className="overflow-x-auto rounded-lg shadow-md">
          <table className="w-full border-collapse min-w-[600px] md:min-w-full">
            <thead className="bg-blue-600 text-white text-sm md:text-base rounded-t-md">
              <tr className="bg-blue-600 text-white text-sm md:text-base">
                <th className="p-3 border">Name</th>
                <th className="p-3 border">Email</th>
                <th className="p-3 border">Role</th>
                <th className="p-3 border">Status</th>
                <th className="p-3 border">Action</th>
              </tr>
            </thead>
            <tbody className="text-center text-sm">
              {users.map((user, index) => (
                <tr key={index} className="bg-blue-100 transition-colors">
                  <td className="p-3 border text-gray-800">{user.name}</td>
                  <td className="p-3 border break-words">{user.email}</td>
                  <td className="p-3 border">{user.role}</td>
                  <td className="p-3 border">{user.status}</td>
                  <td className="p-3 border">
                    <div className="flex flex-wrap gap-2 justify-center">
                      <button
                        className="bg-red-500 text-white px-2 md:px-3 py-1 rounded-md hover:bg-red-600 text-xs md:text-sm"
                        onClick={() => openDeleteModal(user._id)}
                      >
                        Delete
                      </button>
                      <button
                        className="bg-blue-500 text-white px-2 md:px-3 py-1 rounded-md hover:bg-blue-600 text-xs md:text-sm"
                        onClick={() => openUpdateModal(user._id)}
                      >
                        Update
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <DeleteModal
        title={"Delete User"}
        message={"Are You Sure Want to Delete?"}
        onClose={closeDeleteModal}
        isOpen={isOpen}
        onConfirm={deleteConfirmation}
      />

      {isUpdateOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-md">
          <div className="fixed top-1/2 left-1/2 w-[400px] -translate-x-1/2 -translate-y-1/2 rounded-lg border-2 border-black/10 bg-gradient-to-br from-white to-gray-100 p-6 text-center shadow-lg animate-[modal-pop_0.3s_ease-in-out]">
            <span
              className="absolute right-4 top-3 cursor-pointer text-[22px] text-gray-600 transition-all duration-300 hover:scale-110 hover:text-red-600"
              onClick={closeUpdateModal}
            >
              ✖
            </span>
            <p className="mb-5 text-2xl font-bold text-blue-500 shadow-md">
              Update User Role
            </p>
            <div className="w-full text-left">
              <label
                htmlFor="role"
                className="mb-1 block text-lg font-bold text-gray-700"
              >
                New Role
              </label>
            </div>
            <input
              type="text"
              placeholder="Enter Role"
              className="w-full rounded-md border-2 border-blue-500 p-3 text-lg outline-none transition-all duration-300 shadow-md focus:border-blue-700 focus:shadow-lg"
              value={newRole}
              onChange={(e) => setNewRole(e.target.value)}
            />
            <div className="mt-5 flex justify-center">
              <button
                className="rounded-md border-2 border-blue-700 bg-gradient-to-br from-blue-500 to-blue-700 px-6 py-3 text-lg font-bold uppercase tracking-wider text-white shadow-lg transition-all duration-300 hover:scale-105 hover:bg-gradient-to-br hover:from-blue-700 hover:to-blue-900 hover:shadow-xl"
                onClick={handleUpdateRole}
              >
                Update
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default User;
