import { useState } from "react";
import toast from "react-hot-toast";
import axiosInstance from "../../utils/axiosInstance";

const ChangePassword = () => {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [errors, setErrors] = useState({});

  const validateFields = () => {
    let newErrors = {};
    if (!currentPassword) newErrors.currentPassword = "Current Password is required";
    if (!newPassword) newErrors.newPassword = "New Password is required";
    if (!confirmNewPassword)
      newErrors.confirmNewPassword = "Confirm Password is required";

    setErrors(newErrors);
    setTimeout(() => {
      setErrors({});
    }, 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateFields()) return;
    try {
      const { data } = await axiosInstance.put(
        "http://localhost:8000/api/v1/auth/user/update-password",
        {
          currentPassword,
          newPassword,
          confirmNewPassword,
        },
        { withCredentials: true }
      );

      if (data.success) {
        toast.success(data.message);
        setCurrentPassword("");
        setNewPassword("");
        setConfirmNewPassword("");
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      toast.error(error.message || "Failed to change password.");
    }
  };
  return (
    <div>
      <div className="change-password">
        <div className="password-card">
          <h1>Change Password</h1>
          <form onSubmit={handleSubmit}>
            <input
              type="password"
              placeholder="Current Password..."
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
            />
            {errors.currentPassword && (
              <p className="error-message">{errors.currentPassword}</p>
            )}
            <input
              type="password"
              placeholder="New Password..."
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
            />
            {errors.newPassword && (
              <p className="error-message">{errors.newPassword}</p>
            )}
            <input
              type="password"
              placeholder="Confirm New Password..."
              value={confirmNewPassword}
              onChange={(e) => setConfirmNewPassword(e.target.value)}
            />
            {errors.confirmNewPassword && (
              <p className="error-message">{errors.confirmNewPassword}</p>
            )}
            <button type="submit">Save</button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
