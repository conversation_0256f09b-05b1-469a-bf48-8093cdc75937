import { useState } from "react";
import { <PERSON>a<PERSON><PERSON>, FaEyeSlash, FaLock, FaShieldAlt, FaCheck, FaTimes } from "react-icons/fa";
import toast from "react-hot-toast";
import axiosInstance from "../../utils/axiosInstance";

const ChangePassword = () => {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  // Password strength checker
  const checkPasswordStrength = (password) => {
    const requirements = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    const score = Object.values(requirements).filter(Boolean).length;
    return { requirements, score };
  };

  const passwordStrength = checkPasswordStrength(newPassword);

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const validateFields = () => {
    let newErrors = {};

    if (!currentPassword) {
      newErrors.currentPassword = "Current Password is required";
    }

    if (!newPassword) {
      newErrors.newPassword = "New Password is required";
    } else if (passwordStrength.score < 3) {
      newErrors.newPassword = "Password must meet at least 3 security requirements";
    }

    if (!confirmNewPassword) {
      newErrors.confirmNewPassword = "Confirm Password is required";
    } else if (newPassword !== confirmNewPassword) {
      newErrors.confirmNewPassword = "Passwords do not match";
    }

    if (currentPassword === newPassword) {
      newErrors.newPassword = "New password must be different from current password";
    }

    setErrors(newErrors);
    setTimeout(() => {
      setErrors({});
    }, 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateFields()) return;
    setLoading(true);

    try {
      const { data } = await axiosInstance.put(
        "http://localhost:8000/api/v1/auth/user/update-password",
        {
          currentPassword,
          newPassword,
          confirmNewPassword,
        },
        { withCredentials: true }
      );

      if (data.success) {
        toast.success(data.message || "Password changed successfully!");
        setCurrentPassword("");
        setNewPassword("");
        setConfirmNewPassword("");
        setShowPasswords({ current: false, new: false, confirm: false });
      } else {
        toast.error(data.message || "Failed to change password.");
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to change password.");
    } finally {
      setLoading(false);
    }
  };
  const getStrengthColor = (score) => {
    if (score <= 2) return "text-red-400";
    if (score <= 3) return "text-yellow-400";
    if (score <= 4) return "text-blue-400";
    return "text-green-400";
  };

  const getStrengthText = (score) => {
    if (score <= 2) return "Weak";
    if (score <= 3) return "Fair";
    if (score <= 4) return "Good";
    return "Strong";
  };

  return (
    <div className="min-h-screen w-full py-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-red-200 via-orange-200 to-yellow-200 bg-clip-text text-transparent mb-2">
            🔐 Change Password
          </h1>
          <p className="text-slate-400 text-lg">Update your password to keep your account secure</p>
        </div>

        {/* Main Form Card */}
        <div className="relative bg-gradient-to-br from-red-900/60 via-orange-800/70 to-slate-800/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-red-500/30 hover:border-orange-400/50 transition-all duration-500 group overflow-hidden">
          {/* Animated background elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-600/10 via-orange-600/10 to-yellow-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-full blur-3xl transform translate-x-16 -translate-y-16 z-0"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-orange-400/20 to-yellow-400/20 rounded-full blur-2xl transform -translate-x-12 translate-y-12 z-0"></div>

          <form onSubmit={handleSubmit} className="relative z-10 space-y-6">
            {/* Current Password */}
            <div className="space-y-2">
              <label className="text-sm font-semibold text-orange-200 flex items-center gap-2">
                🔒 Current Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords.current ? "text" : "password"}
                  placeholder="Enter your current password..."
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  className="w-full px-4 py-3 pr-12 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-orange-400/50 focus:ring-2 focus:ring-orange-400/20 focus:outline-none transition-all duration-300"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('current')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-orange-300 transition-colors duration-200"
                >
                  {showPasswords.current ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              {errors.currentPassword && (
                <p className="text-red-400 text-sm flex items-center gap-1">
                  ⚠️ {errors.currentPassword}
                </p>
              )}
            </div>

            {/* New Password */}
            <div className="space-y-2">
              <label className="text-sm font-semibold text-orange-200 flex items-center gap-2">
                🔑 New Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords.new ? "text" : "password"}
                  placeholder="Enter your new password..."
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="w-full px-4 py-3 pr-12 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-orange-400/50 focus:ring-2 focus:ring-orange-400/20 focus:outline-none transition-all duration-300"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('new')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-orange-300 transition-colors duration-200"
                >
                  {showPasswords.new ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>

              {/* Password Strength Indicator */}
              {newPassword && (
                <div className="mt-3 p-3 bg-slate-900/30 rounded-lg border border-slate-600/30">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-slate-300">Password Strength:</span>
                    <span className={`text-sm font-semibold ${getStrengthColor(passwordStrength.score)}`}>
                      {getStrengthText(passwordStrength.score)}
                    </span>
                  </div>

                  <div className="w-full bg-slate-700 rounded-full h-2 mb-3">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        passwordStrength.score <= 2 ? 'bg-red-500' :
                        passwordStrength.score <= 3 ? 'bg-yellow-500' :
                        passwordStrength.score <= 4 ? 'bg-blue-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                    ></div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-xs">
                    <div className={`flex items-center gap-1 ${passwordStrength.requirements.length ? 'text-green-400' : 'text-slate-400'}`}>
                      {passwordStrength.requirements.length ? <FaCheck /> : <FaTimes />}
                      <span>8+ characters</span>
                    </div>
                    <div className={`flex items-center gap-1 ${passwordStrength.requirements.uppercase ? 'text-green-400' : 'text-slate-400'}`}>
                      {passwordStrength.requirements.uppercase ? <FaCheck /> : <FaTimes />}
                      <span>Uppercase letter</span>
                    </div>
                    <div className={`flex items-center gap-1 ${passwordStrength.requirements.lowercase ? 'text-green-400' : 'text-slate-400'}`}>
                      {passwordStrength.requirements.lowercase ? <FaCheck /> : <FaTimes />}
                      <span>Lowercase letter</span>
                    </div>
                    <div className={`flex items-center gap-1 ${passwordStrength.requirements.number ? 'text-green-400' : 'text-slate-400'}`}>
                      {passwordStrength.requirements.number ? <FaCheck /> : <FaTimes />}
                      <span>Number</span>
                    </div>
                    <div className={`flex items-center gap-1 ${passwordStrength.requirements.special ? 'text-green-400' : 'text-slate-400'}`}>
                      {passwordStrength.requirements.special ? <FaCheck /> : <FaTimes />}
                      <span>Special character</span>
                    </div>
                  </div>
                </div>
              )}

              {errors.newPassword && (
                <p className="text-red-400 text-sm flex items-center gap-1">
                  ⚠️ {errors.newPassword}
                </p>
              )}
            </div>

            {/* Confirm New Password */}
            <div className="space-y-2">
              <label className="text-sm font-semibold text-orange-200 flex items-center gap-2">
                🔐 Confirm New Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords.confirm ? "text" : "password"}
                  placeholder="Confirm your new password..."
                  value={confirmNewPassword}
                  onChange={(e) => setConfirmNewPassword(e.target.value)}
                  className="w-full px-4 py-3 pr-12 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-orange-400/50 focus:ring-2 focus:ring-orange-400/20 focus:outline-none transition-all duration-300"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('confirm')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-orange-300 transition-colors duration-200"
                >
                  {showPasswords.confirm ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>

              {/* Password Match Indicator */}
              {confirmNewPassword && (
                <div className={`text-sm flex items-center gap-1 ${
                  newPassword === confirmNewPassword ? 'text-green-400' : 'text-red-400'
                }`}>
                  {newPassword === confirmNewPassword ? <FaCheck /> : <FaTimes />}
                  <span>
                    {newPassword === confirmNewPassword ? 'Passwords match' : 'Passwords do not match'}
                  </span>
                </div>
              )}

              {errors.confirmNewPassword && (
                <p className="text-red-400 text-sm flex items-center gap-1">
                  ⚠️ {errors.confirmNewPassword}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <div className="pt-6">
              <button
                type="submit"
                disabled={loading || passwordStrength.score < 3}
                className="w-full relative px-8 py-4 bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 font-bold text-lg tracking-wide disabled:opacity-50 disabled:cursor-not-allowed group/btn overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-red-700 via-orange-700 to-yellow-700 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10 flex items-center justify-center gap-2">
                  {loading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      Changing Password...
                    </>
                  ) : (
                    <>
                      <FaShieldAlt />
                      Change Password
                    </>
                  )}
                </span>
              </button>
            </div>
          </form>
        </div>

        {/* Security Tips */}
        <div className="mt-8 bg-gradient-to-br from-emerald-900/60 via-slate-800/80 to-teal-900/60 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-emerald-500/30 hover:border-emerald-400/50 transition-all duration-500">
          <h3 className="text-xl font-bold text-emerald-200 mb-4 flex items-center gap-2">
            🛡️ Security Tips
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-300">
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Use a unique password for each account</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Include uppercase, lowercase, numbers, and symbols</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Avoid using personal information</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Consider using a password manager</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
