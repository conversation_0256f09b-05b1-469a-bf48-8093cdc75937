import express from "express";
import {
  createLiveClass,
  getAllUpcomingClasses,
  getUpcomingClassesByCategory,
  getLiveClassById,
  updateLiveClass,
  deleteLiveClass,
  registerForLiveClass,
} from "../controllers/liveClassController.js";
import { isAuthenticated, authorizeRoles } from "../middleware/auth.js";

const router = express.Router();

// Public routes
router.get("/upcoming", getAllUpcomingClasses); // Get all upcoming classes
router.get("/upcoming/:category", getUpcomingClassesByCategory); // Get upcoming classes by category
router.get("/:id", getLiveClassById); // Get a class by ID

// Protected routes - only admin can create, update, delete classes
router.post("/", createLiveClass); // Create a class
router.put("/:id", updateLiveClass); // Update a class
router.delete("/:id", deleteLiveClass); // Delete a class

// User registration for a class
router.post("/:id/register", registerForLiveClass); // Register for a class

export default router;