import FAQ from "../models/FAQ.js";
import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import <PERSON>rror<PERSON>andler from "../utils/ErrorHandler.js";

// Get all FAQs or filter by category
export const getAllFAQs = CatchAsyncError(async (req, res, next) => {
  try {
    const { category } = req.query;
    
    const filter = { isActive: true };
    if (category) {
      filter.category = category;
    }
    
    const faqs = await FAQ.find(filter).sort({ order: 1, createdAt: 1 });
    
    res.status(200).json({
      success: true,
      data: faqs,
      count: faqs.length
    });
  } catch (error) {
    return next(new <PERSON>rror<PERSON><PERSON><PERSON>(error.message, 500));
  }
});

// Get FAQ by ID
export const getFAQById = CatchAsyncError(async (req, res, next) => {
  try {
    const faq = await FAQ.findById(req.params.id);
    
    if (!faq) {
      return next(new <PERSON>rror<PERSON>and<PERSON>("FAQ not found", 404));
    }
    
    res.status(200).json({
      success: true,
      data: faq
    });
  } catch (error) {
    return next(new <PERSON>rror<PERSON>and<PERSON>(error.message, 500));
  }
});

// Create new FAQ (Admin only)
export const createFAQ = CatchAsyncError(async (req, res, next) => {
  try {
    const { question, answer, category, order } = req.body;
    
    if (!question || !answer) {
      return next(new ErrorHandler("Question and answer are required", 400));
    }
    
    const faq = await FAQ.create({
      question,
      answer,
      category: category || "general",
      order: order || 0
    });
    
    res.status(201).json({
      success: true,
      message: "FAQ created successfully",
      data: faq
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 500));
  }
});

// Update FAQ (Admin only)
export const updateFAQ = CatchAsyncError(async (req, res, next) => {
  try {
    const faq = await FAQ.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!faq) {
      return next(new ErrorHandler("FAQ not found", 404));
    }
    
    res.status(200).json({
      success: true,
      message: "FAQ updated successfully",
      data: faq
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 500));
  }
});

// Delete FAQ (Admin only)
export const deleteFAQ = CatchAsyncError(async (req, res, next) => {
  try {
    const faq = await FAQ.findByIdAndDelete(req.params.id);
    
    if (!faq) {
      return next(new ErrorHandler("FAQ not found", 404));
    }
    
    res.status(200).json({
      success: true,
      message: "FAQ deleted successfully"
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 500));
  }
});

// Get FAQs by category
export const getFAQsByCategory = CatchAsyncError(async (req, res, next) => {
  try {
    const { category } = req.params;
    
    const faqs = await FAQ.find({ 
      category, 
      isActive: true 
    }).sort({ order: 1, createdAt: 1 });
    
    res.status(200).json({
      success: true,
      data: faqs,
      category,
      count: faqs.length
    });
  } catch (error) {
    return next(new ErrorHandler(error.message, 500));
  }
});
