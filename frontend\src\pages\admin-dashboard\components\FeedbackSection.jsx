import React, { useState } from 'react';
import { FaStar, <PERSON>a<PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>, FaR<PERSON>ly, FaTimes } from 'react-icons/fa';

const FeedbackSection = () => {
  // Mock feedback data
  const [feedbacks, setFeedbacks] = useState([
    { 
      id: 1, 
      student: '<PERSON>', 
      course: 'Python Master Course', 
      rating: 5, 
      comment: 'Excellent course! The instructor explained complex concepts in a simple way.',
      date: '2023-11-15'
    },
    { 
      id: 2, 
      student: '<PERSON>', 
      course: 'Full Stack Development', 
      rating: 4, 
      comment: 'Great content, but some sections could use more examples.',
      date: '2023-11-10'
    },
    { 
      id: 3, 
      student: '<PERSON>', 
      course: 'Data Science Fundamentals', 
      rating: 5, 
      comment: 'Very comprehensive. I learned a lot from this course.',
      date: '2023-11-05',
      reply: ''
    },
  ]);
  
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState('');

  // Render stars based on rating
  const renderStars = (rating) => {
    return Array(5).fill(0).map((_, i) => (
      <FaStar 
        key={i} 
        className={i < rating ? "text-yellow-500" : "text-gray-300"} 
      />
    ));
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Student Feedback</h2>
        <div className="flex items-center">
          <FaFilter className="mr-2 text-gray-500" />
          <select className="border rounded-md px-3 py-1 text-sm">
            <option>All Courses</option>
            <option>Python Master Course</option>
            <option>Full Stack Development</option>
            <option>Data Science Fundamentals</option>
          </select>
        </div>
      </div>

      {/* Feedback Summary */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-1">Overall Rating</h3>
            <div className="flex items-center">
              <div className="flex mr-2">
                {renderStars(4)}
              </div>
              <span className="text-2xl font-bold text-gray-800">4.7</span>
              <span className="text-sm text-gray-500 ml-2">out of 5</span>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Based on</p>
            <p className="text-2xl font-bold text-gray-800">128</p>
            <p className="text-sm text-gray-500">reviews</p>
          </div>
        </div>
      </div>

      {/* Feedback List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h3 className="font-semibold text-gray-700">Recent Feedback</h3>
        </div>
        <ul className="divide-y divide-gray-200">
          {feedbacks.map((feedback) => (
            <li key={feedback.id} className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium text-gray-800">{feedback.student}</p>
                  <p className="text-sm text-gray-500 mb-2">on {feedback.course}</p>
                </div>
                <p className="text-sm text-gray-500">{feedback.date}</p>
              </div>
              <div className="flex mb-2">
                {renderStars(feedback.rating)}
              </div>
              <div className="flex items-start">
                <FaComment className="text-gray-400 mt-1 mr-2" />
                <p className="text-gray-600">{feedback.comment}</p>
              </div>
              
              {feedback.reply && (
                <div className="ml-8 mt-3 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-start">
                    <FaReply className="text-blue-500 mt-1 mr-2 transform rotate-180" />
                    <div>
                      <p className="text-xs font-medium text-blue-600 mb-1">Admin Reply:</p>
                      <p className="text-gray-700">{feedback.reply}</p>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="mt-3 flex justify-end">
                {!feedback.reply ? (
                  <button 
                    onClick={() => setReplyingTo(feedback.id)}
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                  >
                    <FaReply className="mr-1" /> Reply
                  </button>
                ) : (
                  <button 
                    onClick={() => {
                      setReplyingTo(feedback.id);
                      setReplyText(feedback.reply);
                    }}
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                  >
                    <FaReply className="mr-1" /> Edit Reply
                  </button>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>
      
      {/* Reply Modal */}
      {replyingTo && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800">Reply to Feedback</h3>
              <button 
                onClick={() => {
                  setReplyingTo(null);
                  setReplyText('');
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            </div>
            
            <div className="p-6">
              <div className="mb-4 bg-gray-50 p-4 rounded-lg">
                <p className="font-medium text-gray-800">
                  {feedbacks.find(f => f.id === replyingTo)?.student}
                </p>
                <div className="flex my-1">
                  {renderStars(feedbacks.find(f => f.id === replyingTo)?.rating || 0)}
                </div>
                <p className="text-gray-600">
                  {feedbacks.find(f => f.id === replyingTo)?.comment}
                </p>
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="reply">
                  Your Reply
                </label>
                <textarea
                  id="reply"
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows="4"
                  placeholder="Type your response here..."
                  required
                ></textarea>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  className="bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2"
                  onClick={() => {
                    setReplyingTo(null);
                    setReplyText('');
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md"
                  onClick={() => {
                    setFeedbacks(feedbacks.map(feedback => 
                      feedback.id === replyingTo 
                        ? { ...feedback, reply: replyText } 
                        : feedback
                    ));
                    setReplyingTo(null);
                    setReplyText('');
                  }}
                >
                  Submit Reply
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FeedbackSection;