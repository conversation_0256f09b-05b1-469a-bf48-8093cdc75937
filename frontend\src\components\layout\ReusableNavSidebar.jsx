import ReusableNavbar from "./ReusableNavbar";
import ReusableSidebar from "./ReusableSidebar";

const ReusableNavSidebar = ({ 
  isSidebarOpen, 
  toggleSidebar, 
  title = "Upcoding", 
  children 
}) => {
  return (
    <>
      {/* Navbar */}
      <ReusableNavbar toggleSidebar={toggleSidebar} title={title} />
      
      {/* Sidebar */}
      <ReusableSidebar isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      
      {/* Main Content with dynamic margin */}
      <div
        className="transition-all duration-300 ease-out"
        style={{ 
          marginLeft: isSidebarOpen ? "280px" : "0px",
          paddingTop: "80px" // Account for fixed navbar height
        }}
      >
        {children}
      </div>
      
      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-10 lg:hidden"
          onClick={toggleSidebar}
        />
      )}
    </>
  );
};

export default ReusableNavSidebar;
