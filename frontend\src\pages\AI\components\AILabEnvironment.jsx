import React, { useState } from "react";

const AILabEnvironment = ({ showPremiumOverlay }) => {
  const [activeTab, setActiveTab] = useState("setup");

  const tabs = [
    { id: "setup", label: "Environment Setup", icon: "fas fa-cog" },
    { id: "frameworks", label: "AI Frameworks", icon: "fas fa-brain" },
    { id: "projects", label: "AI Projects", icon: "fas fa-robot" }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "setup":
        return <EnvironmentSetup />;
      case "frameworks":
        return <AIFrameworks showPremiumOverlay={showPremiumOverlay} />;
      case "projects":
        return <AIProjects showPremiumOverlay={showPremiumOverlay} />;
      default:
        return <EnvironmentSetup />;
    }
  };

  // Animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md overflow-hidden text-white">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1e293b]/20 to-[#0f172a]/20">
        <h2 className="text-3xl font-bold mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>AI Lab Environment</h2>
        <p className="text-blue-100/80">Set up your artificial intelligence development environment with cutting-edge frameworks and tools</p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-white/10 flex">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-colors duration-200 relative ${
              activeTab === tab.id
                ? "text-cyan-400 border-b-2 border-cyan-400"
                : "text-white/70 hover:text-white"
            }`}
          >
            <i className={`${tab.icon} mr-2`}></i>
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Content */}
      <div className="p-6 fade-in">
        {renderContent()}
      </div>
    </div>
  );
};

// Environment Setup Component
const EnvironmentSetup = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Setting Up Your AI Environment</h3>
      
      <div className="rounded-lg bg-[#303246]/30 backdrop-blur-sm p-6 border border-white/10">
        <h4 className="text-xl font-semibold mb-3 text-cyan-300">Why AI Environment Setup Matters</h4>
        <p className="text-white/80">
          A proper AI environment enables efficient model development, training, and deployment with access to GPU acceleration and specialized libraries.
        </p>
      </div>
      
      <div className="space-y-8 mt-6">
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-cyan-500/30 text-cyan-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">1</span>
            Python & AI Libraries
          </h4>
          <p className="text-white/80 mb-4">
            Install Python with essential AI libraries for machine learning, deep learning, and neural networks.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Install core AI libraries<br />
              pip install tensorflow keras pytorch<br />
              pip install transformers huggingface_hub<br />
              pip install opencv-python pillow
            </code>
          </div>
        </div>

        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-cyan-500/30 text-cyan-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">2</span>
            GPU Configuration
          </h4>
          <p className="text-white/80 mb-4">
            Configure CUDA and cuDNN for GPU-accelerated AI model training and inference.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Check GPU availability<br />
              nvidia-smi<br />
              # Install CUDA toolkit<br />
              conda install cudatoolkit cudnn
            </code>
          </div>
        </div>

        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-cyan-500/30 text-cyan-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">3</span>
            Development Environment
          </h4>
          <p className="text-white/80 mb-4">
            Set up Jupyter Lab and VS Code for interactive AI development and experimentation.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Install development tools<br />
              pip install jupyterlab<br />
              code --install-extension ms-python.python
            </code>
          </div>
        </div>
      </div>
    </div>
  );
};

// AI Frameworks Component
const AIFrameworks = ({ showPremiumOverlay }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white">Popular AI Frameworks</h3>
      
      <div className="grid md:grid-cols-2 gap-6">
        {[
          {
            title: "TensorFlow",
            description: "Google's open-source AI framework",
            features: ["Neural Networks", "Computer Vision", "NLP"],
            icon: "🔥"
          },
          {
            title: "PyTorch",
            description: "Facebook's dynamic neural network library",
            features: ["Dynamic Graphs", "Research Friendly", "GPU Support"],
            icon: "⚡"
          },
          {
            title: "Hugging Face",
            description: "Transformers and pre-trained models",
            features: ["Pre-trained Models", "NLP Tasks", "Model Hub"],
            icon: "🤗"
          },
          {
            title: "OpenAI API",
            description: "Access to GPT and other AI models",
            features: ["GPT Models", "DALL-E", "Codex"],
            icon: "🧠"
          }
        ].map((framework, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10 hover:border-cyan-400/50 transition-all duration-300">
            <div className="text-3xl mb-3">{framework.icon}</div>
            <h4 className="text-lg font-semibold text-white mb-2">{framework.title}</h4>
            <p className="text-white/80 mb-3">{framework.description}</p>
            <div className="flex flex-wrap gap-2 mb-4">
              {framework.features.map((feature, idx) => (
                <span key={idx} className="px-2 py-1 bg-cyan-500/20 text-cyan-300 rounded-full text-xs">
                  {feature}
                </span>
              ))}
            </div>
            <button
              onClick={showPremiumOverlay}
              className="w-full px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-lg hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 text-sm"
            >
              Learn Framework
            </button>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <button
          onClick={showPremiumOverlay}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <span className="mr-2">🚀</span>
          Master All Frameworks
          <span className="ml-2">→</span>
        </button>
      </div>
    </div>
  );
};

// AI Projects Component
const AIProjects = ({ showPremiumOverlay }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white">Hands-on AI Projects</h3>
      
      <div className="grid md:grid-cols-2 gap-6">
        {[
          {
            title: "Chatbot Development",
            description: "Build an intelligent conversational AI",
            difficulty: "Intermediate",
            tech: ["Python", "NLP", "Transformers"],
            icon: "💬"
          },
          {
            title: "Image Classification",
            description: "Train CNNs for image recognition",
            difficulty: "Beginner",
            tech: ["TensorFlow", "Computer Vision", "CNN"],
            icon: "📸"
          },
          {
            title: "Recommendation System",
            description: "Build personalized recommendation engine",
            difficulty: "Advanced",
            tech: ["Collaborative Filtering", "Deep Learning"],
            icon: "🎯"
          },
          {
            title: "Sentiment Analysis",
            description: "Analyze emotions in text data",
            difficulty: "Intermediate",
            tech: ["NLP", "BERT", "Text Processing"],
            icon: "😊"
          }
        ].map((project, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10 hover:border-cyan-400/50 transition-all duration-300">
            <div className="text-3xl mb-3">{project.icon}</div>
            <h4 className="text-lg font-semibold text-white mb-2">{project.title}</h4>
            <p className="text-white/80 mb-3">{project.description}</p>
            <div className="flex items-center justify-between mb-4">
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                project.difficulty === "Beginner" ? "bg-green-500/20 text-green-300" :
                project.difficulty === "Intermediate" ? "bg-yellow-500/20 text-yellow-300" :
                "bg-red-500/20 text-red-300"
              }`}>
                {project.difficulty}
              </span>
            </div>
            <div className="flex flex-wrap gap-1 mb-4">
              {project.tech.map((tech, idx) => (
                <span key={idx} className="px-2 py-1 bg-cyan-500/20 text-cyan-300 rounded text-xs">
                  {tech}
                </span>
              ))}
            </div>
            <button
              onClick={showPremiumOverlay}
              className="w-full px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-lg hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 text-sm"
            >
              Start Project
            </button>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <button
          onClick={showPremiumOverlay}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <span className="mr-2">🔧</span>
          Access All Projects
          <span className="ml-2">→</span>
        </button>
      </div>
    </div>
  );
};

export default AILabEnvironment;
