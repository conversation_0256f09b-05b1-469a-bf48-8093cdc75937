import { LoadingSpinner } from "../ui";
import HomepageLayout from "../layout/HomepageLayout";
import useLoadingState from "../../hooks/useLoadingState";
import useSidebarState from "../../hooks/useSidebarState";

const Home = () => {
  const loading = useLoadingState(1000);
  const { toggleSidebar } = useSidebarState(true);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <HomepageLayout 
      toggleSidebar={toggleSidebar} 
    />
  );
};

export default Home;
