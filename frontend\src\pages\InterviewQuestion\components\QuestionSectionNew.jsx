import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

const QuestionSectionNew = ({ interviewData, showPremiumOverlay }) => {
  const [openIndex, setOpenIndex] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [questionsPerPage] = useState(10);

  // Reset open answer when page changes
  useEffect(() => {
    setOpenIndex(null);
  }, [currentPage]);

  const toggleAnswer = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  
  // Get current questions
  const indexOfLastQuestion = currentPage * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = interviewData.slice(indexOfFirstQuestion, indexOfLastQuestion);
  
  // Calculate total pages
  const totalPages = Math.ceil(interviewData.length / questionsPerPage);
  
  // Go to specific page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  
  // Go to next page
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  // Go to previous page
  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
      },
    },
  };

  return (
    <section className="py-10" id="interview-questions">
      <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-lg border border-white/10 rounded-xl p-6 mb-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-[#303246]/60 backdrop-blur-lg text-blue-300 p-3 rounded-lg border border-white/10">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-white">
            Top Interview Questions
          </h2>
        </div>
        
        <p className="text-gray-300 mb-8">
          Master these frequently asked interview questions from top multinational companies. 
          Click on each question to reveal detailed answers and explanations.
        </p>

        <motion.ul 
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="divide-y divide-gray-700/30"
        >
          {currentQuestions.map((item, index) => (
            <motion.li key={index} variants={itemVariants} className="py-3">
              <div 
                onClick={() => toggleAnswer(index)} 
                className="flex justify-between items-center cursor-pointer py-3 hover:bg-white/5 px-3 rounded-lg transition-colors"
              >
                <div className="flex gap-3 items-center">
                  <span className="bg-[#303246]/60 backdrop-blur-lg text-gray-300 font-semibold rounded-full w-8 h-8 flex items-center justify-center border border-white/10">
                    {indexOfFirstQuestion + index + 1}
                  </span>
                  <h3 className="font-medium text-lg text-gray-200">{item.question}</h3>
                </div>
                <div className={`transition-transform duration-300 ${openIndex === index ? 'rotate-180' : ''}`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="bg-[#1e293b]/30 backdrop-blur-lg p-6 rounded-lg my-3 ml-10 border-l-4 border-blue-500/50">
                      <div className="prose prose-invert max-w-none text-gray-300">
                        {item.answer}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.li>
          ))}
        </motion.ul>
        
        {/* Premium CTA */}
        <div className="mt-8 p-6 bg-[#303246]/30 backdrop-blur-lg rounded-xl border border-white/10 text-center">
          <h3 className="text-xl font-bold text-white mb-2">Want access to all 60+ interview questions?</h3>
          <p className="text-gray-300 mb-4">Upgrade to premium for full access to all questions and detailed answers.</p>
          <button 
            onClick={showPremiumOverlay}
            className="bg-[#303246]/60 backdrop-blur-lg text-white px-6 py-3 rounded-xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-white/10 inline-flex items-center"
          >
            <span className="relative z-10">Get Premium Access</span>
            <span className="ml-2 relative z-10">🚀</span>
          </button>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="flex justify-center items-center mt-8 space-x-2"
          >
            {/* Previous Button */}
            <button
              onClick={prevPage}
              disabled={currentPage === 1}
              className={`px-4 py-2 rounded-md flex items-center gap-1 transition-colors
                ${currentPage === 1 
                  ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed' 
                  : 'bg-[#303246]/60 text-blue-300 hover:bg-[#303246]/80 border border-white/10'
                }`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Prev
            </button>
            
            {/* Page Numbers - with ellipsis for many pages */}
            <div className="flex items-center space-x-1">
              {totalPages <= 7 ? (
                // Show all page numbers if 7 or fewer
                Array.from({ length: totalPages }, (_, i) => (
                  <button
                    key={i}
                    onClick={() => paginate(i + 1)}
                    className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                      ${currentPage === i + 1
                        ? 'bg-blue-600/70 text-white border border-white/20' 
                        : 'bg-[#303246]/40 text-gray-300 hover:bg-[#303246]/60 border border-white/10'
                      }`}
                  >
                    {i + 1}
                  </button>
                ))
              ) : (
                // Show ellipsis for many pages
                <>
                  {/* First page always shown */}
                  <button
                    onClick={() => paginate(1)}
                    className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                      ${currentPage === 1
                        ? 'bg-blue-600/70 text-white border border-white/20' 
                        : 'bg-[#303246]/40 text-gray-300 hover:bg-[#303246]/60 border border-white/10'
                      }`}
                  >
                    1
                  </button>
                  
                  {/* Show ellipsis or second page */}
                  {currentPage > 3 && (
                    <span className="w-10 h-10 flex items-center justify-center text-gray-400">
                      ...
                    </span>
                  )}
                  
                  {/* Current page range */}
                  {Array.from(
                    { length: 3 },
                    (_, i) => {
                      const pageNum = currentPage > 3 
                        ? currentPage - 1 + i 
                        : 2 + i;
                      
                      return pageNum <= totalPages - 1 && pageNum > 1 && (
                        <button
                          key={pageNum}
                          onClick={() => paginate(pageNum)}
                          className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                            ${currentPage === pageNum
                              ? 'bg-blue-600/70 text-white border border-white/20' 
                              : 'bg-[#303246]/40 text-gray-300 hover:bg-[#303246]/60 border border-white/10'
                            }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                  )}
                  
                  {/* Show ellipsis or second-to-last page */}
                  {currentPage < totalPages - 2 && (
                    <span className="w-10 h-10 flex items-center justify-center text-gray-400">
                      ...
                    </span>
                  )}
                  
                  {/* Last page always shown */}
                  <button
                    onClick={() => paginate(totalPages)}
                    className={`w-10 h-10 rounded-md flex items-center justify-center transition-colors
                      ${currentPage === totalPages
                        ? 'bg-blue-600/70 text-white border border-white/20' 
                        : 'bg-[#303246]/40 text-gray-300 hover:bg-[#303246]/60 border border-white/10'
                      }`}
                  >
                    {totalPages}
                  </button>
                </>
              )}
            </div>
            
            {/* Page indicator */}
            <div className="text-gray-400 text-sm ml-2">
              <span className="hidden sm:inline">Page </span>
              {currentPage} of {totalPages}
            </div>
            
            {/* Next Button */}
            <button
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className={`px-4 py-2 rounded-md flex items-center gap-1 transition-colors
                ${currentPage === totalPages 
                  ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed' 
                  : 'bg-[#303246]/60 text-blue-300 hover:bg-[#303246]/80 border border-white/10'
                }`}
            >
              Next
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default QuestionSectionNew;