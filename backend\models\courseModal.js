import mongoose from "mongoose";

const courseSchema = new mongoose.Schema(
  {
    slug: {
      type: String,
      required: [true, "Course URL path is required"],
      unique: true,
    },

    description: {
      type: String,
      default: "",
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    order: {
      type: Number,
      default: 0,
    },
    subTopics: [
      {
        title: {
          type: String,
          required: [true, "Subtopic title is required"],
        },
        questions: [
          {
            question: {
              type: String,
              required: [true, "Question is required"],
            },
            answer: {
              type: String,
              required: [true, "Answer is required"],
            },
          },
        ],
      },
    ],
    materials: [
      {
        fileName: {
          type: String,
          required: true,
        },
        fileUrl: {
          type: String,
          required: true,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

const Course = mongoose.model("Course", courseSchema);
export default Course;
