import React, { useState } from "react";

const FullstackLabEnvironment = ({ showPremiumOverlay }) => {
  const [activeTab, setActiveTab] = useState("setup");

  const tabs = [
    { id: "setup", label: "Environment Setup", icon: "fas fa-cog" },
    { id: "exercises", label: "Practical Exercises", icon: "fas fa-laptop-code" },
    { id: "projects", label: "Projects", icon: "fas fa-project-diagram" }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "setup":
        return <EnvironmentSetup />;
      case "exercises":
        return <PracticalExercises showPremiumOverlay={showPremiumOverlay} />;
      case "projects":
        return <Projects showPremiumOverlay={showPremiumOverlay} />;
      default:
        return <EnvironmentSetup />;
    }
  };

  // Animation styles with glassmorphism
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md overflow-hidden text-white">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1e293b]/20 to-[#0f172a]/20">
        <h2 className="text-3xl font-bold mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Full Stack Lab Environment</h2>
        <p className="text-blue-100/80">Set up your development environment and build real-world full stack applications</p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-white/10 flex">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-colors duration-200 relative ${
              activeTab === tab.id
                ? "text-blue-400 border-b-2 border-blue-400"
                : "text-white/70 hover:text-white"
            }`}
          >
            <i className={`${tab.icon} mr-2`}></i>
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Content */}
      <div className="p-6 fade-in">
        {renderContent()}
      </div>
    </div>
  );
};

// Environment Setup Component
const EnvironmentSetup = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Setting Up Your Full Stack Development Environment</h3>
      
      <div className="rounded-lg bg-[#303246]/30 backdrop-blur-sm p-6 border border-white/10">
        <h4 className="text-xl font-semibold mb-3 text-blue-300">Why Environment Setup Matters</h4>
        <p className="text-white/80">
          A proper full stack development environment ensures consistency across frontend and backend development, makes package management easier, and streamlines your workflow.
        </p>
      </div>
      
      <div className="space-y-8 mt-6">
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold mb-3">
            <span className="bg-blue-500/30 text-blue-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">1</span>
            Node.js & npm Setup
          </h4>
          <p className="text-white/80 mb-4">
            Download and install the latest LTS version of Node.js, which includes npm (Node Package Manager).
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Verify Node.js installation<br />
              node --version<br /><br />
              # Verify npm installation<br />
              npm --version
            </code>
          </div>
        </div>

        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold mb-3">
            <span className="bg-blue-500/30 text-blue-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">2</span>
            MongoDB Setup
          </h4>
          <p className="text-white/80 mb-4">
            Install MongoDB for your database needs. Alternatively, you can use MongoDB Atlas for a cloud-based solution.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Start MongoDB on Windows<br />
              net start MongoDB<br /><br />
              # Start MongoDB on macOS/Linux<br />
              sudo systemctl start mongod
            </code>
          </div>
        </div>
        
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold mb-3">
            <span className="bg-blue-500/30 text-blue-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">3</span>
            Project Setup
          </h4>
          <p className="text-white/80 mb-4">
            Initialize a new MERN stack project with React for the frontend and Express for the backend.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Create React App for Frontend<br />
              npx create-react-app client<br /><br />
              # Initialize Backend<br />
              mkdir server<br />
              cd server<br />
              npm init -y<br />
              npm install express mongoose cors dotenv
            </code>
          </div>
        </div>
        
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold mb-3">
            <span className="bg-blue-500/30 text-blue-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">4</span>
            IDE Setup
          </h4>
          <p className="text-white/80 mb-4">
            Configure your IDE with extensions that enhance your full stack development experience.
          </p>
          <div className="flex flex-wrap gap-4 mt-3">
            <div className="flex items-center bg-[#1e293b]/30 p-3 rounded-md">
              <i className="fab fa-react text-blue-400 text-xl mr-3"></i>
              <span>ES7 React Snippets</span>
            </div>
            <div className="flex items-center bg-[#1e293b]/30 p-3 rounded-md">
              <i className="fas fa-server text-green-400 text-xl mr-3"></i>
              <span>REST Client</span>
            </div>
            <div className="flex items-center bg-[#1e293b]/30 p-3 rounded-md">
              <i className="fas fa-database text-yellow-400 text-xl mr-3"></i>
              <span>MongoDB for VS Code</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Practical Exercises Component
const PracticalExercises = ({ showPremiumOverlay }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Full Stack Practical Exercises</h3>
      
      <p className="text-white/80 mb-6">
        Apply your knowledge with these hands-on exercises that cover both frontend and backend development.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[
          {
            title: "User Authentication System",
            description: "Build a complete authentication system with JWT, user registration, and login functionality.",
            difficulty: "Intermediate",
            technologies: ["React", "Node.js", "Express", "MongoDB", "JWT"],
            isPremium: true,
          },
          {
            title: "REST API Development",
            description: "Create a RESTful API with CRUD operations for a resource of your choice.",
            difficulty: "Beginner",
            technologies: ["Node.js", "Express", "MongoDB"],
            isPremium: false,
          },
          {
            title: "Real-time Chat Application",
            description: "Develop a chat application with real-time messaging capabilities.",
            difficulty: "Advanced",
            technologies: ["React", "Node.js", "Socket.io", "Express"],
            isPremium: true,
          },
          {
            title: "E-commerce Product Page",
            description: "Build a responsive product page with filtering and sorting functionality.",
            difficulty: "Intermediate",
            technologies: ["React", "CSS/SCSS", "Context API"],
            isPremium: false,
          },
        ].map((exercise, index) => (
          <div 
            key={index}
            className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 border border-white/10 relative overflow-hidden"
          >
            {exercise.isPremium && (
              <div className="absolute top-0 right-0">
                <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-xs px-3 py-1 transform rotate-45 translate-x-2 -translate-y-1 shadow-lg">
                  Premium
                </div>
              </div>
            )}
            
            <h4 className="text-xl font-semibold mb-2 text-white">{exercise.title}</h4>
            <p className="text-white/70 mb-3">{exercise.description}</p>
            
            <div className="flex items-center mb-4">
              <span className="text-xs text-blue-300 mr-2">Difficulty:</span>
              <span className={`text-xs font-medium px-2 py-1 rounded ${
                exercise.difficulty === "Beginner" ? "bg-green-500/20 text-green-300" :
                exercise.difficulty === "Intermediate" ? "bg-yellow-500/20 text-yellow-300" :
                "bg-red-500/20 text-red-300"
              }`}>
                {exercise.difficulty}
              </span>
            </div>
            
            <div className="flex flex-wrap gap-2 mb-4">
              {exercise.technologies.map((tech, i) => (
                <span key={i} className="bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded">
                  {tech}
                </span>
              ))}
            </div>
            
            <button 
              onClick={exercise.isPremium ? showPremiumOverlay : () => {}}
              className={`mt-2 w-full py-2 px-4 rounded text-sm font-medium ${
                exercise.isPremium ?
                "bg-gradient-to-r from-yellow-600/50 to-yellow-700/50 text-white hover:from-yellow-600/70 hover:to-yellow-700/70" :
                "bg-gradient-to-r from-blue-600/50 to-blue-700/50 text-white hover:from-blue-600/70 hover:to-blue-700/70"
              } transition-all duration-200`}
            >
              {exercise.isPremium ? "Unlock Premium Exercise" : "Start Exercise"}
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

// Projects Component
const Projects = ({ showPremiumOverlay }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Full Stack Projects</h3>
      
      <p className="text-white/80 mb-6">
        Build complete, production-ready applications that showcase your full stack development skills.
      </p>
      
      <div className="space-y-8">
        {[
          {
            title: "Social Media Dashboard",
            description: "A comprehensive social media management dashboard with analytics, scheduling, and content management.",
            features: ["User authentication", "Analytics charts", "Post scheduling", "Content management"],
            technologies: ["React", "Redux", "Node.js", "Express", "MongoDB", "Chart.js"],
            duration: "4 weeks",
            isPremium: true,
            image: "dashboard.svg",
          },
          {
            title: "E-commerce Platform",
            description: "A complete online shopping platform with product listings, cart functionality, payment processing, and order management.",
            features: ["Product catalog", "Shopping cart", "Checkout process", "Order tracking"],
            technologies: ["React", "Context API", "Node.js", "Express", "MongoDB", "Stripe API"],
            duration: "6 weeks",
            isPremium: false,
            image: "shop.svg",
          },
          {
            title: "Task Management System",
            description: "A collaborative task management application with real-time updates, team assignments, and progress tracking.",
            features: ["Task creation and assignment", "Real-time updates", "Team collaboration", "Progress reporting"],
            technologies: ["React", "Socket.io", "Node.js", "Express", "MongoDB"],
            duration: "3 weeks",
            isPremium: true,
            image: "tasks.svg",
          },
        ].map((project, index) => (
          <div 
            key={index}
            className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-xl p-6 border border-white/10 relative"
          >
            <div className="flex flex-col md:flex-row gap-6">
              <div className="md:w-1/3">
                <div className="bg-gradient-to-br from-[#1e293b]/30 to-[#0f172a]/30 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center h-48 border border-white/10">
                  <img src={`/public/images/${project.image}`} alt={project.title} className="max-h-32 opacity-70" />
                </div>
              </div>
              
              <div className="md:w-2/3">
                <div className="flex justify-between items-start mb-3">
                  <h4 className="text-2xl font-bold text-white">{project.title}</h4>
                  {project.isPremium && (
                    <span className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-xs text-white px-3 py-1 rounded-full">
                      Premium
                    </span>
                  )}
                </div>
                
                <p className="text-white/70 mb-4">{project.description}</p>
                
                <div className="mb-4">
                  <h5 className="text-sm text-blue-300 mb-2">Key Features:</h5>
                  <ul className="grid grid-cols-2 gap-2">
                    {project.features.map((feature, i) => (
                      <li key={i} className="text-white/70 text-sm flex items-center">
                        <span className="text-green-400 mr-2">✓</span> {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="mb-4">
                  <h5 className="text-sm text-blue-300 mb-2">Technologies:</h5>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, i) => (
                      <span key={i} className="bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">
                    <i className="far fa-clock mr-1"></i> {project.duration}
                  </span>
                  
                  <button 
                    onClick={project.isPremium ? showPremiumOverlay : () => {}}
                    className={`py-2 px-6 rounded text-sm font-medium ${
                      project.isPremium ?
                      "bg-gradient-to-r from-yellow-600/50 to-yellow-700/50 text-white hover:from-yellow-600/70 hover:to-yellow-700/70" :
                      "bg-gradient-to-r from-blue-600/50 to-blue-700/50 text-white hover:from-blue-600/70 hover:to-blue-700/70"
                    } transition-all duration-200`}
                  >
                    {project.isPremium ? "Unlock Premium Project" : "Start Project"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FullstackLabEnvironment;
