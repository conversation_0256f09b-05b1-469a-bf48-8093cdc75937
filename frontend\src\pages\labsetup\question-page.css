*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.editor-container {
    background-color: #282c34;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
}

.result-container {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    color: #333;
}

.container {
    margin-top: 50px;
}

.description-section {
    animation: fadeInLeft 1s;
}

.code-section {
    animation: fadeInRight 1s;
}

.colorful-output {
    font-weight: bold;
}

.output-success {
    color: black;
}

.output-error {
    color: red;
}

.spacer {
    margin-top: 5rem;
}


@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@media (max-width: 767px) {
    .display-4 {
        font-size: 1.2rem;
        margin-bottom: 5rem;
    }
    .spacer {
        margin-top: 5rem;
    }
}

@media (min-width: 768px) {
    .display-4 {
        font-size: 2rem;
        margin-bottom: 5rem;
    }
    .spacer {
        margin-top: 5rem;
    }
}
@media (min-width: 1024px) {
    .display-4 {
        font-size: 2.5rem;
        margin-bottom: 5rem;
    }
    .spacer {
        margin-top: 5rem;
    }
}


.editor-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    background: #1e1e1e; 
    border-radius: 10px; 
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); 
    animation: fadeIn 0.5s ease-out;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 2px solid #333;
    opacity: 0;
    animation: slideIn 0.5s ease-out forwards 0.3s;
}

.language-selector {
    padding: 10px 15px;
    background: #333;
    color: #fff;
    font-size: 14px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.language-selector:hover {
    background: #444;
}

.language-selector:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 102, 255, 0.5);
}

.run-button {
    padding: 12px 20px;
    background: linear-gradient(45deg, #0066ff, #0044cc); 
    color: white;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    outline: none;
}

.run-button:hover {
    background: linear-gradient(45deg, #0044cc, #0066ff);
    transform: translateY(-3px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.run-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.5);
}

.editor-container {
    position: relative;
    width: 100%;
    background-color: #1e1e1e;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.output-section {
    background: #151515;
    color: #00ff00;
    padding: 15px;
    border-radius: 10px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    height: 200px;
    animation: fadeIn 1s ease-out;
}

.output-section pre {
    margin: 0;
    padding: 0;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.timer {
    display: flex;
    align-items: center; 
    justify-content: center; 
    gap: 10px;
    font-size: 16px;
    font-weight: bold;
    color: #20c997;
    cursor: pointer;
    flex-wrap: wrap; 
    text-align: center; 
}


.output-section:hover {
    box-shadow: inset 0 2px 10px rgba(143, 145, 143, 0.3);
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .editor-section {
        padding: 15px;
    }

    .editor-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .run-button {
        width: 100%;
        font-size: 14px;
    }
}

.output-section {
    background: #1e1e1e; 
    color: #dcdcdc; 
    padding: 15px;
    border-radius: 10px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.3);
    overflow-y: auto;
  }
  
  .output-section h3 {
    color: #fff;
    margin-bottom: 20px;
  }
  
  .output-testcase {
    margin-bottom: 15px;
  }
  
  .output-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
  }
  
  .expected-output, .your-output {
    flex: 1;
  }
  
  .expected-output h5, .your-output h5 {
    font-size: 16px;
    color: #fff;
    margin-bottom: 5px;
  }
  
  pre {
    background-color: #2d2d2d;
    padding: 10px;
    border-radius: 5px;
    word-wrap: break-word;
    white-space: pre-wrap;
    color: #dcdcdc;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
  }
  
  @media (max-width: 768px) {
    .output-row {
      flex-direction: column;
    }
  }
  

