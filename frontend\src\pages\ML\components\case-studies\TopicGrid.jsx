import React from 'react';
import { FaLayerGroup, FaProjectDiagram, FaStream, FaCode, FaCube, FaThLarge, FaFileAlt, FaKey, FaObjectGroup, FaSyncAlt } from 'react-icons/fa';

const TopicGrid = ({ topics, scrollToSection }) => {
  // Map topic icons to React Icons with colors
  const getTopicIcon = (iconName, index) => {
    const colors = [
      'text-blue-400',
      'text-purple-400',
      'text-green-400',
      'text-yellow-400',
      'text-red-400',
      'text-indigo-400',
      'text-pink-400',
      'text-orange-400',
      'text-teal-400',
      'text-cyan-400'
    ];
    
    const color = colors[index % colors.length];
    
    switch(iconName) {
      case 'fas fa-layer-group': return <FaLayerGroup className={`text-2xl ${color}`} />;
      case 'fas fa-project-diagram': return <FaProjectDiagram className={`text-2xl ${color}`} />;
      case 'fas fa-stream': return <FaStream className={`text-2xl ${color}`} />;
      case 'fas fa-code': return <FaCode className={`text-2xl ${color}`} />;
      case 'fas fa-cube': return <FaCube className={`text-2xl ${color}`} />;
      case 'fas fa-th-large': return <FaThLarge className={`text-2xl ${color}`} />;
      case 'fas fa-file-alt': return <FaFileAlt className={`text-2xl ${color}`} />;
      case 'fas fa-key': return <FaKey className={`text-2xl ${color}`} />;
      case 'fas fa-object-group': return <FaObjectGroup className={`text-2xl ${color}`} />;
      case 'fas fa-sync-alt': return <FaSyncAlt className={`text-2xl ${color}`} />;
      default: return <FaCode className={`text-2xl ${color}`} />;
    }
  };

  return (
    <div className="p-4 bg-blue-900/20 backdrop-blur-sm rounded-lg border border-blue-700/50">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {topics.map((topic, index) => (
          <div
            key={index}
            className="bg-blue-900/30 hover:bg-blue-800/40 border border-blue-700/50 hover:border-blue-600/70 rounded-lg p-4 flex flex-col items-center justify-center text-center cursor-pointer transition-all duration-300 transform hover:scale-105"
            onClick={() => scrollToSection(topic.sectionId)}
          >
            <div className="w-12 h-12 rounded-full bg-blue-900/50 flex items-center justify-center mb-3">
              {getTopicIcon(topic.icon, index)}
            </div>
            <div className="text-white text-sm font-medium">{topic.title}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TopicGrid;