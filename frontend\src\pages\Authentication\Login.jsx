import { Link, useNavigate } from "react-router-dom";
import { useGoogleLogin } from "@react-oauth/google";
import { googleAuth } from "./api";
import { useState } from "react";
import toast from "react-hot-toast";
import axios from "axios";
import { Spinner } from "../../components/ui";

const Login = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const responseGoogle = async (authResult) => {
    try {
      if (authResult["code"]) {
        const result = await googleAuth(authResult["code"]);
        const { name, image } = result.data.user;
        const token = result.data.token;

        if (token) {
          localStorage.setItem("user", JSON.stringify({ name, image }));
          localStorage.setItem("token", token);

          navigate("/");
        }
      }
    } catch (error) {
      console.log(error);
    }
  };
  const googleLogin = useGoogleLogin({
    onSuccess: responseGoogle,
    onError: responseGoogle,
    flow: "auth-code",
  });

  const validateFields = () => {
    let newErrors = {};

    if (!email) newErrors.email = "Email is required";
    if (!password) newErrors.password = "Password is required";
    setErrors(newErrors);
    setTimeout(() => {
      setErrors({});
    }, 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;

    setLoading(true);

    try {
      const { data } = await axios.post(
        "http://localhost:8000/api/v1/auth/login",
        { email, password },
        { withCredentials: true }
      );
      console.log(data);
      if (data.success) {
        if (data.requiresTwoFA) {
          toast.success(
            "OTP sent to your email. Please verify to complete login."
          );
          navigate("/two-fa-otp");
        } else {
          toast.success(data.message);
          navigate("/two-fa");
        }
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error("Login Error:", error);
      toast.error("Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="login-container">
        <div className="signup-box">
          <h2
            style={{
              marginBottom: "20px",
              fontSize: "26px",
              fontWeight: "700",
            }}
          >
            Login Page
          </h2>
          <input
            type="email"
            placeholder="Email"
            className="input-field"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          {errors.email && <p className="error-message">{errors.email}</p>}
          <input
            type="password"
            placeholder="Password"
            className="input-field"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          {errors.password && (
            <p className="error-message">{errors.password}</p>
          )}
          <button
            className="btn-primary"
            style={{ marginBottom: "20px" }}
            onClick={handleLogin}
          >
            {loading ? <Spinner /> : "Log In"}
          </button>
          <div
            className="forgot-checks"
            style={{ marginBottom: "15px", paddingInline: "15px" }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <input type="checkbox" />
              <label htmlFor="">Stay Sign In</label>
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <Link
                style={{ textDecoration: "none", listStyle: "none" }}
                to={"/forgot-password"}
              >
                Forgot Password ?
              </Link>
            </div>
          </div>
          <button className="btn-social" onClick={googleLogin}>
            <img
              src="/images/google.jpg"
              alt="Google"
              className="google-logo"
            />
            Sign In With Google
          </button>
          <p className="signin-text">
            {"Don't"} have an account?{" "}
            <Link to={"/Sign-Up"} className="signin-link">
              Sign Up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
