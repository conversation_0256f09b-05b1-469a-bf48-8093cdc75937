import { useState } from "react";

const useDSAData = () => {
  const [dsaData] = useState([
    {
      level: "Beginner Level",
      icon: "fas fa-leaf",
      badgeClass: "difficulty-easy",
      difficulty: "Easy",
      questions: [
        {
          title: "Implement Stack Using Arrays",
          tags: ["Data Structures", "Stack", "Arrays"],
          keyPoints: [
            "Basic stack operations (push, pop)",
            "Handle stack overflow and underflow",
          ],
        },
        {
          title: "Implement Queue Using Linked List",
          tags: ["Data Structures", "Queue", "Linked List"],
          keyPoints: [
            "Basic queue operations (enqueue, dequeue)",
            "Handling null pointers and queue overflow",
          ],
        },
      ],
    },
    {
      level: "Intermediate Level",
      icon: "fas fa-fire",
      badgeClass: "difficulty-medium",
      difficulty: "Medium",
      questions: [
        {
          title: "Binary Search Tree Operations",
          tags: ["Trees", "Binary Search Tree", "In-order Traversal"],
          keyPoints: [
            "Implement search, insert, delete",
            "Tree balancing concepts",
          ],
        },
        {
          title: "Sorting Algorithms Comparison",
          tags: ["Algorithms", "Sorting", "Time Complexity"],
          keyPoints: [
            "Implement and compare QuickSort, MergeSort",
            "Analyze time and space complexity",
          ],
        },
      ],
    },
    {
      level: "Advanced Level",
      icon: "fas fa-bolt",
      badgeClass: "difficulty-hard",
      difficulty: "Hard",
      questions: [
        {
          title: "Graph Traversal: Dijkstra's Algorithm",
          tags: ["Graph", "Dijkstra's Algorithm", "Shortest Path"],
          keyPoints: [
            "Implement Dijkstra's algorithm",
            "Understand priority queues in pathfinding",
          ],
        },
        {
          title: "Dynamic Programming: Knapsack Problem",
          tags: ["Dynamic Programming", "Optimization", "Recursive Algorithms"],
          keyPoints: [
            "Implement recursive and memoized solution",
            "Optimize using bottom-up approach",
          ],
        },
      ],
    },
  ]);

  const [interviewChecklist] = useState([
    {
      question: "Implement a Linked List",
      answer:
        "A linked list has nodes that contain data and a reference to the next node in the sequence.",
    },
    {
      question: "Implement Depth-First Search (DFS)",
      answer:
        "Use a stack (or recursion) to explore as far as possible along each branch before backtracking.",
    },
    {
      question: "Implement Breadth-First Search (BFS)",
      answer:
        "Use a queue to explore all neighbors at the present depth prior to moving on to nodes at the next depth level.",
    },
    {
      question: "Find the Lowest Common Ancestor in a Binary Tree",
      answer:
        "Use recursion to traverse the tree and check if the nodes match the target; backtrack to find the ancestor.",
    },
    {
      question: "Implement a Binary Search",
      answer:
        "Check the middle of the sorted array; if it's the target, return it, otherwise continue in the left or right half.",
    },
    {
      question: "Detect a Cycle in a Linked List",
      answer:
        "Use two pointers, a slow and a fast one; if they meet, a cycle exists.",
    },
    {
      question: "Implement Quick Sort",
      answer:
        "Choose a pivot, partition the array into elements less than and greater than the pivot, and sort each partition recursively.",
    },
    {
      question: "Implement Merge Sort",
      answer:
        "Divide the array into halves, sort each half, and merge the sorted halves.",
    },
    {
      question: "Check if a String is a Palindrome",
      answer:
        "Compare characters from the start and end of the string, moving toward the center.",
    },
    {
      question: "Implement a Stack Using Queues",
      answer:
        "Use two queues and ensure the stack behavior (LIFO) by managing the push and pop operations accordingly.",
    },
    {
      question: "Find the Kth Largest Element in an Array",
      answer:
        "Use a min-heap of size k to track the largest elements; the smallest item in the heap is the Kth largest.",
    },
    {
      question: "Reverse a Linked List",
      answer:
        "Traverse the list and reverse the pointers in each node until reaching the end of the list.",
    },
    {
      question: "Implement Dijkstra's Shortest Path Algorithm",
      answer:
        "Use a priority queue to select the shortest path incrementally, updating the shortest path to each node.",
    },
    {
      question:
        "Determine if a Binary Tree is a Valid Binary Search Tree (BST)",
      answer:
        "Use recursion to check if each node's value falls between the allowed min and max values for a BST.",
    },
    {
      question: "Find the Longest Common Subsequence",
      answer:
        "Use dynamic programming to compare characters of two sequences, storing intermediate results to avoid recomputation.",
    },
  ]);

  return {
    dsaData,
    interviewChecklist,
  };
};

export default useDSAData;
