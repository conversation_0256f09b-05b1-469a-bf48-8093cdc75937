import "./profile.css";
import ProfileContent from "./ProfileContent";
import Sidebar from "./Sidebar";
import CrossIcon from "./CrossIcon";
import { useEffect, useState } from "react";
import axiosInstance from "../../utils/axiosInstance";
import { userLoggedIn } from "../../features/authSlice";
import { useDispatch} from "react-redux";
import { Routes, Route, Link } from "react-router-dom";
import { motion } from "framer-motion";
import { FiMenu, FiX, FiSun, FiMoon } from "react-icons/fi";
import { FaUser } from "react-icons/fa";

import { TbCashRegister } from "react-icons/tb";
import { MdOutlinePayment } from "react-icons/md";
import { IoMdAnalytics } from "react-icons/io";
import { IoIosNotifications } from "react-icons/io";
import { FaFileInvoice } from "react-icons/fa";
import MyProfilePage from "./MyProfilePage";
import EditProfilePage from "./EditProfilePage";
import SocialAccount from "./SocialAccount";
import ChangePassword from "./ChangePassword";
import ProjectStatus from "./ProjectStatus";
import DeleteAccount from "./DeleteAccount";


const MyProfile = () => {

  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const dispatch = useDispatch();

  const handleCloseSidebar = () => {
    setIsSidebarOpen((prev) => !prev);
  };

  const closeSidebarOnClick = () => {
    setIsSidebarOpen(false);
  };

  useEffect(() => {
    const checkMobile = () => {
      if (window.innerWidth <= 768) {
        setIsMobile(true);
        setIsSidebarOpen(false);
      } else {
        setIsMobile(false);
        setIsSidebarOpen(true); 
      }
    };

    checkMobile();

    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);



  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { data } = await axiosInstance.get(
          "http://localhost:8000/api/v1/auth/me",
          { withCredentials: true }
        );
        dispatch(
          userLoggedIn({
            accessToken: data.accessToken,
            user: data.user,
          })
        );
      } catch (error) {
        console.error(
          "Error fetching user:",
          error.response?.data || error.message
        );
      }
    };

    fetchUser();
  }, [dispatch]);
  


   const [darkMode, setDarkMode] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={`${darkMode ? "dark" : ""} flex min-h-screen flex-col`}>
      <div className="flex flex-1">
        {/* Sidebar */}
        <motion.aside
          className="bg-gray-900 text-white w-52 p-4 space-y-6 fixed md:relative inset-y-0 transform transition-transform duration-300 ease-in-out mt-14 md:mt-0"
          animate={{ x: isSidebarOpen ? 0 : isMobile ? -260 : 0 }}
          style={{marginTop:isMobile ? "80px":"0px"}}
        >
          <nav className={`space-y-4 ${isMobile ? "mt-[0px]" : "mt-[70px]"}`}>
            <Link
              to="/profile/my-profile"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <FaUser className="mr-3" /> My profile
            </Link>
           

            <Link
              to="/profile/edit-profile"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment className="mr-3" /> Edit profile
            </Link>
            <Link
              to="/profile/social-accounts"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment className="mr-3" /> Social Accounts
            </Link>
            <Link
              to="/profile/change-password"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment className="mr-3" /> Change Password
            </Link>
            <Link
              to="/profile/project-status"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <IoMdAnalytics className="mr-3" /> Project Status
            </Link>
            <Link
              to="/profile/delete-account"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <FaFileInvoice className="mr-3" /> Delete Account
            </Link>
            {/* <Link
              to="/admin/notifications"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >-
              <IoIosNotifications className="mr-3" /> Notifications
            </Link> */}
          </nav>
        </motion.aside>

        {/* Content Area */}
        <div
          className={`flex-1 p-6 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white transition-all duration-300 min-h-screen w-full`}
          style={{
            paddingTop: isMobile ? "40px" : "100px",
            width: "100%",
            minHeight: "100vh",
            overflow: "auto",
          }}
        >
           <Routes>
            <Route path="my-profile" element={<MyProfilePage />} />
            <Route path="edit-profile" element={<EditProfilePage />} />
            <Route path="social-accounts" element={<SocialAccount />} />
            <Route path="change-password" element={<ChangePassword />} />
            <Route path="project-status" element={<ProjectStatus />} />
            <Route path="delete-account" element={<DeleteAccount />} />
            
          </Routes>



        </div>
      </div>
    </div>
  );
};

export default MyProfile;