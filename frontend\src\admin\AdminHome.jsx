import { useState, useEffect } from "react";
import { Routes, Route, Link } from "react-router-dom";
import { motion } from "framer-motion";
import { FiMenu, FiX, FiSun, FiMoon } from "react-icons/fi";
import { FaUser } from "react-icons/fa";
import Lab from "./Lab/Lab";
import User from "./user/User";
import PaymentInformation from "./Payment-Info/PaymentInformation";
import { TbCashRegister } from "react-icons/tb";
import { MdOutlinePayment } from "react-icons/md";
import { IoMdAnalytics } from "react-icons/io";
import { IoIosNotifications } from "react-icons/io";
import { FaFileInvoice } from "react-icons/fa";
import AnalyticsReport from "./AnalyticsReport/AnalyticsReport";
import Enrollment from "./Enrollment/Enrollment";
import Internship from "./Registration/Internship";
import Project from "./Registration/Project";

const AdminHome = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsSidebarOpen(true);
      } else {
        setIsSidebarOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className={`${darkMode ? "dark" : ""} flex min-h-screen flex-col`}>
      <nav className="bg-white dark:bg-gray-900 shadow-md fixed top-0 w-full z-50 p-4 flex justify-between items-center md:fixed md:w-full md:z-10">
        {isMobile ? (
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="text-gray-800 dark:text-white p-2 rounded hover:bg-gray-100 "
          >
            {isSidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}
          </button>
        ) : (
          <h2 className="text-xl font-bold">Admin Dashboard</h2>
        )}

        {/* Dark Mode Toggle */}
        <button
          onClick={() => setDarkMode(!darkMode)}
          className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-800 dark:text-white"
        >
          {darkMode ? <FiSun size={24} /> : <FiMoon size={24} />}
        </button>
      </nav>

      <div className="flex flex-1">
        {/* Sidebar */}
        <motion.aside
          className="bg-white text-gray-900 w-56 p-4 space-y-6 fixed md:relative inset-y-0 border-r border-gray-300 shadow-lg transform transition-transform duration-300 ease-in-out mt-14 md:mt-0"
          animate={{ x: isSidebarOpen ? 0 : isMobile ? -260 : 0 }}
          style={{ marginTop: isMobile ? "80px" : "0px" }}
        >
          <nav className={`space-y-4 ${isMobile ? "mt-[0px]" : "mt-[70px]"}`}>
            <Link
              to="/admin/user"
              className="flex items-center gap-2 p-3 rounded hover:bg-gray-100 "
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <FaUser size={20} className="text-gray-600 dark:text-gray-300 " />
              User Management
            </Link>
            <li className="relative list-none">
              {/* Registration Button */}
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center gap-2 p-2 rounded hover:bg-gray-100 w-full"
              >
                <TbCashRegister
                  size={20}
                  className="text-gray-600 dark:text-gray-300 "
                />
                Registration
              </button>

              {/* Dropdown */}
              {isOpen && (
                <ul className="absolute left-[-15px] mt-2 w-48 bg-white text-gray-900 border border-gray-200 text-white rounded shadow-lg">
                  <li>
                    <Link
                      to="/admin/internship"
                      className="block px-4 py-2 text-gray-900 hover:bg-gray-100 "
                      onClick={() => setIsOpen(false)}
                    >
                      Internship
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/admin/project"
                      className="block px-4 text-gray-900 py-2 hover:bg-gray-100"
                      onClick={() => setIsOpen(false)}
                    >
                      Project
                    </Link>
                  </li>
                </ul>
              )}
            </li>
            <Link
              to="/admin/payment-info"
              className="flex items-center gap-2 p-2 rounded hover:bg-gray-100"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment
                size={20}
                className="text-gray-600 dark:text-gray-300 "
              />
              Payment Information
            </Link>
            <Link
              to="/admin/code-execution"
              className="flex items-center gap-2 p-2 rounded hover:bg-gray-100"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment
                size={20}
                className="text-gray-600 dark:text-gray-300 "
              />
              Code Execution
            </Link>
            <Link
              to="/admin/analytics-report"
              className="flex items-center gap-2 p-2 rounded hover:bg-gray-100"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <IoMdAnalytics className="mr-3" /> Analytics & Reports
            </Link>
            <Link
              to="/admin/enrollment"
              className="flex items-center gap-2 p-2 rounded hover:bg-gray-100"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <FaFileInvoice
                size={20}
                className="text-gray-600 dark:text-gray-300 "
              />{" "}
              Enrollment
            </Link>
            <Link
              to="/admin/notifications"
              className="flex items-center gap-2 p-2 rounded hover:bg-gray-100"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <IoIosNotifications
                size={20}
                className="text-gray-600 dark:text-gray-300 "
              />
              Notifications
            </Link>
          </nav>
        </motion.aside>

        {/* Content Area */}
        <div
          className={`flex-1 p-6 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white transition-all duration-300`}
          style={{
            paddingTop: isMobile ? "40px" : "100px",
            width: "calc(100% - 200px)",
            height: "100vh",
            overflow: "auto",
          }}
        >
          <Routes>
            <Route path="user" element={<User />} />
            <Route path="payment-info" element={<PaymentInformation />} />
            <Route path="code-execution" element={<Lab />} />
            <Route path="analytics-report" element={<AnalyticsReport />} />
            <Route path="enrollment" element={<Enrollment />} />
            <Route path="notifications" element={<Enrollment />} />
            <Route path="internship" element={<Internship />} />
            <Route path="project" element={<Project />} />
          </Routes>
        </div>
      </div>
    </div>
  );
};

export default AdminHome;
