import mongoose from "mongoose";

const faqSchema = new mongoose.Schema(
  {
    question: { 
      type: String, 
      required: true,
      trim: true
    },
    answer: { 
      type: String, 
      required: true,
      trim: true
    },
    category: { 
      type: String, 
      required: true,
      enum: ["python", "ml", "ai", "fullstack", "datascience", "general"],
      default: "general"
    },
    isActive: { 
      type: Boolean, 
      default: true 
    },
    order: { 
      type: Number, 
      default: 0 
    }
  },
  { 
    timestamps: true 
  }
);

// Index for better query performance
faqSchema.index({ category: 1, isActive: 1, order: 1 });

const FAQ = mongoose.model("FAQ", faqSchema);

export default FAQ;
