import { Spinner } from "../../components/ui";
import toast from "react-hot-toast";
import { useState } from "react";
import axiosInstance from "../../utils/axiosInstance";

const TwoFactorEmail = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const validateFields = () => {
    if (!email) {
      toast.error("Please Enter Email");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateFields()) return;
    setLoading(true);
    try {
      const { data } = await axiosInstance.post(
        "http://localhost:8000/api/v1/auth/two-fa-token",
        { email },
        { withCredentials: true }
      );
      if (data.success) {
        toast.success(data.message);
        setEmail("");
      } else {
        toast.error(data.error);
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div>
      <div className="forgot-password-container">
        <div className="form-container-forgot">
          <h2>Two Factor Authentication</h2>
          <p>
          Enter your email address to receive a verification link for enabling 2FA.
          </p>

          <form onSubmit={handleSubmit}>
            <input
              type="email"
              className="email-input-forgot"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            
            <button
              type="submit"
              className="submit-btn-forgot"
              disabled={loading}
            >
              {loading ? <Spinner /> : "Submit"}
            </button>
         
          </form>
        </div>
      </div>
    </div>
  );
};

export default TwoFactorEmail;
