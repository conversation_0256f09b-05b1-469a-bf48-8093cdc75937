import { Link } from "react-router-dom";
import "./two-factor.css";

const Force2FA = () => {
  return (
    <div className="two-fa-container">
      <div className="two-fa-card">
        <img
          src="/images/logoG copy.png"
          alt="Company Logo"
          className="two-fa-logo"
        />

        <h1 className="two-fa-main-heading">
          Protect Your Account with 2-Step Verification
        </h1>

        <p className="two-fa-sub-heading">
          Your company prioritizes security. Enable two-factor authentication
          (2FA) to add an extra layer of protection to your account.
        </p>

        <ul className="two-fa-benefits-list">
          <li>Prevent unauthorized access</li>
          <li>Secure your sensitive information</li>
          <li>Ensure only you can log in</li>
        </ul>

        <div className="enable-two-fa">
          <div>
            <Link to={"/two-fa-email"} style={{ textDecoration: "none" }}>
              <button className="btn-primary">
                Enable 2 FA
              </button>
            </Link>
          </div>
          <div>
            <Link to={"/"}>
              <button className="btn-danger">
                Add Later
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Force2FA;
