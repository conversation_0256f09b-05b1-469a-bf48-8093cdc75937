import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  FaShieldAlt,
  FaLock,
  FaCheckCircle,
  FaMobile,
  FaEnvelope,
  FaArrowRight,
  FaHome,
  FaUserShield
} from "react-icons/fa";
import "./two-factor.css";

const Force2FA = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  const benefits = [
    {
      icon: <FaShieldAlt />,
      title: "Prevent unauthorized access",
      description: "Block hackers from accessing your account"
    },
    {
      icon: <FaLock />,
      title: "Secure your sensitive information",
      description: "Protect your personal and professional data"
    },
    {
      icon: <FaUserShield />,
      title: "Ensure only you can log in",
      description: "Add an extra verification step for security"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 flex items-center justify-center p-4">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 w-full max-w-md"
      >
        {/* Main Card */}
        <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500">
          {/* Logo */}
          <motion.div variants={itemVariants} className="text-center mb-8">
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center p-4">
              <img
                src="/upcoding_logo.png"
                alt="UpCoding Logo"
                className="w-full h-full object-contain filter brightness-0 invert"
              />
            </div>
            <div className="flex items-center justify-center gap-2 mb-2">
              <FaShieldAlt className="text-2xl text-blue-400" />
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-cyan-200 bg-clip-text text-transparent">
                Secure Your Account
              </h1>
            </div>
            <p className="text-slate-300 text-lg">
              Enable 2-Factor Authentication for enhanced security
            </p>
          </motion.div>

          {/* Description */}
          <motion.div variants={itemVariants} className="mb-8">
            <div className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded-2xl p-6 border border-blue-500/20">
              <p className="text-slate-200 leading-relaxed text-center">
                Your security is our priority. Enable two-factor authentication (2FA)
                to add an extra layer of protection to your account and keep your data safe.
              </p>
            </div>
          </motion.div>

          {/* Benefits */}
          <motion.div variants={itemVariants} className="mb-8">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <FaCheckCircle className="text-green-400" />
              Security Benefits
            </h3>
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="flex items-start gap-4 p-4 bg-slate-900/30 rounded-xl border border-slate-600/30 hover:border-slate-500/50 transition-all duration-300"
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm">{benefit.icon}</span>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">{benefit.title}</h4>
                    <p className="text-slate-400 text-sm">{benefit.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div variants={itemVariants} className="space-y-4">
            <Link to="/two-fa-email" className="block">
              <button className="w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 font-semibold text-lg flex items-center justify-center gap-3 group">
                <FaEnvelope className="text-xl" />
                <span>Enable 2FA Now</span>
                <FaArrowRight className="text-sm group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </Link>

            <Link to="/" className="block">
              <button className="w-full px-6 py-3 bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-white rounded-xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300 font-semibold flex items-center justify-center gap-2">
                <FaHome className="text-sm" />
                <span>Set Up Later</span>
              </button>
            </Link>
          </motion.div>

          {/* Security Note */}
          <motion.div variants={itemVariants} className="mt-6 pt-6 border-t border-slate-600/30">
            <div className="flex items-center gap-2 text-slate-400 text-sm justify-center">
              <FaMobile className="text-blue-400" />
              <span>Works with SMS, Email, and Authenticator Apps</span>
            </div>
          </motion.div>
        </div>

        {/* Additional Info */}
        <motion.div variants={itemVariants} className="mt-6 text-center">
          <p className="text-slate-400 text-sm">
            Need help? <a href="#" className="text-blue-400 hover:text-blue-300 transition-colors duration-200">Contact Support</a>
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Force2FA;
