import React from "react";
import { motion } from "framer-motion";

const DataScienceLiveClasses = ({ onBackToCourse }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const upcomingClasses = [
    {
      title: "Introduction to Pandas & Data Manipulation",
      instructor: "Dr. <PERSON>",
      date: "July 12, 2025",
      time: "2:00 PM EST",
      duration: "90 mins",
      level: "Beginner",
      topics: ["DataFrame operations", "Data cleaning", "Aggregations", "Merging datasets"]
    },
    {
      title: "Statistical Analysis with Python",
      instructor: "Prof. <PERSON>",
      date: "July 14, 2025", 
      time: "3:00 PM EST",
      duration: "2 hours",
      level: "Intermediate",
      topics: ["Hypothesis testing", "Correlation analysis", "Regression", "ANOVA"]
    },
    {
      title: "Machine Learning Fundamentals",
      instructor: "<PERSON><PERSON> <PERSON>",
      date: "July 16, 2025",
      time: "1:00 PM EST", 
      duration: "2.5 hours",
      level: "Intermediate",
      topics: ["Supervised learning", "Model evaluation", "Cross-validation", "Feature selection"]
    },
    {
      title: "Deep Learning with TensorFlow",
      instructor: "Dr. James Wilson",
      date: "July 18, 2025",
      time: "4:00 PM EST",
      duration: "2 hours",
      level: "Advanced",
      topics: ["Neural networks", "CNNs", "Transfer learning", "Model deployment"]
    }
  ];

  return (
    <div className="py-8">
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-teal-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🎓 Live Interactive Classes
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Learn with{" "}
            <span className="bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
              Expert
            </span>{" "}
            Instructors
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Join interactive sessions with industry experts and learn data science through hands-on practice
          </p>
        </motion.div>

        {/* Live Class Benefits */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {[
            {
              icon: "👨‍🏫",
              title: "Expert Instructors",
              description: "Learn from industry professionals with years of experience"
            },
            {
              icon: "💬",
              title: "Interactive Q&A",
              description: "Ask questions and get real-time answers during sessions"
            },
            {
              icon: "🤝",
              title: "Peer Learning",
              description: "Collaborate and learn alongside other data science enthusiasts"
            },
            {
              icon: "📝",
              title: "Hands-on Practice",
              description: "Work on real problems and datasets during live sessions"
            }
          ].map((benefit, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ y: -5, scale: 1.02 }}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-6 text-center shadow-xl hover:shadow-2xl transition-all duration-300"
            >
              <div className="text-4xl mb-4">{benefit.icon}</div>
              <h3 className="text-lg font-bold text-white mb-3">{benefit.title}</h3>
              <p className="text-white/70 text-sm">{benefit.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Upcoming Classes */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-white mb-8 text-center">Upcoming Live Classes</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {upcomingClasses.map((classItem, index) => (
              <motion.div
                key={index}
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300"
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h4 className="text-xl font-bold text-white mb-2">{classItem.title}</h4>
                    <p className="text-teal-300 font-medium">{classItem.instructor}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    classItem.level === 'Beginner' ? 'bg-green-500/20 text-green-300' :
                    classItem.level === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-300' :
                    'bg-red-500/20 text-red-300'
                  }`}>
                    {classItem.level}
                  </span>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <p className="text-white/60 text-sm mb-1">📅 Date</p>
                    <p className="text-white font-medium">{classItem.date}</p>
                  </div>
                  <div>
                    <p className="text-white/60 text-sm mb-1">⏰ Time</p>
                    <p className="text-white font-medium">{classItem.time}</p>
                  </div>
                  <div>
                    <p className="text-white/60 text-sm mb-1">⏱️ Duration</p>
                    <p className="text-white font-medium">{classItem.duration}</p>
                  </div>
                  <div>
                    <p className="text-white/60 text-sm mb-1">👥 Format</p>
                    <p className="text-white font-medium">Live Online</p>
                  </div>
                </div>
                
                <div className="mb-6">
                  <p className="text-white/60 text-sm mb-3">📚 Topics Covered:</p>
                  <div className="flex flex-wrap gap-2">
                    {classItem.topics.map((topic, i) => (
                      <span 
                        key={i}
                        className="bg-teal-500/10 border border-teal-500/20 text-teal-300 px-3 py-1 rounded-full text-xs"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>
                
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 text-white py-3 px-6 rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Register for Class
                </motion.button>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Past Sessions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-16"
        >
          <h3 className="text-2xl font-bold text-white mb-8 flex items-center">
            <span className="text-3xl mr-3">📺</span>
            Recorded Sessions Library
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { title: "Python Fundamentals for Data Science", views: "2.1K", rating: "4.9" },
              { title: "Data Visualization Masterclass", views: "1.8K", rating: "4.8" },
              { title: "Machine Learning Project Walkthrough", views: "3.2K", rating: "4.9" },
              { title: "SQL for Data Analysis", views: "1.5K", rating: "4.7" },
              { title: "Time Series Analysis Workshop", views: "950", rating: "4.8" },
              { title: "Natural Language Processing Basics", views: "1.2K", rating: "4.6" }
            ].map((session, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.02 }}
                className="bg-white/5 border border-white/10 rounded-xl p-4 hover:bg-white/10 transition-all duration-300 cursor-pointer"
              >
                <div className="aspect-video bg-gradient-to-br from-teal-600/20 to-blue-600/20 rounded-lg mb-4 flex items-center justify-center">
                  <div className="text-4xl">▶️</div>
                </div>
                <h4 className="font-bold text-white mb-2 text-sm">{session.title}</h4>
                <div className="flex justify-between items-center text-xs text-white/60">
                  <span>👁️ {session.views} views</span>
                  <span>⭐ {session.rating}/5</span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.7 }}
          className="text-center bg-gradient-to-r from-teal-600/20 to-blue-600/20 backdrop-blur-lg border border-white/10 rounded-3xl p-12"
        >
          <h3 className="text-3xl font-bold text-white mb-4">Join Our Learning Community</h3>
          <p className="text-white/80 text-lg mb-8 max-w-2xl mx-auto">
            Connect with fellow learners, access exclusive content, and accelerate your data science journey.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-teal-600 to-blue-600 text-white px-8 py-4 rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Get Premium Access
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="border-2 border-white/30 backdrop-blur-lg text-white px-8 py-4 rounded-xl font-bold hover:bg-white/10 transition-all duration-300"
            >
              View Class Schedule
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DataScienceLiveClasses;
