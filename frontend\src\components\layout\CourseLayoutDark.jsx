import React from "react";
import { motion } from "framer-motion";
import ReusableNavbar from "./ReusableNavbar";
import SidebarWrapper from "./SidebarWrapper";
import { AnimatedBackground } from "../ui";

const CourseLayoutDark = ({ 
  children, 
  isSidebarOpen, 
  toggleSidebar, 
  title,
  showAnimatedBackground = true 
}) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-[#1a3c50] to-[#010509] relative overflow-hidden"
    >
      {/* Background animated elements */}
      {showAnimatedBackground && <AnimatedBackground />}

      {/* Navbar */}
      <ReusableNavbar 
        toggleSidebar={toggleSidebar} 
        title={title}
        isSidebarOpen={isSidebarOpen}
      />
      
      {/* Sidebar */}
      <SidebarWrapper isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      
      {/* Main Content with dynamic left margin for sidebar */}
      <motion.div
        className="flex flex-col min-h-screen bg-gradient-to-br from-[#1a3c50] to-[#010509] relative z-10 transition-all duration-400"
        style={{ 
          marginLeft: isSidebarOpen ? "280px" : "0px",
          paddingTop: "0px" // Remove all top padding so hero sections start from 0px
        }}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {children}
      </motion.div>
    </motion.div>
  );
};

export default CourseLayoutDark;