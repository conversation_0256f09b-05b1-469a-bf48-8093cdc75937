import React from "react";
import { <PERSON><PERSON><PERSON>, QuestionSection, InterviewChecklist } from "./components";
import TopQuestionsIntroduction from "./components/TopQuestionsIntroduction";
import TopQuestionsLabEnvironment from "./components/TopQuestionsLabEnvironment";
import TopQuestionsLiveClasses from "./components/TopQuestionsLiveClasses";
import TopQuestionsFAQS from "./components/TopQuestionsFAQS";
import TopQuestionsLayout from "./components/TopQuestionsLayout";
import TopQuestionsPremiumModal from "./components/TopQuestionsPremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import useSidebarState from "../../hooks/useSidebarState";
import useInterviewData from "./hooks/useInterviewData";

const TopInterviewQuestion_updated = () => {
  const { interviewData, interviewChecklist } = useInterviewData();

  const courseConfig = {
    title: "Interview Questions Resources",
    subtitle: "Select a resource category to master technical interviews through theory, practice, live sessions, or FAQs",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn interview strategies and techniques",
        icon: "📚",
        component: TopQuestionsIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice with interview simulators",
        icon: "💻",
        component: TopQuestionsLabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join mock interviews and strategy sessions",
        icon: "🎥",
        component: TopQuestionsLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common interview questions",
        icon: "❓",
        component: TopQuestionsFAQS,
        props: {}
      },
      {
        id: "InterviewQuestions",
        title: "Top Questions",
        description: "Curated questions from top companies",
        icon: "🎯",
        component: QuestionSection,
        props: { interviewData }
      },
      {
        id: "InterviewChecklist",
        title: "Interview Checklist",
        description: "Complete preparation checklist",
        icon: "✅",
        component: InterviewChecklist,
        props: { interviewChecklist }
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={InterviewHero}
      LayoutComponent={TopQuestionsLayout}
      PremiumModalComponent={TopQuestionsPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default TopInterviewQuestion_updated;
