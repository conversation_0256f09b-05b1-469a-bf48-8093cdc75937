import React from "react";
import { CourseResourcesSection } from "../../../components/ui";
import useSidebarState from "../../../hooks/useSidebarState";
import JavaFullStackHero from "./components/JavaFullStackHero";
import FullstackLayout from "../components/FullstackLayout";
import FullstackPremiumModal from "../components/FullstackPremiumModal";
import JavaFullStackIntroduction from "./components/JavaFullStackIntroduction";
import JavaFullStackLabEnvironment from "./components/JavaFullStackLabEnvironment";
import JavaFullStackLiveClasses from "./components/JavaFullStackLiveClasses";
import JavaFullStackFAQS from "./components/JavaFullStackFAQS";

const JavaFullStack = () => {
  const courseConfig = {
    title: "Java Full Stack Development",
    subtitle: "Master enterprise-grade applications with Java, Spring Boot, and modern frontend frameworks",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn Java full stack fundamentals",
        icon: "📚",
        component: JavaFullStackIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Practice Lab",
        description: "Build full stack applications with Java",
        icon: "💻",
        component: JavaFullStackLabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Projects",
        description: "Work on real-world Java projects",
        icon: "🚀",
        component: JavaFullStackLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common questions",
        icon: "❓",
        component: JavaFullStackFAQS,
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={JavaFullStackHero}
      LayoutComponent={FullstackLayout}
      PremiumModalComponent={FullstackPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default JavaFullStack;