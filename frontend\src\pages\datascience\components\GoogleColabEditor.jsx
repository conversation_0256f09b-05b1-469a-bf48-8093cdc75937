import React, { useState } from "react";
import { EmbeddedCodeEditor } from "../../../components/ui";

const GoogleColabEditor = ({ onBack }) => {
  const [cells, setCells] = useState([
    {
      id: "cell-1",
      content: "# Google Colab Notebook\n# Mount Google Drive\nfrom google.drive import mount\n\nmount('/content/drive')\nprint('Google Drive mounted successfully!')",
      output: "Google Drive mounted successfully!",
      isRunning: false
    },
    {
      id: "cell-2",
      content: "# Install additional packages\n!pip install tensorflow==2.9.0 keras matplotlib seaborn",
      output: "Successfully installed tensorflow-2.9.0 keras-2.9.0 matplotlib-3.5.2 seaborn-0.11.2",
      isRunning: false
    },
    {
      id: "cell-3",
      content: "# Import libraries\nimport tensorflow as tf\nimport numpy as np\nimport pandas as pd\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\nprint(f\"TensorFlow version: {tf.__version__}\")\nprint(f\"NumPy version: {np.__version__}\")\nprint(f\"Pandas version: {pd.__version__}\")",
      output: "TensorFlow version: 2.9.0\nNumPy version: 1.22.4\nPandas version: 1.4.2",
      isRunning: false
    }
  ]);

  const handleRunCode = (cellId) => {
    setCells(cells.map(cell => {
      if (cell.id === cellId) {
        // Simulate code execution
        const output = simulateCodeExecution(cell.content);
        return { ...cell, isRunning: false, output };
      }
      return cell;
    }));
  };

  const simulateCodeExecution = (code) => {
    // Simple simulation of code execution with appropriate outputs
    if (code.includes('mount')) {
      return "Google Drive mounted successfully!";
    } else if (code.includes('pip install')) {
      return "Successfully installed tensorflow-2.9.0 keras-2.9.0 matplotlib-3.5.2 seaborn-0.11.2";
    } else if (code.includes('version')) {
      return "TensorFlow version: 2.9.0\nNumPy version: 1.22.4\nPandas version: 1.4.2";
    } else if (code.includes('plt.plot')) {
      return "[Matplotlib plot displayed]";
    } else {
      return "Code executed successfully!";
    }
  };

  const handleCodeChange = (cellId, newContent) => {
    setCells(cells.map(cell => 
      cell.id === cellId ? { ...cell, content: newContent } : cell
    ));
  };

  const addCell = () => {
    const newCell = {
      id: `cell-${Date.now()}`,
      content: "# New code cell",
      output: "",
      isRunning: false
    };
    
    setCells([...cells, newCell]);
  };

  return (
    <div className="bg-[#1e1e1e] text-white rounded-xl overflow-hidden border border-gray-700">
      <div className="bg-[#3c78d8] p-4 flex justify-between items-center">
        <div className="flex items-center">
          <button 
            onClick={onBack}
            className="mr-3 px-3 py-1 bg-white/20 hover:bg-white/30 rounded text-sm flex items-center"
          >
            <span className="mr-1">←</span> Back
          </button>
          <span className="text-2xl mr-3">☁️</span>
          <h2 className="text-xl font-bold">Google Colab</h2>
        </div>
        <div className="flex space-x-2">
          <div className="px-3 py-1 bg-white/20 rounded text-sm">GPU: T4</div>
          <button className="px-3 py-1 bg-white/20 hover:bg-white/30 rounded text-sm">Share</button>
        </div>
      </div>

      <div className="p-4">
        <div className="mb-4 flex justify-between items-center">
          <div className="flex space-x-2">
            <button className="px-3 py-1 bg-[#3c78d8] hover:bg-[#2a66c5] rounded text-sm">File</button>
            <button className="px-3 py-1 bg-[#3c78d8] hover:bg-[#2a66c5] rounded text-sm">Edit</button>
            <button className="px-3 py-1 bg-[#3c78d8] hover:bg-[#2a66c5] rounded text-sm">Runtime</button>
          </div>
          <div>
            <button className="px-3 py-1 bg-[#3c78d8] hover:bg-[#2a66c5] rounded text-sm">Connect</button>
          </div>
        </div>

        {cells.map((cell, index) => (
          <div key={cell.id} className="mb-8">
            {/* Cell controls */}
            <div className="flex items-center justify-between mb-1 text-xs text-gray-400">
              <div className="flex items-center">
                <span className="bg-[#3c78d8] px-2 py-1 rounded mr-2 text-white">{`[${index + 1}]`}</span>
                <span>Code</span>
              </div>
              <div className="flex space-x-2">
                <button 
                  onClick={() => handleRunCode(cell.id)}
                  className="px-2 py-1 bg-[#3c78d8] hover:bg-[#2a66c5] rounded flex items-center text-white"
                >
                  <span className="mr-1">▶</span> Run
                </button>
              </div>
            </div>

            {/* Cell content */}
            <div className="border border-gray-700 rounded-md overflow-hidden">
              <div className="border-b border-gray-700">
                <EmbeddedCodeEditor
                  language="python"
                  value={cell.content}
                  onChange={(newCode) => handleCodeChange(cell.id, newCode)}
                  height="auto"
                />
              </div>
              {cell.output && (
                <div className="bg-[#2d2d2d] p-4 font-mono text-sm whitespace-pre-wrap text-gray-300">
                  {cell.output}
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Add cell button */}
        <div className="flex justify-center mt-6">
          <button 
            onClick={addCell}
            className="px-4 py-2 bg-[#3c78d8] hover:bg-[#2a66c5] rounded-md"
          >
            + Add Code Cell
          </button>
        </div>
      </div>
    </div>
  );
};

export default GoogleColabEditor;