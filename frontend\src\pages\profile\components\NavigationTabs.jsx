const NavigationTabs = ({ activeTab, setActiveTab, stats }) => {
  const tabs = [
    { key: "active", label: "Active Projects", count: stats.activeProjects, icon: "🚀" },
    { key: "completed", label: "Completed", count: stats.completedProjects, icon: "✅" },
    { key: "paused", label: "Paused", count: stats.pausedProjects, icon: "⏸️" }
  ];

  return (
    <div className="bg-gradient-to-r from-slate-800/50 via-slate-700/50 to-slate-800/50 backdrop-blur-xl rounded-2xl shadow-xl p-2 mb-8 border border-slate-600/30">
      <div className="flex space-x-2">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-xl font-semibold transition-all duration-300 ${
              activeTab === tab.key
                ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                : "text-slate-300 hover:text-white hover:bg-slate-700/50"
            }`}
          >
            <span className="text-lg">{tab.icon}</span>
            <span>{tab.label}</span>
            <span className={`px-2 py-1 rounded-full text-xs font-bold ${
              activeTab === tab.key ? "bg-white/20" : "bg-slate-600/50"
            }`}>
              {tab.count}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default NavigationTabs;
