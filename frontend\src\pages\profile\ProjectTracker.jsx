import { useState, useEffect } from "react";
import { 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaPlay, 
  FaPause, 
  FaStop, 
  FaClock, 
  FaCalendarAlt,
  FaFlag,
  FaCheckCircle,
  FaExclamationTriangle,
  FaChartLine,
  FaFilter,
  FaSearch,
  FaSave,
  FaTimes
} from "react-icons/fa";
import toast from "react-hot-toast";

const ProjectTracker = () => {
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProject, setEditingProject] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [sortBy, setSortBy] = useState("created");

  // Form state for adding/editing projects
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    status: "planning",
    priority: "medium",
    startDate: "",
    endDate: "",
    progress: 0,
    tags: [],
    estimatedHours: 0,
    actualHours: 0
  });

  // Sample initial data
  useEffect(() => {
    const sampleProjects = [
      {
        id: 1,
        title: "E-commerce Website",
        description: "Full-stack e-commerce platform with React and Node.js",
        status: "in-progress",
        priority: "high",
        startDate: "2024-01-15",
        endDate: "2024-03-15",
        progress: 65,
        tags: ["React", "Node.js", "MongoDB"],
        estimatedHours: 120,
        actualHours: 78,
        createdAt: "2024-01-15T10:00:00Z",
        lastUpdated: "2024-01-20T14:30:00Z"
      },
      {
        id: 2,
        title: "Mobile App UI/UX",
        description: "Design and prototype for fitness tracking mobile app",
        status: "completed",
        priority: "medium",
        startDate: "2023-12-01",
        endDate: "2024-01-10",
        progress: 100,
        tags: ["Figma", "UI/UX", "Mobile"],
        estimatedHours: 60,
        actualHours: 55,
        createdAt: "2023-12-01T09:00:00Z",
        lastUpdated: "2024-01-10T16:45:00Z"
      },
      {
        id: 3,
        title: "API Documentation",
        description: "Comprehensive API documentation for client project",
        status: "planning",
        priority: "low",
        startDate: "2024-02-01",
        endDate: "2024-02-15",
        progress: 15,
        tags: ["Documentation", "API", "Technical Writing"],
        estimatedHours: 30,
        actualHours: 5,
        createdAt: "2024-01-25T11:00:00Z",
        lastUpdated: "2024-01-25T11:00:00Z"
      }
    ];
    setProjects(sampleProjects);
    setFilteredProjects(sampleProjects);
  }, []);

  // Filter and search functionality
  useEffect(() => {
    let filtered = projects;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Status filter
    if (filterStatus !== "all") {
      filtered = filtered.filter(project => project.status === filterStatus);
    }

    // Priority filter
    if (filterPriority !== "all") {
      filtered = filtered.filter(project => project.priority === filterPriority);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "title":
          return a.title.localeCompare(b.title);
        case "priority":
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case "progress":
          return b.progress - a.progress;
        case "created":
        default:
          return new Date(b.createdAt) - new Date(a.createdAt);
      }
    });

    setFilteredProjects(filtered);
  }, [projects, searchTerm, filterStatus, filterPriority, sortBy]);

  const getStatusColor = (status) => {
    switch (status) {
      case "planning":
        return "bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border-blue-500/30";
      case "in-progress":
        return "bg-gradient-to-r from-orange-600/20 to-yellow-600/20 border-orange-500/30";
      case "completed":
        return "bg-gradient-to-r from-green-600/20 to-emerald-600/20 border-green-500/30";
      case "on-hold":
        return "bg-gradient-to-r from-gray-600/20 to-slate-600/20 border-gray-500/30";
      default:
        return "bg-gradient-to-r from-slate-600/20 to-gray-600/20 border-slate-500/30";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "text-red-400 bg-red-900/30 border-red-500/30";
      case "medium":
        return "text-yellow-400 bg-yellow-900/30 border-yellow-500/30";
      case "low":
        return "text-green-400 bg-green-900/30 border-green-500/30";
      default:
        return "text-gray-400 bg-gray-900/30 border-gray-500/30";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "planning":
        return <FaClock className="text-blue-400" />;
      case "in-progress":
        return <FaPlay className="text-orange-400" />;
      case "completed":
        return <FaCheckCircle className="text-green-400" />;
      case "on-hold":
        return <FaPause className="text-gray-400" />;
      default:
        return <FaClock className="text-slate-400" />;
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleTagsChange = (e) => {
    const tags = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag);
    setFormData(prev => ({
      ...prev,
      tags
    }));
  };

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      status: "planning",
      priority: "medium",
      startDate: "",
      endDate: "",
      progress: 0,
      tags: [],
      estimatedHours: 0,
      actualHours: 0
    });
  };

  const handleAddProject = () => {
    if (!formData.title.trim()) {
      toast.error("Project title is required");
      return;
    }

    const newProject = {
      id: Date.now(),
      ...formData,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    setProjects(prev => [newProject, ...prev]);
    setShowAddModal(false);
    resetForm();
    toast.success("Project added successfully!");
  };

  const handleEditProject = () => {
    if (!formData.title.trim()) {
      toast.error("Project title is required");
      return;
    }

    setProjects(prev => prev.map(project =>
      project.id === editingProject.id
        ? { ...project, ...formData, lastUpdated: new Date().toISOString() }
        : project
    ));

    setShowEditModal(false);
    setEditingProject(null);
    resetForm();
    toast.success("Project updated successfully!");
  };

  const handleDeleteProject = (id) => {
    if (window.confirm("Are you sure you want to delete this project?")) {
      setProjects(prev => prev.filter(project => project.id !== id));
      toast.success("Project deleted successfully!");
    }
  };

  const openEditModal = (project) => {
    setEditingProject(project);
    setFormData({
      title: project.title,
      description: project.description,
      status: project.status,
      priority: project.priority,
      startDate: project.startDate,
      endDate: project.endDate,
      progress: project.progress,
      tags: project.tags,
      estimatedHours: project.estimatedHours,
      actualHours: project.actualHours
    });
    setShowEditModal(true);
  };

  const calculateTimeEfficiency = (estimated, actual) => {
    if (estimated === 0) return 0;
    return Math.round(((estimated - actual) / estimated) * 100);
  };

  const getProjectStats = () => {
    const total = projects.length;
    const completed = projects.filter(p => p.status === "completed").length;
    const inProgress = projects.filter(p => p.status === "in-progress").length;
    const planning = projects.filter(p => p.status === "planning").length;
    const onHold = projects.filter(p => p.status === "on-hold").length;
    
    const totalEstimated = projects.reduce((sum, p) => sum + p.estimatedHours, 0);
    const totalActual = projects.reduce((sum, p) => sum + p.actualHours, 0);
    const avgProgress = projects.length > 0 ? Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length) : 0;

    return {
      total,
      completed,
      inProgress,
      planning,
      onHold,
      totalEstimated,
      totalActual,
      avgProgress,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
    };
  };

  const stats = getProjectStats();

  return (
    <div className="min-h-screen w-full pt-4 pb-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-200 via-blue-200 to-cyan-200 bg-clip-text text-transparent mb-2">
            🚀 Project Tracker
          </h1>
          <p className="text-slate-400 text-lg">Manage and track your projects with comprehensive analytics</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-br from-blue-900/60 via-blue-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-blue-500/30 hover:border-blue-400/50 transition-all duration-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-200 text-sm font-semibold">Total Projects</p>
                <p className="text-3xl font-bold text-white">{stats.total}</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                <FaChartLine className="text-blue-400 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-900/60 via-green-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-green-500/30 hover:border-green-400/50 transition-all duration-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-200 text-sm font-semibold">Completed</p>
                <p className="text-3xl font-bold text-white">{stats.completed}</p>
                <p className="text-green-300 text-xs">{stats.completionRate}% completion rate</p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                <FaCheckCircle className="text-green-400 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-900/60 via-orange-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-orange-500/30 hover:border-orange-400/50 transition-all duration-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-200 text-sm font-semibold">In Progress</p>
                <p className="text-3xl font-bold text-white">{stats.inProgress}</p>
                <p className="text-orange-300 text-xs">Avg: {stats.avgProgress}% complete</p>
              </div>
              <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                <FaPlay className="text-orange-400 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-900/60 via-purple-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-200 text-sm font-semibold">Time Tracked</p>
                <p className="text-3xl font-bold text-white">{stats.totalActual}h</p>
                <p className="text-purple-300 text-xs">Est: {stats.totalEstimated}h</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                <FaClock className="text-purple-400 text-xl" />
              </div>
            </div>
          </div>
        </div>

        {/* Controls Bar */}
        <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-500 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
              />
            </div>

            {/* Filters */}
            <div className="flex gap-3 items-center">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
              >
                <option value="all">All Status</option>
                <option value="planning">Planning</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="on-hold">On Hold</option>
              </select>

              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                className="px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
              >
                <option value="all">All Priority</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
              >
                <option value="created">Sort by Created</option>
                <option value="title">Sort by Title</option>
                <option value="priority">Sort by Priority</option>
                <option value="progress">Sort by Progress</option>
              </select>

              <button
                onClick={() => setShowAddModal(true)}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold flex items-center gap-2"
              >
                <FaPlus />
                Add Project
              </button>
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className={`relative backdrop-blur-xl rounded-2xl shadow-xl p-6 border transition-all duration-500 group overflow-hidden ${getStatusColor(project.status)}`}
            >
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

              <div className="relative z-10">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(project.status)}
                    <div>
                      <h3 className="text-xl font-bold text-white mb-1">{project.title}</h3>
                      <p className="text-slate-300 text-sm line-clamp-2">{project.description}</p>
                    </div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs font-bold border ${getPriorityColor(project.priority)}`}>
                    {project.priority.toUpperCase()}
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-slate-300">Progress</span>
                    <span className="text-sm font-semibold text-white">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-slate-700/50 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        project.status === 'completed' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                        project.status === 'in-progress' ? 'bg-gradient-to-r from-blue-500 to-cyan-500' :
                        'bg-gradient-to-r from-yellow-500 to-orange-500'
                      }`}
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Tags */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {project.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-slate-800/50 text-slate-300 text-xs rounded-lg border border-slate-600/30"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Project Details */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <div className="text-slate-400 mb-1">Start Date</div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaCalendarAlt className="text-xs" />
                      {new Date(project.startDate).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400 mb-1">End Date</div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaCalendarAlt className="text-xs" />
                      {new Date(project.endDate).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400 mb-1">Estimated</div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaClock className="text-xs" />
                      {project.estimatedHours}h
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400 mb-1">Actual</div>
                    <div className="text-white font-semibold flex items-center gap-1">
                      <FaClock className="text-xs" />
                      {project.actualHours}h
                    </div>
                  </div>
                </div>

                {/* Time Efficiency */}
                {project.estimatedHours > 0 && (
                  <div className="mb-4 p-3 bg-slate-900/30 border border-slate-600/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FaChartLine className={`${
                        calculateTimeEfficiency(project.estimatedHours, project.actualHours) >= 0
                          ? 'text-green-400'
                          : 'text-red-400'
                      }`} />
                      <span className="text-slate-300 text-sm font-semibold">
                        Time Efficiency: {calculateTimeEfficiency(project.estimatedHours, project.actualHours)}%
                      </span>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <button
                    onClick={() => openEditModal(project)}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white rounded-lg transition-all duration-300 border border-slate-600/30 hover:border-slate-500/50"
                  >
                    <FaEdit className="text-sm" />
                    <span className="text-sm font-semibold">Edit</span>
                  </button>

                  <button
                    onClick={() => handleDeleteProject(project.id)}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-red-800/50 hover:bg-red-700/50 text-red-300 hover:text-white rounded-lg transition-all duration-300 border border-red-600/30 hover:border-red-500/50"
                  >
                    <FaTrash className="text-sm" />
                    <span className="text-sm font-semibold">Delete</span>
                  </button>
                </div>

                {/* Last Updated */}
                <div className="mt-4 pt-4 border-t border-slate-600/30">
                  <div className="text-xs text-slate-400">
                    Last updated: {new Date(project.lastUpdated).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaExclamationTriangle className="text-slate-300 text-3xl" />
            </div>
            <h3 className="text-2xl font-bold text-slate-300 mb-2">
              No projects found
            </h3>
            <p className="text-slate-400 mb-6">
              {searchTerm || filterStatus !== "all" || filterPriority !== "all"
                ? "Try adjusting your search or filters"
                : "Start by creating your first project"
              }
            </p>
            <button
              onClick={() => setShowAddModal(true)}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold"
            >
              + Add New Project
            </button>
          </div>
        )}

        {/* Add Project Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="relative bg-gradient-to-br from-slate-800/95 via-slate-700/90 to-slate-800/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 max-w-2xl w-full border border-slate-600/30 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">Add New Project</h2>
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                  }}
                  className="text-slate-400 hover:text-white transition-colors duration-200"
                >
                  <FaTimes className="text-xl" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Project Title *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter project title..."
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Priority
                    </label>
                    <select
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-300 mb-2">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter project description..."
                    rows={3}
                    className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300 resize-none"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Status
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    >
                      <option value="planning">Planning</option>
                      <option value="in-progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="on-hold">On Hold</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Progress (%)
                    </label>
                    <input
                      type="number"
                      name="progress"
                      value={formData.progress}
                      onChange={handleInputChange}
                      min="0"
                      max="100"
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      name="startDate"
                      value={formData.startDate}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      End Date
                    </label>
                    <input
                      type="date"
                      name="endDate"
                      value={formData.endDate}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Estimated Hours
                    </label>
                    <input
                      type="number"
                      name="estimatedHours"
                      value={formData.estimatedHours}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Actual Hours
                    </label>
                    <input
                      type="number"
                      name="actualHours"
                      value={formData.actualHours}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-300 mb-2">
                    Tags (comma-separated)
                  </label>
                  <input
                    type="text"
                    value={formData.tags.join(', ')}
                    onChange={handleTagsChange}
                    placeholder="React, Node.js, MongoDB..."
                    className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                  />
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    className="flex-1 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-xl transition-all duration-300 font-semibold"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddProject}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-300 font-semibold flex items-center justify-center gap-2"
                  >
                    <FaSave />
                    Add Project
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Edit Project Modal */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="relative bg-gradient-to-br from-slate-800/95 via-slate-700/90 to-slate-800/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 max-w-2xl w-full border border-slate-600/30 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">Edit Project</h2>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingProject(null);
                    resetForm();
                  }}
                  className="text-slate-400 hover:text-white transition-colors duration-200"
                >
                  <FaTimes className="text-xl" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Project Title *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter project title..."
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Priority
                    </label>
                    <select
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-300 mb-2">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter project description..."
                    rows={3}
                    className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300 resize-none"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Status
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    >
                      <option value="planning">Planning</option>
                      <option value="in-progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="on-hold">On Hold</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Progress (%)
                    </label>
                    <input
                      type="number"
                      name="progress"
                      value={formData.progress}
                      onChange={handleInputChange}
                      min="0"
                      max="100"
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      name="startDate"
                      value={formData.startDate}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      End Date
                    </label>
                    <input
                      type="date"
                      name="endDate"
                      value={formData.endDate}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Estimated Hours
                    </label>
                    <input
                      type="number"
                      name="estimatedHours"
                      value={formData.estimatedHours}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-300 mb-2">
                      Actual Hours
                    </label>
                    <input
                      type="number"
                      name="actualHours"
                      value={formData.actualHours}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-300 mb-2">
                    Tags (comma-separated)
                  </label>
                  <input
                    type="text"
                    value={formData.tags.join(', ')}
                    onChange={handleTagsChange}
                    placeholder="React, Node.js, MongoDB..."
                    className="w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                  />
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    onClick={() => {
                      setShowEditModal(false);
                      setEditingProject(null);
                      resetForm();
                    }}
                    className="flex-1 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-xl transition-all duration-300 font-semibold"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleEditProject}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl transition-all duration-300 font-semibold flex items-center justify-center gap-2"
                  >
                    <FaSave />
                    Update Project
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectTracker;
