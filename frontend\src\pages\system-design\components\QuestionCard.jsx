import { motion } from "framer-motion";

const QuestionCard = ({ question, index }) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { 
        duration: 0.5, 
        delay: index * 0.1,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={{ 
        scale: 1.02, 
        y: -5,
        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)"
      }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-xl p-6 mb-4 shadow-lg border border-gray-100 cursor-pointer hover:border-blue-200 transition-all duration-300"
    >
      <div className="mb-4">
        <motion.h3 
          className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          {question.title}
        </motion.h3>
      </div>
      
      <motion.div 
        className="flex flex-wrap gap-2 mb-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 + index * 0.1 }}
      >
        {question.tags.map((tag, tagIndex) => (
          <motion.span
            key={tagIndex}
            whileHover={{ scale: 1.05, y: -2 }}
            className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium border border-blue-200"
          >
            {tag}
          </motion.span>
        ))}
      </motion.div>
      
      <motion.ul 
        className="space-y-2 text-gray-600"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 + index * 0.1 }}
      >
        {question.keyPoints.map((point, pointIndex) => (
          <motion.li
            key={pointIndex}
            className="flex items-start gap-2"
            whileHover={{ x: 5 }}
            transition={{ duration: 0.2 }}
          >
            <motion.span 
              className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2 flex-shrink-0"
              whileHover={{ scale: 1.3 }}
              transition={{ duration: 0.2 }}
            />
            {point}
          </motion.li>
        ))}
      </motion.ul>
    </motion.div>
  );
};

export default QuestionCard;
