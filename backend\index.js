import express from "express";
import { connectDB } from "./utils/db.js";
import dotenv from "dotenv";
import cors from "cors";
import { ErrorMiddleware } from "./middleware/error.js";
import userRouter from "./routes/user.js";
import registrationRouter from "./routes/registration.js";
import internshipRouter from "./routes/internship.js"
import cookieParser from "cookie-parser";
import morgan from "morgan";
import taskRoutes from "./routes/taskRoutes.js";
import messageRoutes from "./routes/messageRoutes.js";
import questionRoutes from "./routes/questionRoutes.js";
import faqRoutes from "./routes/faqRoutes.js";
import liveClassRoutes from "./routes/liveClassRoutes.js";


dotenv.config()

const app = express();

const PORT = process.env.PORT || 5000;

app.get("/", (req, res) => {
  res.status(200).json({
    success: true,
    message: "API WORKING",
  });
});

app.use(cookieParser());

const corsOptions = {
  origin: "http://localhost:5173",
  credentials: true,
  methods: "GET,POST,PUT,DELETE,PATCH",
  allowedHeaders: "Content-Type,Authorization,Origin,Accept",
};

app.use(cors(corsOptions));
app.use(express.json({ limit: "50mb" }));
app.use(morgan("dev"));
app.use(express.json());

app.use("/api/v1/auth", userRouter);
app.use("/api/v1", registrationRouter);
app.use("/api/v1", internshipRouter);
app.use("/api/tasks", taskRoutes);
app.use("/api/questions", questionRoutes);
app.use("/api/messages", messageRoutes);
app.use("/api/faqs", faqRoutes);
app.use("/api/v1/live-classes", liveClassRoutes);

app.use(ErrorMiddleware);


app.all("*", (req, res, next) => {
  const err = new Error(`Route ${req.originalUrl} not found`);
  err.statusCode = 404;
  next(err);
});

// Connect to database first
connectDB();

app.listen(PORT, () => {
  console.log(`Server is running on PORT ${PORT}`);
}); 