import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const PremiumModal = ({ showModal, closeModal }) => {
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const modalVariants = {
    hidden: { scale: 0.8, opacity: 0, y: 50 },
    visible: { 
      scale: 1, 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: { scale: 0.8, opacity: 0, y: 50 }
  };

  return (
    <AnimatePresence>
      {showModal && (
        <motion.div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          onClick={closeModal}
        >
          <motion.div
            className="bg-gradient-to-br from-[#0c0d21] to-[#1a103a] rounded-3xl p-8 max-w-md w-full relative overflow-hidden shadow-2xl border border-white/10"
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <motion.button
              className="absolute top-4 right-4 text-white/60 hover:text-white text-2xl"
              onClick={closeModal}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              ×
            </motion.button>

            {/* Premium icon */}
            <div className="text-center mb-6">
              <motion.div
                className="w-20 h-20 mx-auto bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white text-3xl mb-4"
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                👑
              </motion.div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Premium Access
              </h2>
              <p className="text-gray-600">
                Unlock the complete Python programming course
              </p>
            </div>

            {/* Features */}
            <div className="space-y-4 mb-8">
              {[
                "Complete Python curriculum",
                "Hands-on projects & exercises",
                "Real-world case studies",
                "24/7 support & mentorship",
                "Certificate of completion",
                "Lifetime access to updates"
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-3"
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.1 * index }}
                >
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">
                    ✓
                  </div>
                  <span className="text-gray-700">{feature}</span>
                </motion.div>
              ))}
            </div>

            {/* CTA */}
            <div className="space-y-4">
              <motion.button
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 rounded-2xl transition-all duration-300 shadow-lg"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Get Premium Access - $99
              </motion.button>
              
              <button
                className="w-full text-gray-500 hover:text-gray-700 py-2 transition-colors"
                onClick={closeModal}
              >
                Maybe later
              </button>
            </div>

            {/* Background decoration */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-10"></div>
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-10"></div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PremiumModal;
