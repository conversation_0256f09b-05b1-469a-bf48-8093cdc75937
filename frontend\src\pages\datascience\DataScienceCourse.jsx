import InternshipApply from "../../components/InternshipApply";
import React from "react";
import Introduction from "./Introduction";
import FAQS from "./FAQS";
import DataScienceLayout from "./components/DataScienceLayout";
import DataScienceHero from "./components/DataScienceHero";
import DataScienceLabEnvironmentNew from "./components/DataScienceLabEnvironmentNew";
import DataScienceLiveClasses from "./components/DataScienceLiveClasses";
import DataSciencePremiumModal from "./components/DataSciencePremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import useSidebarState from "../../hooks/useSidebarState";

const DataScienceCourse = () => {
  const courseConfig = {
    title: "Data Science Course Resources",
    subtitle: "Master data science through comprehensive theory, hands-on projects, live workshops, and expert guidance",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-white/80"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn data science fundamentals and methodologies",
        icon: "📊",
        component: Introduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice with real-world datasets and tools",
        icon: "🔬",
        component: DataScienceLabEnvironmentNew,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join expert-led data science workshops",
        icon: "🎓",
        component: DataScienceLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to data science questions",
        icon: "❓",
        component: FAQS,
        props: {}
      }
    ]
  };

  return (
    <>
      <CourseResourcesSection
        courseConfig={courseConfig}
        HeroComponent={DataScienceHero}
        LayoutComponent={DataScienceLayout}
        PremiumModalComponent={DataSciencePremiumModal}
        useSidebarHook={useSidebarState}
      />
      {/* Removed duplicate JSX */}
      <InternshipApply />
    </>
  );
};

export default DataScienceCourse;
