export const mockProjects = {
  active: [
    {
      id: 1,
      title: "E-commerce Platform",
      description: "Full-stack e-commerce application with React and Node.js",
      status: "in-progress",
      progress: 75,
      startDate: "2024-01-15",
      deadline: "2024-02-28",
      technologies: ["React", "Node.js", "MongoDB", "Express"],
      priority: "high",
      githubUrl: "https://github.com/user/ecommerce-platform",
      liveUrl: "https://ecommerce-demo.com",
      lastUpdated: "2024-01-20",
      timeSpent: "45 hours",
      estimatedTime: "60 hours"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "Collaborative task management tool with real-time updates",
      status: "in-progress",
      progress: 40,
      startDate: "2024-01-10",
      deadline: "2024-02-15",
      technologies: ["Vue.js", "Firebase", "Tailwind CSS"],
      priority: "medium",
      githubUrl: "https://github.com/user/task-manager",
      liveUrl: null,
      lastUpdated: "2024-01-18",
      timeSpent: "28 hours",
      estimatedTime: "70 hours"
    }
  ],
  completed: [
    {
      id: 3,
      title: "Portfolio Website",
      description: "Personal portfolio showcasing projects and skills",
      status: "completed",
      progress: 100,
      startDate: "2023-12-01",
      deadline: "2023-12-31",
      completedDate: "2023-12-28",
      technologies: ["React", "Tailwind CSS", "Framer Motion"],
      priority: "low",
      githubUrl: "https://github.com/user/portfolio",
      liveUrl: "https://myportfolio.com",
      lastUpdated: "2023-12-28",
      timeSpent: "35 hours",
      estimatedTime: "40 hours",
      rating: 4.8
    },
    {
      id: 4,
      title: "Weather Dashboard",
      description: "Real-time weather application with location-based forecasts",
      status: "completed",
      progress: 100,
      startDate: "2023-11-15",
      deadline: "2023-12-15",
      completedDate: "2023-12-10",
      technologies: ["JavaScript", "OpenWeather API", "Chart.js"],
      priority: "medium",
      githubUrl: "https://github.com/user/weather-dashboard",
      liveUrl: "https://weather-app-demo.com",
      lastUpdated: "2023-12-10",
      timeSpent: "25 hours",
      estimatedTime: "30 hours",
      rating: 4.5
    }
  ],
  paused: [
    {
      id: 5,
      title: "Social Media Analytics",
      description: "Analytics dashboard for social media performance tracking",
      status: "paused",
      progress: 25,
      startDate: "2023-12-20",
      deadline: "2024-03-01",
      technologies: ["Python", "Django", "PostgreSQL", "D3.js"],
      priority: "low",
      githubUrl: "https://github.com/user/social-analytics",
      liveUrl: null,
      lastUpdated: "2024-01-05",
      timeSpent: "15 hours",
      estimatedTime: "80 hours",
      pauseReason: "Waiting for API access approval"
    }
  ]
};
