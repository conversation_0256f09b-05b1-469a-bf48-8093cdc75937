import { useState } from "react";

const InterviewChecklist = ({ interviewChecklist, showPremiumOverlay }) => {
  const [activeIndex, setActiveIndex] = useState(null);

  const toggleAnswer = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  // CSS Animation Styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    @keyframes slideInLeft {
      from { opacity: 0; transform: translateX(-30px); }
      to { opacity: 1; transform: translateX(0); }
    }
    .checklist-container {
      opacity: 0;
      animation: fadeIn 0.6s ease forwards;
    }
    .checklist-header {
      opacity: 0;
      animation: slideInLeft 0.5s ease forwards 0.6s;
    }
    .checklist-icon {
      transition: transform 0.3s ease;
    }
    .checklist-icon:hover {
      transform: rotate(10deg) scale(1.1);
    }
  `;

  return (
    <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-6 shadow-lg border border-indigo-200 checklist-container">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      <div
        className="flex items-center gap-4 mb-6 pb-4 border-b border-gray-200 checklist-header"
        id="interview-checklist"
      >
        <i
          className="fas fa-clipboard-check text-2xl text-white p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl shadow-md checklist-icon"
        />
        <h2 className="text-2xl font-bold text-gray-800">
          Interview Checklist
        </h2>
      </div>

      <ul className="space-y-3">
        {interviewChecklist.map((item, index) => (
          <li
            key={index}
            className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden checklist-item`}
            style={{ 
              opacity: 0, 
              animation: `fadeIn 0.4s ease forwards ${0.8 + index * 0.05}s` 
            }}
          >
            <div
              className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200 hover:translate-x-1"
              onClick={() => toggleAnswer(index)}
            >
              <span className="font-semibold text-gray-800 flex-1">
                {index + 1}. {item.question}
              </span>
              <i
                className={`fas fa-chevron-down text-blue-600 ml-4 transform transition-transform duration-300 ${activeIndex === index ? 'rotate-180' : ''}`}
              />
            </div>
            
            <div
              className={`px-4 pb-4 transition-all duration-300 overflow-hidden ${activeIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}
            >
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-l-4 border-blue-500">
                <p className="text-gray-700 leading-relaxed">
                  {item.answer}
                </p>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default InterviewChecklist;
