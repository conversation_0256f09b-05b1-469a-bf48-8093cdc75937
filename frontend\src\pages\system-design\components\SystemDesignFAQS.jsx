import React, { useState } from "react";
import { motion } from "framer-motion";

const SystemDesignFAQS = ({ onBackToCourse }) => {
  const [activeIndex, setActiveIndex] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  const toggleFAQ = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const faqs = [
    {
      category: "Fundamentals",
      questions: [
        {
          question: "What is system design and why is it important?",
          answer: "System design is the process of defining the architecture, components, modules, interfaces, and data for a system to satisfy specified requirements. It's crucial for building scalable, reliable, and maintainable applications that can handle growth and complexity."
        },
        {
          question: "How do I approach a system design interview?",
          answer: "Start by clarifying requirements, estimate scale, design high-level architecture, deep dive into components, address scalability concerns, and discuss trade-offs. Always ask questions and think out loud to show your thought process."
        },
        {
          question: "What's the difference between horizontal and vertical scaling?",
          answer: "Horizontal scaling (scale-out) adds more servers to handle increased load, while vertical scaling (scale-up) increases the power of existing servers. Horizontal scaling is generally preferred for large-scale systems due to better fault tolerance and cost efficiency."
        }
      ]
    },
    {
      category: "Architecture Patterns",
      questions: [
        {
          question: "When should I use microservices vs monolithic architecture?",
          answer: "Use microservices for large, complex applications with multiple teams, need for independent deployments, and different technology requirements. Use monoliths for smaller teams, simpler applications, or when starting a new project where requirements aren't fully clear."
        },
        {
          question: "What is the difference between SQL and NoSQL databases?",
          answer: "SQL databases provide ACID compliance, strong consistency, and complex queries but may have scalability limitations. NoSQL databases offer better horizontal scalability, flexible schemas, and are optimized for specific use cases but may sacrifice some consistency guarantees."
        },
        {
          question: "How do I choose between different caching strategies?",
          answer: "Cache-aside is good for read-heavy workloads, write-through ensures data consistency, write-behind improves write performance, and refresh-ahead works well for predictable access patterns. Choose based on your consistency requirements, read/write patterns, and performance needs."
        }
      ]
    },
    {
      category: "Scalability & Performance",
      questions: [
        {
          question: "How do I design for high availability?",
          answer: "Use redundancy, eliminate single points of failure, implement health checks, use load balancers, deploy across multiple availability zones, implement circuit breakers, and have proper monitoring and alerting in place."
        },
        {
          question: "What are the trade-offs in the CAP theorem?",
          answer: "CAP theorem states you can only guarantee two of: Consistency, Availability, and Partition tolerance. In practice, partition tolerance is mandatory for distributed systems, so you choose between consistency (CP systems like traditional databases) or availability (AP systems like DNS, web caches)."
        },
        {
          question: "How do I handle database scaling issues?",
          answer: "Options include read replicas for read scaling, database sharding for write scaling, caching to reduce database load, connection pooling, query optimization, and considering NoSQL databases for specific use cases that don't require complex relationships."
        }
      ]
    },
    {
      category: "Security & Reliability",
      questions: [
        {
          question: "How do I secure my system design?",
          answer: "Implement authentication and authorization, use HTTPS/TLS, validate and sanitize inputs, implement rate limiting, use principle of least privilege, encrypt sensitive data, keep systems updated, and implement proper logging and monitoring."
        },
        {
          question: "What is eventual consistency and when should I use it?",
          answer: "Eventual consistency means the system will become consistent over time, but may be temporarily inconsistent. Use it when you can tolerate temporary inconsistencies in exchange for better availability and performance, such as in social media feeds, recommendation systems, or DNS."
        },
        {
          question: "How do I implement effective monitoring and alerting?",
          answer: "Monitor key metrics (latency, throughput, error rates, resource utilization), implement distributed tracing, set up meaningful alerts based on SLIs/SLOs, use centralized logging, implement health checks, and ensure observability at all system levels."
        }
      ]
    },
    {
      category: "Real-world Applications",
      questions: [
        {
          question: "How would you design a chat application like WhatsApp?",
          answer: "Key components: WebSocket connections for real-time messaging, message queues for reliability, database sharding by user ID, CDN for media files, push notification service, end-to-end encryption, load balancers, and caching for active conversations."
        },
        {
          question: "How do you design a URL shortener like bit.ly?",
          answer: "Components needed: base62 encoding for short URLs, database for URL mappings, caching for popular URLs, analytics service, rate limiting, custom aliases support, TTL for URL expiration, and geographic load balancing for global performance."
        },
        {
          question: "What are the challenges in designing a video streaming service?",
          answer: "Challenges include: content delivery via CDN, video encoding in multiple formats/qualities, adaptive bitrate streaming, geographic content distribution, handling peak loads, content recommendation systems, user authentication, and digital rights management."
        }
      ]
    }
  ];

  const filteredFAQs = faqs.map(category => ({
    ...category,
    questions: category.questions.filter(
      faq => 
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  return (
    <div className="py-8">
      <div className="max-w-4xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            ❓ System Design FAQs
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            Frequently Asked{" "}
            <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              Questions
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Get answers to common system design questions and clarify your doubts
          </p>
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-12"
        >
          <div className="relative">
            <input
              type="text"
              placeholder="Search FAQs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl px-6 py-4 text-white placeholder-white/50 focus:outline-none focus:border-blue-400 transition-all duration-300"
            />
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50">
              🔍
            </div>
          </div>
        </motion.div>

        {/* FAQ Categories */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="space-y-8"
        >
          {filteredFAQs.map((category, categoryIndex) => (
            <div key={category.category} className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <span className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-sm font-bold">
                  {categoryIndex + 1}
                </span>
                {category.category}
              </h3>
              
              <div className="space-y-4">
                {category.questions.map((faq, index) => {
                  const globalIndex = `${categoryIndex}-${index}`;
                  return (
                    <div
                      key={index}
                      className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg overflow-hidden"
                    >
                      <button
                        onClick={() => toggleFAQ(globalIndex)}
                        className="w-full px-6 py-4 text-left hover:bg-white/10 transition-all duration-300 flex items-center justify-between"
                      >
                        <span className="text-white font-medium pr-4">{faq.question}</span>
                        <span className={`text-white transition-transform duration-300 flex-shrink-0 ${
                          activeIndex === globalIndex ? 'rotate-180' : ''
                        }`}>
                          ▼
                        </span>
                      </button>
                      
                      {activeIndex === globalIndex && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="px-6 pb-6"
                        >
                          <div className="pt-4 border-t border-white/10">
                            <p className="text-white/80 leading-relaxed">{faq.answer}</p>
                          </div>
                        </motion.div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </motion.div>

        {/* No Results */}
        {filteredFAQs.length === 0 && searchTerm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold text-white mb-2">No results found</h3>
            <p className="text-white/70">Try searching with different keywords</p>
          </motion.div>
        )}

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-lg border border-white/10 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              💬 Still have questions?
            </h3>
            <p className="text-white/80 mb-6">
              Can't find what you're looking for? Our expert instructors are here to help!
            </p>
            <button className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105">
              Ask an Expert
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default SystemDesignFAQS;
