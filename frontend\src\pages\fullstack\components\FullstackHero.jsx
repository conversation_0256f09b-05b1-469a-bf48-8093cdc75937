import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const FullstackHero = ({ showPremiumOverlay }) => {
  return (
    <motion.section
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      className="relative overflow-hidden bg-gradient-to-br from-[#1a3c50] to-[#010509] text-white w-full min-h-screen flex items-center justify-center"
    >
      {/* Complex animated background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full opacity-10 blur-3xl"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 80, 0],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full opacity-10 blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.sin(i) * 20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className={`absolute w-2 h-2 bg-white rounded-full opacity-30`}
            style={{
              top: `${20 + i * 10}%`,
              left: `${10 + i * 12}%`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 md:px-6 py-20 pt-36">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center lg:text-left"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="inline-block px-6 py-3 bg-white/10 border border-white/20 rounded-full text-white/90 text-sm font-medium mb-8 backdrop-blur-sm"
            >
              💻 Master Full Stack Development
            </motion.div>

            <motion.h1
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight"
            >
              Full Stack{" "}
              <span className="bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 bg-clip-text text-transparent"
                style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                Development
              </span>{" "}
              Course
            </motion.h1>

            <motion.p
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="text-lg md:text-xl lg:text-2xl text-white/90 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0"
            >
              From frontend to backend, master modern web development with hands-on projects, 
              industry-standard practices, and cutting-edge technologies.
              Become a complete developer today.
            </motion.p>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center mb-12"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <button
                  onClick={showPremiumOverlay}
                  className="bg-[#303246]/60 backdrop-blur-lg text-white px-8 py-4 rounded-xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-white/10 inline-flex items-center"
                  style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}
                >
                  <span className="relative z-10">Get Premium Access</span>
                  <motion.span
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="ml-2 relative z-10"
                  >
                    🚀
                  </motion.span>
                </button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="#fullstack-content"
                  className="border-2 border-white/30 backdrop-blur-lg text-white px-8 py-4 rounded-xl font-bold hover:text-blue-300 transition-all duration-300 transform hover:scale-105 inline-flex items-center"
                  style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}
                >
                  <span className="mr-2">📚</span>
                  Explore Content
                </Link>
              </motion.div>
            </motion.div>

            {/* Trust indicators */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-wrap items-center justify-center lg:justify-start gap-6 text-white/70"
            >
              <div className="flex items-center gap-2">
                <span className="text-yellow-300">⭐</span>
                <span className="text-sm">Complete MERN Stack</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-300">✓</span>
                <span className="text-sm">Real-World Projects</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-300">🎯</span>
                <span className="text-sm">Job-Ready Skills</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Interactive Elements */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Code window mockup */}
            <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-lg border border-white/10 rounded-2xl overflow-hidden shadow-2xl">
              <div className="flex items-center gap-2 px-4 py-3 bg-[#1e293b]/30 border-b border-white/10">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="ml-2 text-xs text-blue-100/80">
                  app.js
                </span>
              </div>
              <div className="p-6 font-mono text-sm">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2 }}
                >
                  <div className="text-blue-400">import</div>
                  <div className="text-white ml-1">
                    React, {'{useState, useEffect}'} from 'react';
                  </div>
                  <div className="text-blue-400">
                    import
                  </div>
                  <div className="text-white ml-1">
                    {'{Route, Switch}'} from 'react-router-dom';
                  </div>
                  <div className="text-green-400 mt-2">
                    // Full stack components
                  </div>
                  <div className="text-blue-400">
                    const
                  </div>
                  <div className="text-yellow-400 ml-1">
                    App = () =&gt; {"{"}
                  </div>
                  <div className="text-pink-400 ml-4">
                    return &lt;div&gt;Full Stack Developer Journey!&lt;/div&gt;;
                  </div>
                  <div className="text-yellow-400 ml-1">
                    {"}"};
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Floating achievement cards */}
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="absolute -top-4 -right-4 bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-4 rounded-xl shadow-xl text-white text-center"
            >
              <div className="text-2xl font-bold" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>15+</div>
              <div className="text-xs text-blue-100/80">Projects</div>
            </motion.div>

            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 4, repeat: Infinity, delay: 0.5 }}
              className="absolute -bottom-4 -left-4 bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-4 rounded-xl shadow-xl text-white text-center"
            >
              <div className="text-2xl font-bold" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>24/7</div>
              <div className="text-xs text-blue-100/80">Support</div>
            </motion.div>
          </motion.div>
        </div>

        {/* Enhanced Stats Section */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.0 }}
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            {
              number: "20+",
              label: "Modules",
              icon: "📦",
              color: "from-[#1e293b]/10 to-transparent",
            },
            {
              number: "50+",
              label: "Exercises",
              icon: "💻",
              color: "from-[#1e293b]/10 to-transparent",
            },
            {
              number: "15+",
              label: "Projects",
              icon: "🚀",
              color: "from-[#1e293b]/10 to-transparent",
            },
            {
              number: "∞",
              label: "Opportunities",
              icon: "🌟",
              color: "from-[#1e293b]/10 to-transparent",
            },
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 1.2 + index * 0.1, duration: 0.5 }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl p-6 text-center shadow-lg"
            >
              <div className="text-3xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-bold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                {stat.number}
              </div>
              <div className="text-sm text-blue-100/80">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default FullstackHero;
