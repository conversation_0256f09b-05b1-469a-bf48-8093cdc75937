import React from "react";
import { CourseResourcesSection } from "../../../components/ui";
import useSidebarState from "../../../hooks/useSidebarState";
import PythonFullStackHero from "./components/PythonFullStackHero";
import FullstackLayout from "../components/FullstackLayout";
import FullstackPremiumModal from "../components/FullstackPremiumModal";
import PythonFullStackIntroduction from "./components/PythonFullStackIntroduction";
import PythonFullStackLabEnvironment from "./components/PythonFullStackLabEnvironment";
import PythonFullStackLiveClasses from "./components/PythonFullStackLiveClasses";
import PythonFullStackFAQS from "./components/PythonFullStackFAQS";

const PythonFullStack = () => {
  const courseConfig = {
    title: "Python Full Stack Development",
    subtitle: "Master front-end, back-end, and database technologies with Python and modern web frameworks",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn Python full stack fundamentals",
        icon: "📚",
        component: PythonFullStackIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Practice Lab",
        description: "Build full stack applications with Python",
        icon: "💻",
        component: PythonFullStackLabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Projects",
        description: "Work on real-world Python projects",
        icon: "🚀",
        component: PythonFullStackLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common questions",
        icon: "❓",
        component: PythonFullStackFAQS,
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={PythonFullStackHero}
      LayoutComponent={FullstackLayout}
      PremiumModalComponent={FullstackPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default PythonFullStack;