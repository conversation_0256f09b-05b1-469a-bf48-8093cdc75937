import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import upcodingLogo from "../../assets/upcoding_logo.svg";

const Footer = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 },
    },
  };

  return (
    <footer className="bg-[#1a293b] text-white pt-1">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="container mx-auto px-6 py-6 "
      >
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Company Info */}
          <motion.div variants={itemVariants} className="space-y-6">
            <div className="flex flex-col">
              <div className="flex items-center">
                <img
                  src={upcodingLogo}
                  alt="UpCoding"
                  className="h-16 w-auto object-contain"
                />
                <div className="leading-tight ml-2">
                  <p className="text-sm text-gray-300 leading-snug">
                    Empowering Tech Careers
                  </p>
                </div>
              </div>
              <div className="text-xs font-medium px-2 py-0.5 mt-2 self-start inline-flex bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-md backdrop-blur-sm border border-blue-300/30 text-white">
                A product by codexuslabs
              </div>
            </div>

            <p className="text-gray-300 leading-relaxed">
              Transform your career with industry-leading courses in AI, Machine
              Learning, System Design, and more. Join thousands of professionals
              who've accelerated their growth with us.
            </p>
            <div className="flex space-x-4">
              {[
                {
                  icon: "📧",
                  href: "mailto:<EMAIL>",
                  label: "Email",
                },
                { icon: "💼", href: "#", label: "LinkedIn" },
                { icon: "🐦", href: "#", label: "Twitter" },
                { icon: "📱", href: "#", label: "GitHub" },
              ].map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  whileHover={{ scale: 1.2, y: -2 }}
                  className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-lg hover:bg-white/20 transition-colors"
                  aria-label={social.label}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={itemVariants}>
            <h4 className="text-xl font-bold mb-6 text-white">Quick Links</h4>
            <ul className="space-y-3">
              {[
                { name: "Home", href: "/" },
                { name: "About Us", href: "/about" },
                { name: "Courses", href: "/courses" },
                { name: "Pricing", href: "/pricing" },
                { name: "Contact", href: "/contact" },
                { name: "Blog", href: "/blog" },
              ].map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-white transition-colors hover:underline"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Learning Paths */}
          <motion.div variants={itemVariants}>
            <h4 className="text-xl font-bold mb-6 text-white">
              Learning Paths
            </h4>
            <ul className="space-y-3">
              {[
                { name: "Python Development", href: "/pythoncourse" },
                { name: "Full Stack Development", href: "/fullstack-course" },
                { name: "Data Science", href: "/datascience-course" },
                { name: "AI & Machine Learning", href: "/ai-course" },
                { name: "System Design", href: "/sys_des_for_int" },
                { name: "Data Structures", href: "/data_strut" },
                { name: "Interview Prep", href: "/interview_150" },
                { name: "Web Development", href: "/web-dev" },
              ].map((course, index) => (
                <li key={index}>
                  <Link
                    to={course.href}
                    className="text-gray-300 hover:text-white transition-colors hover:underline"
                  >
                    {course.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact & Newsletter */}
          <motion.div variants={itemVariants}>
            <h4 className="text-xl font-bold mb-6 text-white">
              Stay Connected
            </h4>
            <div className="space-y-6">
              <div>
                <p className="text-gray-300 mb-4">
                  Get the latest updates on new courses and career
                  opportunities.
                </p>
                <div className="flex flex-col gap-3">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                  >
                    Subscribe
                  </motion.button>
                </div>
              </div>

              <div className="pt-4 border-t border-white/10">
                <p className="text-gray-300 text-sm mb-2">📍 Address:</p>
                <p className="text-white text-sm">
                  Bangalore, Karnataka, India
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Achievement Stats */}
        <motion.div
          variants={itemVariants}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 py-8 border-y border-white/10 mb-8"
        >
          {[
            { number: "10K+", label: "Students Enrolled", icon: "👨‍🎓" },
            { number: "15+", label: "Expert Courses", icon: "📚" },
            { number: "95%", label: "Job Placement", icon: "💼" },
            { number: "24/7", label: "Support Available", icon: "🛟" },
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-2xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-bold text-white">{stat.number}</div>
              <div className="text-sm text-gray-300">{stat.label}</div>
            </div>
          ))}
        </motion.div>

        {/* Success Stories Ticker */}
        <motion.div
          variants={itemVariants}
          className="mt-8 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-2xl p-6 border border-blue-500/20"
        >
          <div className="text-center">
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="text-sm text-gray-300 mb-2"
            >
              🎉 Latest Success Story
            </motion.p>
            <motion.p
              animate={{
                opacity: [0.7, 1, 0.7],
                scale: [1, 1.02, 1],
              }}
              transition={{ duration: 3, repeat: Infinity }}
              className="text-white font-medium"
            >
              "Just landed my dream job at Google thanks to Upcoding! The
              system design course was incredible." - Sarah M.
            </motion.p>
          </div>
        </motion.div>

        {/* Bottom Footer */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col md:flex-row justify-between items-center pt-8 border-t border-white/10"
        >
          <div className="flex items-center gap-4 text-gray-300 text-sm mb-4 md:mb-0">
            <img
              src="/images/logonew.png"
              alt="Upcoding"
              className="w-8 h-8"
            />
            <p>
              © 2025 Codexus Labs AI Solutions Pvt Ltd. All rights reserved.
              Empowering the next generation of tech professionals.
            </p>
          </div>

          <div className="flex space-x-6 text-sm">
            <Link
              to="/privacy"
              className="text-gray-300 hover:text-white transition-colors hover:underline"
            >
              Privacy Policy
            </Link>
            <Link
              to="/terms"
              className="text-gray-300 hover:text-white transition-colors hover:underline"
            >
              Terms of Service
            </Link>
            <Link
              to="/cookies"
              className="text-gray-300 hover:text-white transition-colors hover:underline"
            >
              Cookie Policy
            </Link>
            <Link
              to="/sitemap"
              className="text-gray-300 hover:text-white transition-colors hover:underline"
            >
              Sitemap
            </Link>
          </div>
        </motion.div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            animate={{
              x: [0, 50, 0],
              y: [0, -30, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            }}
            className="absolute top-10 right-10 w-32 h-32 bg-blue-400/5 rounded-full"
          />
          <motion.div
            animate={{
              x: [0, -30, 0],
              y: [0, 40, 0],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: "linear",
            }}
            className="absolute bottom-10 left-10 w-24 h-24 bg-purple-400/5 rounded-full"
          />
          <motion.div
            animate={{
              x: [0, 20, 0],
              y: [0, -20, 0],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear",
            }}
            className="absolute top-1/2 left-1/4 w-16 h-16 bg-indigo-400/5 rounded-full"
          />
        </div>
      </motion.div>
    </footer>
  );
};

export default Footer;
