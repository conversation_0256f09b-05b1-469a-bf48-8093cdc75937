import React from "react";
import { motion } from "framer-motion";

const DSALiveClasses = ({ onBackToCourse, showPremiumOverlay }) => {
  const interviewTopics = [
    {
      title: "Array & String Interview Questions",
      description: "Common array and string problems asked in technical interviews",
      date: "Next Session: Tomorrow, 7:00 PM",
      icon: "📊"
    },
    {
      title: "Linked List & Tree Problems",
      description: "Advanced data structure questions with optimal solutions",
      date: "Next Session: Wednesday, 6:30 PM",
      icon: "🔗"
    },
    {
      title: "Dynamic Programming Deep Dive",
      description: "Mastering DP approaches for complex interview problems",
      date: "Next Session: Friday, 7:00 PM",
      icon: "📈"
    },
    {
      title: "System Design for DSA Engineers",
      description: "How to approach system design questions in interviews",
      date: "Next Session: Saturday, 11:00 AM",
      icon: "🏗️"
    }
  ];

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-900/70 to-red-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Interview Preparation</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Ace Your Technical Interviews</h3>
          <p className="text-gray-300">
            Prepare for technical interviews with our specialized DSA sessions. Learn problem-solving 
            strategies and practice with mock interviews.
          </p>
        </div>
        
        {/* Interview Topics */}
        <div className="space-y-4">
          {interviewTopics.map((topic, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start p-4 border border-gray-700/30 bg-gray-800/30 rounded-lg hover:bg-gray-700/40 transition-colors cursor-pointer"
              onClick={showPremiumOverlay}
            >
              <div className="text-3xl mr-4">{topic.icon}</div>
              <div>
                <h4 className="font-medium text-white">{topic.title}</h4>
                <p className="text-sm text-gray-300 mb-1">{topic.description}</p>
                <p className="text-xs text-blue-600 font-medium">{topic.date}</p>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Mock Interview Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8 bg-gray-800/30 border border-gray-700/30 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Mock Interview Sessions</h3>
          <p className="text-gray-600 mb-4">
            Practice with real interview scenarios and get feedback from experienced engineers.
          </p>
          <button 
            onClick={showPremiumOverlay}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            Schedule a Mock Interview
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default DSALiveClasses;