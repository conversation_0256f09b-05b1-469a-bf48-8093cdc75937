import React, { useState } from "react";
import ReusableCodeEditor from "../../../components/ui/ReusableCodeEditor";

const MLLabEnvironmentNew = ({ showPremiumOverlay }) => {
  const [activeTab, setActiveTab] = useState("setup");

  const tabs = [
    { id: "setup", label: "Environment Setup", icon: "fas fa-cog" },
    { id: "jupyter", label: "Jupyter Notebooks", icon: "fas fa-book-open" },
    { id: "datasets", label: "Datasets & Tools", icon: "fas fa-database" },
    { id: "codeEditor", label: "Code Editor", icon: "fas fa-code" }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "setup":
        return <EnvironmentSetup />;
      case "jupyter":
        return <JupyterNotebooks showPremiumOverlay={showPremiumOverlay} />;
      case "datasets":
        return <DatasetsAndTools showPremiumOverlay={showPremiumOverlay} />;
      case "codeEditor":
        return <ReusableCodeEditor showPremiumOverlay={showPremiumOverlay} />;
      default:
        return <EnvironmentSetup />;
    }
  };

  // Animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md overflow-hidden text-white">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1e293b]/20 to-[#0f172a]/20">
        <h2 className="text-3xl font-bold mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>ML Lab Environment</h2>
        <p className="text-blue-100/80">Set up your machine learning environment with Jupyter notebooks, datasets, and essential tools</p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-white/10 flex">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-colors duration-200 relative ${
              activeTab === tab.id
                ? "text-purple-400 border-b-2 border-purple-400"
                : "text-white/70 hover:text-white"
            }`}
          >
            <i className={`${tab.icon} mr-2`}></i>
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Content */}
      <div className="p-6 fade-in">
        {renderContent()}
      </div>
    </div>
  );
};

// Environment Setup Component
const EnvironmentSetup = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Setting Up Your ML Environment</h3>
      
      <div className="rounded-lg bg-[#303246]/30 backdrop-blur-sm p-6 border border-white/10">
        <h4 className="text-xl font-semibold mb-3 text-purple-300">Why Environment Setup Matters</h4>
        <p className="text-white/80">
          A proper ML environment ensures reproducible experiments, easy package management, and access to GPU acceleration for deep learning models.
        </p>
      </div>
      
      <div className="space-y-8 mt-6">
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-purple-500/30 text-purple-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">1</span>
            Install Python & Anaconda
          </h4>
          <p className="text-white/80 mb-4">
            Download Anaconda distribution which includes Python, Jupyter, and essential ML libraries pre-installed.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Verify installation<br />
              conda --version<br />
              python --version<br />
              jupyter --version
            </code>
          </div>
        </div>

        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-purple-500/30 text-purple-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">2</span>
            Essential ML Libraries
          </h4>
          <p className="text-white/80 mb-4">
            Install core machine learning and data science libraries for your projects.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              pip install numpy pandas scikit-learn matplotlib seaborn<br />
              pip install tensorflow keras pytorch<br />
              pip install jupyter jupyterlab
            </code>
          </div>
        </div>

        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10">
          <h4 className="flex items-center text-lg font-semibold text-white mb-3">
            <span className="bg-purple-500/30 text-purple-300 w-8 h-8 rounded-full flex items-center justify-center mr-3">3</span>
            GPU Setup (Optional)
          </h4>
          <p className="text-white/80 mb-4">
            Configure CUDA for GPU acceleration in deep learning tasks.
          </p>
          <div className="bg-[#0f172a] text-green-400 rounded-md p-4 overflow-x-auto">
            <code>
              # Check GPU availability<br />
              nvidia-smi<br />
              # Install CUDA-enabled TensorFlow<br />
              pip install tensorflow-gpu
            </code>
          </div>
        </div>
      </div>
    </div>
  );
};

// Jupyter Notebooks Component
const JupyterNotebooks = ({ showPremiumOverlay }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white">Interactive Jupyter Notebooks</h3>
      
      <div className="grid md:grid-cols-2 gap-6">
        {[
          {
            title: "Linear Regression",
            description: "Implement linear regression from scratch",
            difficulty: "Beginner",
            icon: "📈"
          },
          {
            title: "Neural Networks",
            description: "Build your first neural network",
            difficulty: "Intermediate",
            icon: "🧠"
          },
          {
            title: "Computer Vision",
            description: "Image classification with CNNs",
            difficulty: "Advanced",
            icon: "👁️"
          },
          {
            title: "NLP Projects",
            description: "Text processing and sentiment analysis",
            difficulty: "Intermediate",
            icon: "📝"
          }
        ].map((notebook, index) => (
          <div key={index} className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-6 shadow-sm border border-white/10 hover:border-purple-400/50 transition-all duration-300">
            <div className="text-3xl mb-3">{notebook.icon}</div>
            <h4 className="text-lg font-semibold text-white mb-2">{notebook.title}</h4>
            <p className="text-white/80 mb-3">{notebook.description}</p>
            <div className="flex items-center justify-between">
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                notebook.difficulty === "Beginner" ? "bg-green-500/20 text-green-300" :
                notebook.difficulty === "Intermediate" ? "bg-yellow-500/20 text-yellow-300" :
                "bg-red-500/20 text-red-300"
              }`}>
                {notebook.difficulty}
              </span>
              <button
                onClick={showPremiumOverlay}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 text-sm"
              >
                Launch Notebook
              </button>
            </div>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <button
          onClick={showPremiumOverlay}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <span className="mr-2">🚀</span>
          Access All Notebooks
          <span className="ml-2">→</span>
        </button>
      </div>
    </div>
  );
};

// Datasets and Tools Component
const DatasetsAndTools = ({ showPremiumOverlay }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4 text-white">Datasets & ML Tools</h3>
      
      <div className="grid md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-4 border border-white/10">
          <h4 className="font-semibold text-white mb-2">📊 Popular Datasets</h4>
          <ul className="text-white/80 text-sm space-y-1">
            <li>• Iris Dataset</li>
            <li>• Housing Prices</li>
            <li>• MNIST Digits</li>
            <li>• Titanic Dataset</li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-4 border border-white/10">
          <h4 className="font-semibold text-white mb-2">🛠️ ML Tools</h4>
          <ul className="text-white/80 text-sm space-y-1">
            <li>• Scikit-learn</li>
            <li>• TensorFlow</li>
            <li>• PyTorch</li>
            <li>• XGBoost</li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-lg p-4 border border-white/10">
          <h4 className="font-semibold text-white mb-2">📈 Visualization</h4>
          <ul className="text-white/80 text-sm space-y-1">
            <li>• Matplotlib</li>
            <li>• Seaborn</li>
            <li>• Plotly</li>
            <li>• Bokeh</li>
          </ul>
        </div>
      </div>
      
      <div className="text-center mt-8">
        <button
          onClick={showPremiumOverlay}
          className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <span className="mr-2">🔧</span>
          Access Premium Tools
          <span className="ml-2">→</span>
        </button>
      </div>
    </div>
  );
};

export default MLLabEnvironmentNew;