import { motion } from "framer-motion";
import { 
  FaRocket, 
  FaUsers, 
  FaLightbulb, 
  FaGraduationCap,
  FaCode,
  FaChartLine,
  FaHandshake,
  FaGlobe,
  FaHeart,
  FaShieldAlt,
  FaAward,
  FaInfinity
} from "react-icons/fa";

const AboutUs = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  const values = [
    {
      icon: <FaLightbulb />,
      title: "Innovation",
      description: "We constantly push the boundaries of technology and education to create cutting-edge learning experiences.",
      color: "from-yellow-500 to-orange-500"
    },
    {
      icon: <FaUsers />,
      title: "Community",
      description: "Building a supportive global community where learners and educators collaborate and grow together.",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <FaGraduationCap />,
      title: "Excellence",
      description: "Committed to delivering the highest quality education and maintaining exceptional standards.",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: <FaShieldAlt />,
      title: "Integrity",
      description: "Operating with transparency, honesty, and ethical practices in everything we do.",
      color: "from-green-500 to-emerald-500"
    }
  ];

  const achievements = [
    {
      icon: <FaUsers />,
      number: "50K+",
      label: "Active Learners",
      description: "Students worldwide trust our platform"
    },
    {
      icon: <FaCode />,
      number: "1000+",
      label: "Coding Challenges",
      description: "Comprehensive programming exercises"
    },
    {
      icon: <FaGraduationCap />,
      number: "95%",
      label: "Success Rate",
      description: "Students achieve their learning goals"
    },
    {
      icon: <FaAward />,
      number: "25+",
      label: "Industry Awards",
      description: "Recognition for educational excellence"
    }
  ];

  const team = [
    {
      name: "Sarah Johnson",
      role: "CEO & Founder",
      description: "Visionary leader with 15+ years in EdTech",
      image: "👩‍💼"
    },
    {
      name: "Michael Chen",
      role: "CTO",
      description: "Tech innovator specializing in AI and ML",
      image: "👨‍💻"
    },
    {
      name: "Emily Rodriguez",
      role: "Head of Education",
      description: "Curriculum expert with PhD in Computer Science",
      image: "👩‍🏫"
    },
    {
      name: "David Kim",
      role: "Head of Community",
      description: "Community builder and developer advocate",
      image: "👨‍🎓"
    }
  ];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="py-16 px-4 md:px-6 lg:px-8"
    >
      <div className="max-w-6xl mx-auto">
        {/* Mission Statement */}
        <motion.div variants={itemVariants} className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <FaRocket className="text-2xl text-white" />
            </div>
            <h2 className="text-4xl font-bold text-white">Our Mission</h2>
          </div>
          <div className="bg-gradient-to-br from-slate-800/90 via-blue-900/80 to-purple-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500">
            <p className="text-xl text-slate-200 leading-relaxed max-w-4xl mx-auto">
              We are dedicated to democratizing technology education by providing world-class, 
              accessible, and practical learning experiences. Our mission is to empower individuals 
              from all backgrounds to master programming, data science, AI, and emerging technologies 
              through innovative, hands-on learning approaches.
            </p>
          </div>
        </motion.div>

        {/* Our Story */}
        <motion.div variants={itemVariants} className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Our Story</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl p-8 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-500">
              <div className="flex items-center gap-3 mb-4">
                <FaInfinity className="text-2xl text-blue-400" />
                <h3 className="text-xl font-bold text-white">The Beginning</h3>
              </div>
              <p className="text-slate-300 leading-relaxed">
                Founded in 2020 by a team of passionate educators and technologists, we started with 
                a simple vision: make quality tech education accessible to everyone. What began as a 
                small online platform has grown into a comprehensive learning ecosystem serving 
                thousands of students worldwide.
              </p>
            </div>

            <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl p-8 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-500">
              <div className="flex items-center gap-3 mb-4">
                <FaChartLine className="text-2xl text-green-400" />
                <h3 className="text-xl font-bold text-white">Our Growth</h3>
              </div>
              <p className="text-slate-300 leading-relaxed">
                Today, we're proud to be a leading platform in tech education, offering courses in 
                programming, data science, AI, and more. Our community-driven approach and 
                industry-relevant curriculum have helped thousands of students launch successful 
                careers in technology.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Core Values */}
        <motion.div variants={itemVariants} className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Our Core Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-500 group"
              >
                <div className={`w-12 h-12 bg-gradient-to-r ${value.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <span className="text-white text-xl">{value.icon}</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">{value.title}</h3>
                <p className="text-slate-300 text-sm leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Achievements */}
        <motion.div variants={itemVariants} className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Our Achievements</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-gradient-to-br from-blue-900/60 via-purple-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-blue-500/30 hover:border-purple-400/50 transition-all duration-500 text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">{achievement.icon}</span>
                </div>
                <div className="text-3xl font-bold text-white mb-2">{achievement.number}</div>
                <div className="text-lg font-semibold text-blue-200 mb-2">{achievement.label}</div>
                <p className="text-slate-300 text-sm">{achievement.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Team Section */}
        <motion.div variants={itemVariants} className="mb-16">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Meet Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {team.map((member, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-500 text-center group"
              >
                <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {member.image}
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{member.name}</h3>
                <div className="text-blue-400 font-semibold mb-3">{member.role}</div>
                <p className="text-slate-300 text-sm leading-relaxed">{member.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div variants={itemVariants} className="text-center">
          <div className="bg-gradient-to-br from-slate-800/90 via-blue-900/80 to-purple-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500">
            <div className="flex items-center justify-center gap-3 mb-6">
              <FaHandshake className="text-3xl text-blue-400" />
              <h2 className="text-3xl font-bold text-white">Get in Touch</h2>
            </div>
            <p className="text-xl text-slate-200 mb-8 max-w-2xl mx-auto">
              Have questions about our mission or want to learn more about our programs? 
              We'd love to hear from you!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold">
                Contact Us
              </button>
              <button className="px-8 py-4 bg-slate-700/50 hover:bg-slate-600/50 text-white rounded-xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300 font-semibold">
                Join Our Newsletter
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default AboutUs;
