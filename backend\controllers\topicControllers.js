import Topic from "../models/topicModal.js";
import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import <PERSON>rrorHandler from "../utils/ErrorHandler.js";

export const createTopic = CatchAsyncError(async (req, res, next) => {
  try {
    const { title, icon, order } = req.body;
    if (!title) {
      return next(new ErrorHandler("Title is required"));
    }
    const topic = new Topic({ title, icon, order });
    await topic.save();
    res.status(201).json({ success: true, topic });
  } catch (error) {
    res.status(500).json({
      message: error.message || "Something went wrong",
    });
  }
});

// GET: Get all active topics
export const getAllTopics = CatchAsyncError(async (req, res, next) => {
  const topics = await Topic.find({ isActive: true }).sort({ order: 1 });

  res.status(200).json({
    success: true,
    topics,
  });
});

// PUT: Update a topic by ID
export const updateTopics = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  const { title, icon, order, isActive } = req.body;

  const topic = await Topic.findById(id);
  if (!topic) {
    return next(new ErrorHandler("Topic not found", 404));
  }

  if (title !== undefined) topic.title = title;
  if (icon !== undefined) topic.icon = icon;
  if (order !== undefined) topic.order = order;
  if (isActive !== undefined) topic.isActive = isActive;

  await topic.save();

  res.status(200).json({
    success: true,
    message: "Topic updated successfully",
    topic,
  });
});

export const deleteTopic = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  const topic = await Topic.findByIdAndDelete(id);
  if (!topic) {
    return next(new ErrorHandler("Topic not found", 404));
  }

  res.status(200).json({
    success: true,
    message: "Topic deleted successfully",
  });
});
