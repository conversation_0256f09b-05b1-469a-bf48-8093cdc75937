import { useState } from "react";
import { motion } from "framer-motion";
import TopicGridNew from "./components/case-studies/TopicGridNew";
import CaseStudySectionNew from "./components/case-studies/CaseStudySectionNew";
import { 
  topics,
  supervisedLearningStudies,
  unsupervisedLearningStudies,
  reinforcementLearningStudies,
  deepLearningStudies,
  nlpStudies,
  featureEngineeringStudies,
  modelEvaluationStudies,
  ensembleLearningStudies,
  dimensionalityReductionStudies,
  timeSeriesStudies
} from "./components/case-studies/data/caseStudiesData";

const CaseStudyNew = () => {
  // State for each section
  const [activeIndex, setActiveIndex] = useState(null);
  const [showSolutions, setShowSolutions] = useState(Array(supervisedLearningStudies.length).fill(false));
  
  // State for other sections
  const [activeStudy, setActiveStudy] = useState(null);
  const [showSolution, setShowSolution] = useState(Array(10).fill(false));
  
  // State for expanded sections
  const [expandedSections, setExpandedSections] = useState({});

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      // Expand the section when scrolled to
      const sectionTitle = topics.find(topic => topic.sectionId === sectionId)?.title;
      if (sectionTitle) {
        setExpandedSections(prev => ({
          ...prev,
          [sectionTitle]: true
        }));
      }
    }
  };

  const toggleSolution = (index) => {
    const updatedSolutions = [...showSolutions];
    updatedSolutions[index] = !updatedSolutions[index];
    setShowSolutions(updatedSolutions);
  };

  const toggleChapterContent = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const toggleContent = (index) => {
    setActiveStudy(activeStudy === index ? null : index);
  };

  const toggleSolutionTwo = (index) => {
    const updatedSolutions = [...showSolution];
    updatedSolutions[index] = !updatedSolutions[index];
    setShowSolution(updatedSolutions);
  };
  
  const toggleSection = (sectionTitle) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <motion.div 
      className="py-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        {/* Header Section */}
        <motion.div variants={itemVariants} className="text-center mb-12">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-block px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg"
          >
            🧠 Machine Learning Case Studies
          </motion.div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
            Practical{" "}
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              ML Case Studies
            </span>
          </h2>
          
          <p className="text-xl text-blue-100/80 max-w-3xl mx-auto leading-relaxed mb-4">
            Learn machine learning through real-world scenarios and hands-on practice
          </p>
          
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md p-6 max-w-2xl mx-auto"
          >
            <h3 className="text-lg font-semibold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
              🎯 Ready to dive in?
            </h3>
            <p className="text-blue-100/80">
              Click on any topic below to explore comprehensive case studies and start your learning journey!
            </p>
          </motion.div>
        </motion.div>

        {/* Topics Grid */}
        <motion.div variants={itemVariants} className="mb-12">
          <TopicGridNew topics={topics} scrollToSection={scrollToSection} />
        </motion.div>

      <motion.div variants={itemVariants} className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md p-8 md:p-12 mb-8">
        {/* Supervised Learning */}
        <CaseStudySectionNew
          title="Supervised Learning"
          studies={supervisedLearningStudies}
          activeIndex={activeIndex}
          showSolutions={showSolutions}
          toggleContent={toggleChapterContent}
          toggleSolution={toggleSolution}
          isExpanded={expandedSections["Supervised Learning"]}
          toggleSection={toggleSection}
        />

        {/* Unsupervised Learning */}
        <CaseStudySectionNew
          title="Unsupervised Learning"
          studies={unsupervisedLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Unsupervised Learning"]}
          toggleSection={toggleSection}
        />

        {/* Reinforcement Learning */}
        <CaseStudySectionNew
          title="Reinforcement Learning"
          studies={reinforcementLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Reinforcement Learning"]}
          toggleSection={toggleSection}
        />

        {/* Deep Learning */}
        <CaseStudySectionNew
          title="Deep Learning"
          studies={deepLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Deep Learning"]}
          toggleSection={toggleSection}
        />

        {/* NLP */}
        <CaseStudySectionNew
          title="Natural Language Processing (NLP)"
          studies={nlpStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Natural Language Processing (NLP)"]}
          toggleSection={toggleSection}
        />

        {/* Feature Engineering */}
        <CaseStudySectionNew
          title="Feature Engineering"
          studies={featureEngineeringStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Feature Engineering"]}
          toggleSection={toggleSection}
        />

        {/* Model Evaluation */}
        <CaseStudySectionNew
          title="Model Evaluation and Validation"
          studies={modelEvaluationStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Model Evaluation and Validation"]}
          toggleSection={toggleSection}
        />

        {/* Ensemble Learning */}
        <CaseStudySectionNew
          title="Ensemble Learning"
          studies={ensembleLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Ensemble Learning"]}
          toggleSection={toggleSection}
        />

        {/* Dimensionality Reduction */}
        <CaseStudySectionNew
          title="Dimensionality Reduction"
          studies={dimensionalityReductionStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Dimensionality Reduction"]}
          toggleSection={toggleSection}
        />

        {/* Time Series */}
        <CaseStudySectionNew
          title="Time Series Analysis and Forecasting"
          studies={timeSeriesStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Time Series Analysis and Forecasting"]}
          toggleSection={toggleSection}
        />
      </motion.div>
    </div>
    </motion.div>
  );
};

export default CaseStudyNew;