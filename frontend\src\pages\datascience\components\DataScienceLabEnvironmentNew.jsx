import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>b,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SiRequests
} from "./DataScienceLabEnvironment.icons";
import JupyterNotebookEditor from "./JupyterNotebookEditor";
import GoogleColabEditor from "./GoogleColabEditor";
import LocalSetupEditor from "./LocalSetupEditor";

const DataScienceLabEnvironmentNew = ({ showPremiumOverlay, onBackToCourse }) => {
  const [activeTab, setActiveTab] = useState("environment");
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const handleBackToEnvironment = () => {
    setActiveTab("environment");
  };

  const renderContent = () => {
    switch (activeTab) {
      case "environment":
        return <EnvironmentSetup showPremiumOverlay={showPremiumOverlay} setActiveTab={setActiveTab} />;
      case "notebook":
        return <JupyterNotebookEditor onBack={handleBackToEnvironment} />;
      case "colab":
        return <GoogleColabEditor onBack={handleBackToEnvironment} />;
      case "local":
        return <LocalSetupEditor onBack={handleBackToEnvironment} />;
      default:
        return <EnvironmentSetup showPremiumOverlay={showPremiumOverlay} setActiveTab={setActiveTab} />;
    }
  };

  return (
    <div className="py-8">
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-8"
        >
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-teal-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🧪 Lab Environment Setup
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Your Data Science{" "}
            <span className="bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
              Workspace
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Set up your complete data science environment with industry-standard tools and platforms
          </p>
        </motion.div>

        {/* Tabs */}
        <div className="flex justify-center mb-8">
          <div className="inline-flex bg-white/5 backdrop-blur-lg rounded-xl p-1 border border-white/10">
            <button
              onClick={() => setActiveTab("environment")}
              data-tab="environment"
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeTab === "environment"
                  ? "bg-gradient-to-r from-teal-600 to-blue-600 text-white shadow-lg"
                  : "text-white/70 hover:text-white"
              }`}
            >
              Environment Setup
            </button>
            <button
              onClick={() => setActiveTab("notebook")}
              data-tab="notebook"
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeTab === "notebook"
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                  : "text-white/70 hover:text-white"
              }`}
            >
              Jupyter Notebook
            </button>
            <button
              onClick={() => setActiveTab("colab")}
              data-tab="colab"
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeTab === "colab"
                  ? "bg-gradient-to-r from-[#3c78d8] to-[#2a66c5] text-white shadow-lg"
                  : "text-white/70 hover:text-white"
              }`}
            >
              Google Colab
            </button>
            <button
              onClick={() => setActiveTab("local")}
              data-tab="local"
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeTab === "local"
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                  : "text-white/70 hover:text-white"
              }`}
            >
              Local Setup
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="fade-in">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

// Environment Setup Component
const EnvironmentSetup = ({ showPremiumOverlay, setActiveTab }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <>
      {/* Environment Options */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
      >
        {/* Jupyter Notebook */}
        <motion.div
          variants={itemVariants}
          whileHover={{ y: -5, scale: 1.02 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300"
        >
          <div className="text-4xl mb-4">📓</div>
          <h3 className="text-2xl font-bold text-white mb-4">Jupyter Notebook</h3>
          <p className="text-white/70 mb-6">Interactive coding environment perfect for data exploration and analysis.</p>
          
          <div className="space-y-3 mb-6">
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-teal-400 rounded-full mr-3"></div>
              Live code execution
            </div>
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-teal-400 rounded-full mr-3"></div>
              Rich output display
            </div>
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-teal-400 rounded-full mr-3"></div>
              Markdown documentation
            </div>
          </div>
          
          <button 
            onClick={() => setActiveTab("notebook")}
            className="w-full bg-gradient-to-r from-teal-600/30 to-blue-600/30 hover:from-teal-600/40 hover:to-blue-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 border border-white/10"
          >
            Launch Jupyter
          </button>
        </motion.div>

        {/* Google Colab */}
        <motion.div
          variants={itemVariants}
          whileHover={{ y: -5, scale: 1.02 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300"
        >
          <div className="text-4xl mb-4">☁️</div>
          <h3 className="text-2xl font-bold text-white mb-4">Google Colab</h3>
          <p className="text-white/70 mb-6">Cloud-based platform with free GPU access for machine learning projects.</p>
          
          <div className="space-y-3 mb-6">
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Free GPU/TPU access
            </div>
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Pre-installed libraries
            </div>
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Easy sharing & collaboration
            </div>
          </div>
          
          <button 
            onClick={() => setActiveTab("colab")}
            className="w-full bg-gradient-to-r from-green-600/30 to-teal-600/30 hover:from-green-600/40 hover:to-teal-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 border border-white/10"
          >
            Open Google Colab
          </button>
        </motion.div>

        {/* Local Setup */}
        <motion.div
          variants={itemVariants}
          whileHover={{ y: -5, scale: 1.02 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300"
        >
          <div className="text-4xl mb-4">💻</div>
          <h3 className="text-2xl font-bold text-white mb-4">Local Setup</h3>
          <p className="text-white/70 mb-6">Complete guide to set up your own data science environment locally.</p>
          
          <div className="space-y-3 mb-6">
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
              Anaconda installation
            </div>
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
              Package management
            </div>
            <div className="flex items-center text-sm text-white/60">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
              Virtual environments
            </div>
          </div>
          
          <button 
            onClick={() => setActiveTab("local")}
            className="w-full bg-gradient-to-r from-blue-600/30 to-purple-600/30 hover:from-blue-600/40 hover:to-purple-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 border border-white/10"
          >
            Open Local Setup
          </button>
        </motion.div>
      </motion.div>

      {/* Essential Libraries */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-16"
      >
        <h3 className="text-2xl font-bold text-white mb-8 flex items-center">
          <span className="text-3xl mr-3">📦</span>
          Essential Python Libraries
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          { [
            { name: "Python", icon: <SiPython className="text-[#3776AB] text-4xl" /> },
            { name: "Pandas", icon: <SiPandas className="text-[#150458] text-4xl" /> },
            { name: "NumPy", icon: <SiNumpy className="text-[#013243] text-4xl" /> },
            { name: "Matplotlib", icon: <SiMatplotlib className="text-[#11557C] text-4xl" /> },
            { name: "Seaborn", icon: <SiSeaborn className="text-[#4C72B0] text-4xl" /> },
            { name: "Scikit-learn", icon: <SiScikitlearn className="text-[#F7931E] text-4xl" /> },
            { name: "TensorFlow", icon: <SiTensorflow className="text-[#FF6F00] text-4xl" /> },
            { name: "Plotly", icon: <SiPlotly className="text-[#3F4F75] text-4xl" /> },
            { name: "Jupyter", icon: <SiJupyter className="text-[#F37626] text-4xl" /> },
            { name: "Requests", icon: <SiRequests className="text-[#000000] text-4xl" /> },
          ].map((tool, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05 }}
              className="bg-white/5 border border-white/10 rounded-xl p-4 text-center hover:bg-white/10 transition-all duration-300"
            >
              <div className="text-3xl mb-2">{tool.icon}</div>
              <h4 className="font-bold text-white mb-1">{tool.name}</h4>
            </motion.div>
          )) }
        </div>
      </motion.div>
    </>
  );
};

export default DataScienceLabEnvironmentNew;