// import mongoose from "mongoose";

// import Question from "./models/Question.js";
// import { connectDB } from "./utils/db.js";


// const questions = [
//   {
//     title: "Sum Pair Triangle",
//     description: "Form a triangle by summing adjacent pairs in each row until one value remains. Example: Input: [1,2,3] → Output: [[1,2,3],[3,5],[8]]",
//     difficulty: "Easy",
//     tags: ["Arrays"],
//   },
//   {
//     title: "Linked List Value Rotate",
//     description: "Rotate the values in a linked list (not nodes) to the right by k steps. Example: Input: 1 → 2 → 3 → 4, k=2 → Output: 3 → 4 → 1 → 2",
//     difficulty: "Easy",
//     tags: ["Linked List", "Arrays"],
//   },
//   {
//     title: "Nested String Comparator (Simplified)",
//     description: "Compare two strings and return true if they contain the same characters with same frequency. Example: Input: 'abb', 'bba' → Output: true",
//     difficulty: "Easy",
//     tags: ["Strings"],
//   },
//   {
//     title: "Set-Based Merge Reducer",
//     description: "Merge two arrays after removing their common elements. Example: Input: [1,2,3], [2,4,5] → Output: [1,3,4,5]",
//     difficulty: "Easy",
//     tags: ["Sets", "Arrays"],
//   },
//   {
//     title: "Dynamic Prime Gap Finder (Simplified)",
//     description: "From a list of numbers, filter primes and return max difference between consecutive primes. Example: Input: [2, 3, 5, 11, 17] → Output: 6",
//     difficulty: "Easy",
//     tags: ["Math"],
//   },
//   {
//     title: "Alternating Stack Sort (Simplified)",
//     description: "Push elements into a stack, and after each push, pop if the top two are in decreasing order. Return the final stack. Example: Input: [3,2,1] → Output: [3]",
//     difficulty: "Easy",
//     tags: ["Stack"],
//   },
//   {
//     title: "Matrix Spiral Sum",
//     description: "Return the sum of all elements in a matrix by reading it in spiral order. Example: Input: [[1,2],[3,4]] → Output: 10",
//     difficulty: "Easy",
//     tags: ["Matrix", "Traversal"],
//   },
//   {
//     title: "Word Merge Orderer",
//     description: "Given two words, merge them into a lexicographically sorted single string. Example: Input: 'abc', 'acd' → Output: 'aabccd'",
//     difficulty: "Easy",
//     tags: ["Strings", "Sorting"],
//   },
//   {
//     title: "Graph Cycle Labeler (Simplified)",
//     description: "Given an adjacency list of a graph, count how many nodes are part of any cycle. Example: Input: [[1],[0,2],[1,3],[2,0]] → Output: 4",
//     difficulty: "Easy",
//     tags: ["Graph", "Counting"],
//   },
//   {
//     title: "Array Stagger Balance",
//     description: "Find an index such that the sum of elements at even positions to the left equals the sum at odd positions to the right. Example: Input: [2, 4, 6, 1, 5, 3] → Output: 2",
//     difficulty: "Easy",
//     tags: ["Arrays", "Prefix Sum"],
//   },
//   {
//     title: "Universal Binary Decoder",
//     description: "Decode a compressed binary string that uses recursive encoding patterns to store variable-length info.",
//     difficulty: "Hard",
//     tags: ["Strings", "Binary Encoding"],
//   },
//   {
//     title: "Color Flip Grid Puzzle",
//     description: "Given a grid of red/blue cells, find the fewest flips to make no adjacent same-colored cells.",
//     difficulty: "Hard",
//     tags: ["Grids", "Greedy/Backtracking"],
//   },
//   {
//     title: "Stack-Queue Synchronized Sort",
//     description: "Given two structures (stack and queue), sort elements using only pops/pushes/enqueues/dequeues.",
//     difficulty: "Hard",
//     tags: ["Stack", "Queue", "Sorting Algorithms"],
//   },
//   {
//     title: "Max XOR Chain Path",
//     description: "Find a path in a graph where XOR of all node values in the path is maximized.",
//     difficulty: "Hard",
//     tags: ["Bitmasking", "Graphs"],
//   },
//   {
//     title: "Cache-Path Intersection Minimizer",
//     description: "Given multiple access paths through a network, remove minimum number of edges to make all paths disjoint.",
//     difficulty: "Hard",
//     tags: ["Graph Traversal", "Optimization"],
//   },{
//   title: "Prime Distance Graph Mapper",
//   description: "Build a graph from a list of numbers where edges exist only between nodes whose difference is a prime.",
//   difficulty: "Hard",
//   tags: ["Graphs", "Number Theory"],
// },
// {
//   title: "Recursive Time-Splitter",
//   description: "Given a total duration, recursively split time into unique sets of odd-length periods.",
//   difficulty: "Hard",
//   tags: ["Recursion", "Divide & Conquer"],
// },
// {
//   title: "Quad Trie Bit Scanner",
//   description: "Store a large number of integers in a quad-trie structure and answer range queries using bit patterns.",
//   difficulty: "Hard",
//   tags: ["Trie", "Bit Manipulation"],
// },
// {
//   title: "Minimize Non-Adjacent Product",
//   description: "From an array, pick non-adjacent elements to minimize their product. Return the minimum possible product.",
//   difficulty: "Hard",
//   tags: ["Dynamic Programming", "Optimization"],
// },
// {
//   title: "Sparse Segment Additive Search",
//   description: "Design a data structure to apply range additive updates and support fast queries for sparse indexed arrays.",
//   difficulty: "Hard",
//   tags: ["Segment Trees", "Lazy Propagation"],
// },
// {
//   title: "Prefix-Postfix Inversion Sort",
//   description: "Given array A, count how many prefix and postfix swaps required to sort it with minimal inversions.",
//   difficulty: "Hard",
//   tags: ["Sorting", "Inversions"],
// },
// {
//   title: "Integer Root Array Split",
//   description: "Split array into parts such that sum of square roots of all parts is an integer. Maximize number of parts.",
//   difficulty: "Hard",
//   tags: ["Math", "Binary Search"],
// },
// {
//   title: "Distributed Hash Leader Election",
//   description: "Simulate a distributed system where nodes elect a leader based on hash of their ID and neighborhood.",
//   difficulty: "Hard",
//   tags: ["Hashing", "Simulation", "Graphs"],
// },
// {
//   title: "Hypergraph k-Core Pruner",
//   description: "From a hypergraph, iteratively prune vertices of degree less than K. Return resulting subgraph.",
//   difficulty: "Hard",
//   tags: ["Graph Theory"],
// },
// {
//   title: "Binary Matrix Path Transformer",
//   description: "Flip minimal number of 0s to 1s to ensure at least one top-left to bottom-right path exists.",
//   difficulty: "Hard",
//   tags: ["DP", "Matrices"],
// },
// {
//   title: "Encoded Path Validator",
//   description: "Given an encoding of a tree (via DFS numbering), validate if path from root to any node is consistent.",
//   difficulty: "Hard",
//   tags: ["Trees", "Encoding"],
// },
// {
//   title: "K-Element Average Optimizer",
//   description: "Find the longest subarray of K-length chunks with increasing averages.",
//   difficulty: "Hard",
//   tags: ["Sliding Window", "Greedy"],
// },
// {
//   title: "K-Cycle Detector in Directed Graph",
//   description: "Detect if a directed graph contains a cycle of length exactly K.",
//   difficulty: "Hard",
//   tags: ["Graph Theory", "DFS"],
// },
// {
//   title: "Modulo Segment Tree",
//   description: "Design a segment tree where each node keeps modulo 10 of its segment sum. Support updates.",
//   difficulty: "Hard",
//   tags: ["Segment Tree", "Range Queries"],
// },
// {
//   title: "Palindrome Graph Paths",
//   description: "In a graph where each node holds a character, find number of simple paths that spell out a palindrome.",
//   difficulty: "Hard",
//   tags: ["Graphs", "DFS", "Strings"],
// },
// {
//   title: "Frequency Explosion Sort",
//   description: "Sort elements by frequency, but duplicate them in result proportional to square of frequency.",
//   difficulty: "Medium",
//   tags: ["Hash Map", "Sorting"],
// },
// {
//   title: "Sparse Binary Max Window",
//   description: "Find the max number of 1s in any window of size K in a sparse binary array.",
//   difficulty: "Medium",
//   tags: ["Binary", "Sliding Window"],
// },
// {
//   title: "Zigzag Count Matrix",
//   description: "Count number of zigzag paths from top-left to bottom-right where turns alternate direction.",
//   difficulty: "Medium",
//   tags: ["Matrices", "DFS"],
// },
// {
//   title: "Prefix Digit Sum Consistency",
//   description: "Given string of digits, find all substrings where digit sum is consistent across fixed length.",
//   difficulty: "Medium",
//   tags: ["Prefix Sum", "Strings"],
// },
// {
//   title: "Bipartite Index Tree Check",
//   description: "Check if tree is bipartite by assigning alternating levels to each node.",
//   difficulty: "Medium",
//   tags: ["Graphs", "Trees", "BFS"],
// },
// {
//   title: "Longest Vowel-Only Gap",
//   description: "Find longest substring made up of only vowels and bounded by consonants.",
//   difficulty: "Medium",
//   tags: ["Strings", "Sliding Window"],
// },
// {
//   title: "Circular Increment Pattern",
//   description: "Simulate circular incrementing every Kth position N times and return final array.",
//   difficulty: "Medium",
//   tags: ["Arrays", "Simulation"],
// },
// {
//   title: "Bit Toggle Explosion",
//   description: "Starting from LSB, toggle bits until a 0 is encountered. Repeat for all elements. Count total toggles.",
//   difficulty: "Medium",
//   tags: ["Bit Manipulation"],
// },
// {
//   title: "Even-Odd Stack Converter",
//   description: "Rearrange array such that all even numbers come from stack, and odds from queue in their respective LIFO/FIFO order.",
//   difficulty: "Medium",
//   tags: ["Stack", "Queues"],
// },
// {
//   title: "Diagonal Word Collector",
//   description: "Given a character matrix, collect all diagonal words (↘ direction) that form palindromes.",
//   difficulty: "Medium",
//   tags: ["Matrices", "Strings"],
// },
// {
//   title: "Parenthesis Nest Depth",
//   description: "Find max nesting depth of valid parentheses.",
//   difficulty: "Medium",
//   tags: ["Stacks", "Strings"],
// },
// {
//   title: "Prime Partition Sum",
//   description: "Check if a number can be split into primes that sum up to original number.",
//   difficulty: "Medium",
//   tags: ["Primes", "DP"],
// },
// {
//   title: "Bit Shift K Difference",
//   description: "Count numbers that differ by exactly K bits from their left-shifted version.",
//   difficulty: "Medium",
//   tags: ["Binary", "Bit Manipulation"],
// },
// {
//   title: "Magic Diagonal Matrix",
//   description: "Return true if both diagonals sum to same value in square matrix.",
//   difficulty: "Medium",
//   tags: ["Matrices"],
// },
// {
//   title: "Sum Tree Property Validator",
//   description: "Check if every node is the sum of its child nodes.",
//   difficulty: "Medium",
//   tags: ["Trees", "DFS"],
// },
// {
//   title: "Max Unique Characters Window",
//   description: "Return the length of the longest substring with all unique characters.",
//   difficulty: "Medium",
//   tags: ["Strings", "Sliding Window"],
// },
// {
//   title: "Sum of All Odd-Length Subarrays",
//   description: "Return sum of all possible odd-length subarrays.",
//   difficulty: "Medium",
//   tags: ["Arrays", "Sliding Window"],
// },
// {
//   title: "Array Rotation Distance",
//   description: "Return the rotation count needed to get from one array to another. If not possible, return -1.",
//   difficulty: "Medium",
//   tags: ["Arrays", "Math"],
// },
// {
//   title: "Index Product Zero",
//   description: "Return all pairs (i, j) where i * arr[i] == j * arr[j].",
//   difficulty: "Medium",
//   tags: ["Math", "Arrays"],
// },
// {
//   title: "Alternating Binary Flips",
//   description: "Count minimum flips to make the array alternate between 0 and 1.",
//   difficulty: "Medium",
//   tags: ["Bit Manipulation", "Arrays"],
// },
// {
//   title: "Minimum Bounce Wall Traversal",
//   description: "Given a grid of 0s and walls (1s), find the min number of directional bounces (turns) needed to go from top-left to bottom-right.",
//   difficulty: "Hard",
//   tags: ["Dynamic Programming", "Geometry"],
// },
// {
//   title: "Path Compression Swap Tree",
//   description: "Given a tree, swap values to make all union-find parent pointers optimal. Return min number of swaps.",
//   difficulty: "Hard",
//   tags: ["Union-Find", "Tree Swaps"],
// },
// {
//   title: "Segmented Heap Island",
//   description: "Given a 2D grid of elevations, find number of distinct islands where each island's internal elevations form a max-heap (parent ≥ children).",
//   difficulty: "Hard",
//   tags: ["Heap", "Union Find", "Greedy"]
// },
// {
//   title: "Bit Circular Chain Reaction",
//   description: "A circular list of integers reacts if the bitwise AND of two adjacent numbers is zero. Count how many full passes before stabilization.",
//   difficulty: "Hard",
//   tags: ["Graphs", "Simulation", "Bitmasking"]
// },
// {
//   title: "Lexicographically Minimal Inversion Sort",
//   description: "Return a lexicographically smallest permutation of the array that has the same number of inversions as the input.",
//   difficulty: "Hard",
//   tags: ["Merge Sort", "Inversion Count"]
// },
// {
//   title: "Bi-Zonal Coloring Tree",
//   description: "In a binary tree, color nodes with two colors such that no two connected nodes within the same zone (left/right subtree of root) have the same color. Return count of valid colorings.",
//   difficulty: "Hard",
//   tags: ["Trees", "DFS", "Coloring"]
// },
// {
//   title: "Prime Twin Reducer",
//   description: "Starting with N, subtract twin primes (pairs with diff 2) such that the remainder is always divisible by both. Return if you can reach 0.",
//   difficulty: "Hard",
//   tags: ["Number Theory", "Greedy"]
// },
// {
//   title: "K-Bit Jump Maze",
//   description: "Given a binary string, you can jump to any index where XOR of jump length and current position has exactly k set bits. Can you reach the end?",
//   difficulty: "Hard",
//   tags: ["DP", "Bitmasking"]
// },
// {
//   title: "Time-Shifted Subarrays",
//   description: "Given a time series array, find the longest subarray where difference between max and min is less than window length.",
//   difficulty: "Hard",
//   tags: ["Sliding Window", "Math"]
// },
// {
//   title: "Teleporting Graph Path",
//   description: "In a directed graph with teleport portals between nodes, find the shortest path from node 0 to node n-1 using at most one portal.",
//   difficulty: "Hard",
//   tags: ["Graphs", "BFS", "Portals"]
// },
// {
//   title: "Increasing Prime Gaps",
//   description: "Generate first n primes such that each subsequent gap is strictly increasing.",
//   difficulty: "Medium",
//   tags: ["Prime Sieve", "Math"]
// },
// {
//   title: "Zero Removal Jumps",
//   description: "Reduce all 0s in the array to one 0 by jumping non-zero elements over them. Count jumps.",
//   difficulty: "Medium",
//   tags: ["Greedy", "Arrays"]
// },
// {
//   title: "Parity Flip Sort",
//   description: "Sort an array where odd and even elements must alternate, keeping relative order stable.",
//   difficulty: "Medium",
//   tags: ["Sorting", "Math"]
// },
// {
//   title: "String Pair Reversal Match",
//   description: "Count unique pairs of words where one is the reverse of the other.",
//   difficulty: "Medium",
//   tags: ["Hash Map", "Strings"]
// },
// {
//   title: "Segment Sum Finder",
//   description: "Find the smallest segment whose sum is a multiple of 10.",
//   difficulty: "Medium",
//   tags: ["Prefix Sum", "Two Pointers"]
// },
// {
//   title: "Index Pairing Permutation",
//   description: "Rearrange a permutation of 2n elements so each pair (i, i+1) adds to the same value.",
//   difficulty: "Medium",
//   tags: ["Greedy", "Sorting"]
// },
// {
//   title: "Bitwise XOR Grouping",
//   description: "Group elements by XOR similarity (XOR sum divisible by 4) and return group count.",
//   difficulty: "Medium",
//   tags: ["Bit Manipulation", "Hashing"]
// },
// {
//   title: "Circular Jump Sum",
//   description: "Given an array of jump values, simulate a circular jump starting from index 0 and return true if you visit every index exactly once.",
//   difficulty: "Medium",
//   tags: ["Arrays", "Modulo Arithmetic"]
// },
// {
//   title: "Subarray Mirror Count",
//   description: "Count how many subarrays are palindromic when reversed and mirrored.",
//   difficulty: "Medium",
//   tags: ["Arrays", "Palindromes"]
// },
// {
//   title: "Inverse Index Sum",
//   description: "Given an array of unique integers, find all pairs (i, j) such that arr[i] + i == arr[j] + j and i != j.",
//   difficulty: "Medium",
//   tags: ["Hash Map", "Indexing"]
// },
// {
//   title: "Letter Height Pyramid",
//   description: "Given a string, return a list of strings in pyramid form, each row adding one more character.",
//   difficulty: "Easy",
//   tags: ["Strings", "Pattern Printing"]
// },
// {
//   title: "Modulo Match Pairs",
//   description: "Count pairs of elements where their sum is divisible by 3.",
//   difficulty: "Easy",
//   tags: ["Hash Map", "Modulo"]
// },
// {
//   title: "Consecutive Group Split",
//   description: "Count how many consecutive increasing groups exist.",
//   difficulty: "Easy",
//   tags: ["Arrays"]
// },
// {
//   title: "Shifted Binary Counter",
//   description: "Count the number of 1s in binary representation of all numbers in array after left-shifting them by 1.",
//   difficulty: "Easy",
//   tags: ["Binary", "Bit Manipulation"]
// },
// {
//   title: "Array Bubble Injector",
//   description: "Insert the average of every adjacent pair into a new array.",
//   difficulty: "Easy",
//   tags: ["Arrays"]
// },
// {
//   title: "Minimal Char Distance",
//   description: "Given a string, return the minimum distance between any two same characters.",
//   difficulty: "Easy",
//   tags: ["Strings", "Math"]
// },
// {
//   title: "Vowel Sandwich",
//   description: "Count how many substrings of length 3 contain a vowel between two consonants.",
//   difficulty: "Easy",
//   tags: ["Strings"]
// },
// {
//   title: "Digit Alternator",
//   description: "Return true if digits of a number alternate between even and odd.",
//   difficulty: "Easy",
//   tags: ["Math", "Conditionals"]
// },
// {
//   title: "Reverse Index Joiner",
//   description: "Reverse the list, then return a new list of (index + reversed value).",
//   difficulty: "Easy",
//   tags: ["Arrays", "Indexing"]
// },
// {
//   title: "Mirror Alphabet Sort",
//   description: "Given a lowercase string, sort it by how far each character is from 'z' (i.e., 'z' is closest, 'a' is farthest).",
//   difficulty: "Easy",
//   tags: ["Strings", "Sorting"]
// }
// ];



// const seedData = async () => {
//   try {

//     connectDB();
//     // Remove existing questions
//     await Question.deleteMany();
//     console.log("Existing questions deleted");

//     // Insert new questions
//     await Question.insertMany(questions);
//     console.log("Questions added successfully!");

//     mongoose.connection.close(); // Close connection
//   } catch (error) {
//     console.error("unsuccess");
//     mongoose.connection.close();
//   }
// };

// seedData();