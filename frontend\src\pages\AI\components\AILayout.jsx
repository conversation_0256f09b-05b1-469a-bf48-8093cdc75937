import React from 'react';
import { Footer, CourseLayout } from '../../../components/layout';

const AILayout = ({ children, isSidebarOpen, toggleSidebar }) => {
  return (
    <CourseLayout 
      isSidebarOpen={isSidebarOpen} 
      toggleSidebar={toggleSidebar}
      title="Artificial Intelligence"
    >
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes slideInRight {
          from { transform: translateX(50px); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes fadeUpContent {
          from { transform: translateY(10px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
        .content-animate {
          animation: slideInRight 0.3s ease-out forwards;
        }
        .content-fade-up {
          animation: fadeUpContent 0.3s ease-out forwards;
        }
      `}} />
      
      {/* Main Content */}
      <main className="flex-1 relative z-10 content-fade-up">
        {children}
      </main>
      
      {/* Footer */}
      <Footer />
    </CourseLayout>
  );
};

export default AILayout;
