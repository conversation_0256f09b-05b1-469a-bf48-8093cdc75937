import { motion } from "framer-motion";

const DifficultySection = ({ dsaData }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  const getDifficultyColors = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case "easy":
        return {
          bg: "bg-green-100",
          text: "text-green-800",
          border: "border-green-200",
          icon: "text-green-600",
        };
      case "medium":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-800",
          border: "border-yellow-200",
          icon: "text-yellow-600",
        };
      case "hard":
        return {
          bg: "bg-red-100",
          text: "text-red-800",
          border: "border-red-200",
          icon: "text-red-600",
        };
      default:
        return {
          bg: "bg-gray-100",
          text: "text-gray-800",
          border: "border-gray-200",
          icon: "text-gray-600",
        };
    }
  };

  return (
    <section className="py-16 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="space-y-12"
        >
          {dsaData.map((section, index) => {
            const colors = getDifficultyColors(section.difficulty);
            
            return (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-gray-50 rounded-2xl p-8"
              >
                <div className="flex items-center mb-6">
                  <i className={`${section.icon} text-3xl ${colors.icon} mr-4`}></i>
                  <h2 className="text-2xl font-bold text-gray-900 mr-4">
                    {section.level}
                  </h2>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${colors.bg} ${colors.text} ${colors.border} border`}>
                    {section.difficulty}
                  </span>
                </div>
                
                <div className="grid md:grid-cols-2 gap-6">
                  {section.questions.map((question, idx) => (
                    <motion.div
                      key={idx}
                      whileHover={{ y: -3 }}
                      className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300"
                    >
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        {question.title}
                      </h3>
                      
                      <div className="flex flex-wrap gap-2 mb-4">
                        {question.tags.map((tag, tIndex) => (
                          <span
                            key={tIndex}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-md"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      
                      <ul className="space-y-2">
                        {question.keyPoints.map((point, pIndex) => (
                          <li key={pIndex} className="flex items-start">
                            <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-gray-700 text-sm">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
};

export default DifficultySection;
