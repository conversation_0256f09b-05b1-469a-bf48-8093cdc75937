import React from 'react';
import { motion } from 'framer-motion';

const Stat = ({ number, label, icon, gradient }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5, delay: 0.5 }}
    className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-white/20 transition-colors"
  >
    <div className="text-3xl mb-2">{icon}</div>
    <div className={`text-3xl font-bold bg-gradient-to-r ${gradient} bg-clip-text text-transparent mb-2`}>
      {number}
    </div>
    <div className="text-gray-300 text-sm">{label}</div>
  </motion.div>
);

const HeroStats = ({ itemVariants }) => (
  <motion.div
    variants={itemVariants}
    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-16"
  >
    <Stat
      number="100+"
      label="Interview Questions"
      icon="🎯"
      gradient="from-blue-500 to-blue-600"
    />
    <Stat
      number="250h+"
      label="Training Content"
      icon="⚡"
      gradient="from-indigo-500 to-indigo-600"
    />
    <Stat
      number="10K+"
      label="Active Learners"
      icon="👨‍💻"
      gradient="from-purple-500 to-purple-600"
    />
    <Stat
      number="95%"
      label="Job Placement"
      icon="🎯"
      gradient="from-violet-500 to-violet-600"
    />
  </motion.div>
);

export default HeroStats;
