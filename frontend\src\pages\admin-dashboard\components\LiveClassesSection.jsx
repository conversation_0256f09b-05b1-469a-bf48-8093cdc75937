import React, { useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaCalendarAlt, FaClock, FaToggleOn, FaToggleOff, FaUsers, FaTimes, FaUserCheck } from 'react-icons/fa';

// Mock instructors data
const instructors = [
  { id: 1, name: '<PERSON>' },
  { id: 2, name: '<PERSON>' },
  { id: 3, name: '<PERSON>' },
  { id: 4, name: '<PERSON>' },
  { id: 5, name: '<PERSON>' }
];

// Mock data for live classes with enhanced properties
const initialLiveClasses = [
  {
    id: 1,
    title: 'Python Programming Basics',
    instructorId: 1,
    dateTime: '2023-12-15T14:00',
    published: true,
    registered: 45,
    attended: 38,
    status: 'upcoming'
  },
  {
    id: 2,
    title: 'React Hooks Deep Dive',
    instructorId: 2,
    dateTime: '2023-12-18T16:30',
    published: true,
    registered: 62,
    attended: 0,
    status: 'upcoming'
  },
  {
    id: 3,
    title: 'Data Visualization with D3.js',
    instructorId: 3,
    dateTime: '2023-11-20T10:00',
    published: true,
    registered: 53,
    attended: 41,
    status: 'completed'
  },
  {
    id: 4,
    title: 'Advanced JavaScript Patterns',
    instructorId: 2,
    dateTime: '2023-11-10T15:00',
    published: false,
    registered: 35,
    attended: 28,
    status: 'completed'
  }
];

const LiveClassesSection = () => {
  const [liveClasses, setLiveClasses] = useState(initialLiveClasses);
  const [showModal, setShowModal] = useState(false);
  const [editingClass, setEditingClass] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    instructorId: '',
    dateTime: '',
    published: true
  });
  const [activeTab, setActiveTab] = useState('upcoming');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const openModal = (liveClass = null) => {
    if (liveClass) {
      // Edit mode
      setFormData({
        title: liveClass.title,
        instructorId: liveClass.instructorId,
        dateTime: liveClass.dateTime,
        published: liveClass.published
      });
      setEditingClass(liveClass);
    } else {
      // Add mode
      setFormData({
        title: '',
        instructorId: '',
        dateTime: '',
        published: true
      });
      setEditingClass(null);
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingClass(null);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const now = new Date();
    const classDate = new Date(formData.dateTime);
    const status = classDate > now ? 'upcoming' : 'completed';
    
    if (editingClass) {
      // Update existing class
      setLiveClasses(liveClasses.map(liveClass => 
        liveClass.id === editingClass.id 
          ? { 
              ...liveClass, 
              ...formData,
              status: liveClass.status // Preserve original status
            } 
          : liveClass
      ));
    } else {
      // Add new class
      const newLiveClass = {
        id: liveClasses.length + 1,
        ...formData,
        registered: 0,
        attended: 0,
        status
      };
      setLiveClasses([...liveClasses, newLiveClass]);
    }
    
    closeModal();
  };

  const togglePublishStatus = (classId) => {
    setLiveClasses(liveClasses.map(liveClass => 
      liveClass.id === classId 
        ? { ...liveClass, published: !liveClass.published } 
        : liveClass
    ));
  };

  const deleteLiveClass = (classId) => {
    setLiveClasses(liveClasses.filter(liveClass => liveClass.id !== classId));
    setShowDeleteConfirm(null);
  };

  // Format date and time for display
  const formatDateTime = (dateTimeStr) => {
    const dateTime = new Date(dateTimeStr);
    return {
      date: dateTime.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
      time: dateTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getInstructorName = (instructorId) => {
    return instructors.find(instructor => instructor.id === instructorId)?.name || 'Unknown';
  };

  const filteredClasses = liveClasses.filter(liveClass => 
    activeTab === 'all' || liveClass.status === activeTab
  );

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Live Classes Management</h2>
        <button
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          onClick={() => openModal()}
        >
          <FaPlus className="mr-2" />
          Schedule New Class
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'all' 
              ? 'text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('all')}
        >
          All Classes
        </button>
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'upcoming' 
              ? 'text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('upcoming')}
        >
          Upcoming
        </button>
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'completed' 
              ? 'text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('completed')}
        >
          Past Classes
        </button>
      </div>

      {/* Live Classes Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Instructor</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredClasses.map((liveClass) => {
              const { date, time } = formatDateTime(liveClass.dateTime);
              return (
                <tr key={liveClass.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{liveClass.title}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{getInstructorName(liveClass.instructorId)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <div className="flex items-center text-sm text-gray-900">
                        <FaCalendarAlt className="mr-1 text-blue-500" />
                        {date}
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <FaClock className="mr-1 text-blue-500" />
                        {time}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <button 
                        onClick={() => togglePublishStatus(liveClass.id)}
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mb-1 ${
                          liveClass.published 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {liveClass.published ? <FaToggleOn className="mr-1" /> : <FaToggleOff className="mr-1" />}
                        {liveClass.published ? 'Visible' : 'Hidden'}
                      </button>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        liveClass.status === 'upcoming' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-purple-100 text-purple-800'
                      }`}>
                        {liveClass.status === 'upcoming' ? 'Upcoming' : 'Completed'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex items-center text-sm text-gray-900 mr-3" title="Registered">
                        <FaUsers className="mr-1 text-blue-500" />
                        {liveClass.registered}
                      </div>
                      {liveClass.status === 'completed' && (
                        <div className="flex items-center text-sm text-gray-900" title="Attended">
                          <FaUserCheck className="mr-1 text-green-500" />
                          {liveClass.attended}
                        </div>
                      )}
                    </div>
                    {liveClass.status === 'completed' && liveClass.registered > 0 && (
                      <div className="text-xs text-gray-500 mt-1">
                        {Math.round((liveClass.attended / liveClass.registered) * 100)}% attendance
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                      onClick={() => openModal(liveClass)} 
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      <FaEdit />
                    </button>
                    <button 
                      onClick={() => setShowDeleteConfirm(liveClass.id)} 
                      className="text-red-600 hover:text-red-900"
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Live Class Form Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800">
                {editingClass ? 'Edit Live Class' : 'Schedule New Live Class'}
              </h3>
              <button 
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="title">
                  Class Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="instructorId">
                  Instructor
                </label>
                <select
                  id="instructorId"
                  name="instructorId"
                  value={formData.instructorId}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                >
                  <option value="">Select an instructor</option>
                  {instructors.map(instructor => (
                    <option key={instructor.id} value={instructor.id}>
                      {instructor.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="dateTime">
                  Date & Time
                </label>
                <input
                  type="datetime-local"
                  id="dateTime"
                  name="dateTime"
                  value={formData.dateTime}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              
              <div className="mb-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="published"
                    name="published"
                    checked={formData.published}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <label htmlFor="published" className="text-sm font-medium text-gray-700">
                    Make this class visible to students
                  </label>
                </div>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  className="bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2"
                  onClick={closeModal}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md"
                >
                  {editingClass ? 'Update Class' : 'Schedule Class'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this live class? This action cannot be undone.
            </p>
            <div className="flex justify-end">
              <button
                className="bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2"
                onClick={() => setShowDeleteConfirm(null)}
              >
                Cancel
              </button>
              <button
                className="bg-red-600 text-white px-4 py-2 rounded-md"
                onClick={() => deleteLiveClass(showDeleteConfirm)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveClassesSection;